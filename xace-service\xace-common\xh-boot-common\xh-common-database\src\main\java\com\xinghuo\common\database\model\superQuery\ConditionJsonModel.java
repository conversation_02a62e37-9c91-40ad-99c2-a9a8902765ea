package com.xinghuo.common.database.model.superQuery;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.xinghuo.common.util.json.JsonObjectToStringDeserializer;
import lombok.Data;

/**
 * 高级查询
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
public class ConditionJsonModel {
	private String field;
	private String fieldValue;
	private String symbol;
	private String tableName;
	private String xhKey;
	private String defaultValue;
	@JsonDeserialize(using= JsonObjectToStringDeserializer.class)
	private String attr;
	/**
	 * 表单字段是否多选
	 */
	private boolean formMultiple;
}
