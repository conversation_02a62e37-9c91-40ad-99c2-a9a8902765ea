package com.xinghuo.common.enums;

/**
 * 数据库驱动枚举类
 *
  * <AUTHOR>
 * @date 2023-10-05
 */
public enum  DbDriverEnum {
    MYSQL("com.mysql.cj.jdbc.Driver"),
    ORACLE("oracle.jdbc.OracleDriver"),
    SQLSERVER("com.microsoft.sqlserver.jdbc.SQLServerDriver"),
    DM("dm.jdbc.driver.DmDriver"),
    POSTGRE_SQL("org.postgresql.Driver"),
    KINGBASE_ES("com.kingbase8.Driver");


    private String dbDriver;

    DbDriverEnum(String dbDriver) {
        this.dbDriver = dbDriver;
    }

    public String getDbDriver() {
        return dbDriver;
    }

    public void setDbDriver(String dbDriver) {
        this.dbDriver = dbDriver;
    }
}
