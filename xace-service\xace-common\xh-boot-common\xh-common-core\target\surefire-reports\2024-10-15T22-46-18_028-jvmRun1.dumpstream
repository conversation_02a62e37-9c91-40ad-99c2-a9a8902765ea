# Created at 2024-10-15T22:46:18.983
Corrupted channel by directly writing to native stream in forked JVM 1. Stream '#'.

# Created at 2024-10-15T22:46:18.985
Corrupted channel by directly writing to native stream in forked JVM 1. Stream '# There is insufficient memory for the Java Runtime Environment to continue.'.

# Created at 2024-10-15T22:46:18.987
Corrupted channel by directly writing to native stream in forked JVM 1. Stream '# Native memory allocation (mmap) failed to map 268435456 bytes for G1 virtual space'.

# Created at 2024-10-15T22:46:18.988
Corrupted channel by directly writing to native stream in forked JVM 1. Stream '# An error report file with more information is saved as:'.

# Created at 2024-10-15T22:46:19.051
Corrupted channel by directly writing to native stream in forked JVM 1. Stream '# G:\v2\pd-xace-v2\xace-service\xace-common\xh-boot-common\xh-common-core\hs_err_pid34368.log'.

