package com.xinghuo.common.config;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.nacos.api.exception.NacosException;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import org.apache.shardingsphere.driver.api.yaml.YamlShardingSphereDataSourceFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;

/**
 * Apache ShardingSphere 是一个开源的分布式数据库解决方案，由两个主要的子项目组成：ShardingSphere-JDBC 和 ShardingSphere-Proxy。
 * 它们提供了数据分片（Sharding）、读写分离（Read/Write Splitting）、分布式事务和数据库治理等功能。
 * ShardingSphere 旨在解决在云原生和微服务架构中的数据库水平扩展问题，以及提升大规模数据处理的效率。
 * <AUTHOR>
 * @date 2023-10-05
 */
@Configuration
@ConditionalOnProperty(prefix = "config", name = "sharding-sphere-enabled", havingValue = "true")
public class ShardingSphereAutoConfig {

    public ShardingSphereAutoConfig() {
        System.out.println("启用ShardingSphere");
    }

    public static final String PREFIX = "shardingsphere";

    @Bean
    public Object initShardingSphereDataSource(@Qualifier("dataSourceSystem") DynamicRoutingDataSource dataSource, NacosConfigManager nacosConfigManager) throws SQLException, IOException, NacosException {
        String shardingContent = nacosConfigManager.getConfigService().getConfig("sharding-sphere.yaml", "DEFAULT_GROUP", 3000L);
        if(shardingContent != null) {
            DataSource SSDataSource = YamlShardingSphereDataSourceFactory.createDataSource(shardingContent.getBytes(StandardCharsets.UTF_8));
            dataSource.addDataSource(PREFIX, SSDataSource);
        }else{
            System.out.println("ShardingSphere加载失败, 缺少sharding-sphere.yaml配置文件");
        }
        return null;
    }

}
