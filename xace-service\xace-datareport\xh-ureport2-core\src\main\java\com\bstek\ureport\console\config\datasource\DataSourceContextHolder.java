package com.bstek.ureport.console.config.datasource;

/**
 * 数据库上下文切换
 *
 */
public class DataSourceContextHolder {

    private static final ThreadLocal<String> contextDbNameHolder = new ThreadLocal<>();

    private static final ThreadLocal<String> contextDbIdHolder = new ThreadLocal<>();

    //设置当前数据库
    public static void setDatasource(String dbId,String dbName) {
        contextDbNameHolder.set(dbName);
        contextDbIdHolder.set(dbId);
    }

    //取得当前数据源Id
    public static String getDatasourceId() {
        String str = contextDbIdHolder.get();
        return str;
    }
    //取得当前数据源名称
    public static String getDatasourceName() {
        String str = contextDbNameHolder.get();
        return str;
    }

    //清除上下文数据
    public static void clearDatasourceType() {
        contextDbNameHolder.remove();
        contextDbIdHolder.remove();
    }
}
