> 本项目为`xace-java-cloud`的基础依赖，可上传到私服或使用本地导入的方式引用该项目

## 一 结构说明
```bash
xace-common
    ├── xh-common-dependencies - 项目设计所有所有依赖版本
    ├── xh-common-boot - 单体版本涉及依赖
    ├         ├── xh-common-connector - 单点数据推送模块
    ├         ├── xh-common-file - 文件工具类模块
    ├         ├── xh-common-message - 短信模块
    ├         ├── xh-common-office - office操作模块
    ├         ├── xh-common-scheduletask - 调度工具模块
    ├         ├── xh-common-auth - 认证模块
    ├         ├── xh-common-core - 基础类及常用工具
    ├         ├── xh-common-database - 数据库配置及多数据库兼容
    ├         ├── xh-common-redis - 缓存工具Redis组件配置
    ├         ├── xh-common-security - 接口鉴权配置
    ├         └── xh-common-swagger - API组件Swagger配置

    ├── xace-common-cloud - 微服务版本额外依赖
    ├         ├── xh-common-dubbo - dubbo拦截器, 自动封装认证信息
    ├         ├── xh-common-feign - 远程调用Feign组件配置
    ├         └── xh-common-seata - seata依赖
```
## 二 使用方式
### 2.1 私服发布

> 需要Maven私服仓库，若相同版本重新发布，需要先登录私服把`maven-releases`库中的`com.xinghuo`目录删除

#### 2.1.1 修改Maven配置
 修改Maven文件下conf文件夹中的`setttings.xml`，在`<servers></servers>`标签中增加`<server></server>`，示例:
```xml
  <server>
    <id>maven-releases</id>
    <username>xh-user（账号，结合私服配置设置）</username>
    <password>123456（密码，结合私服配置设置）</password>
  </server>
  <server>
    <id>maven-snapshots</id>
    <username>xh-user（账号，结合私服配置设置）</username>
    <password>123456（密码，结合私服配置设置）</password>
  </server>
```
 修改`xace-common`项目根目录下的`pom.xml`文件，增加maven配置
```xml
  <repository>
    <id>maven-releases(与server-id一致)</id>
    <name>maven-releases</name>
    <url>http://ip:port/repository/maven-releases/(仓库地址)</url>
  </repository>
```
#### 2.1.2 发布到私服

  打开`xace-common`项目, 运行`deploy`插件，刷新Maven配置即可

### 2.2 本地安装

  打开`xace-common`项目, 运行`install`插件，将`xace-common`中的包安装至本地

### 2.3 本地导入

  点击`File`>`Project Structure..`>`module`，点击右边的+号，点击`import module`，找到`xace-common`项目后点击ok,点击apply，ok,刷新Maven即可

## 三 更新版本号

### 进入xh-dependencies目录， 执行命令

```
# mvn versions:set -DnewVersion=3.4.9-RELEASE
mvn versions:set -DnewVersion=版本号
```
