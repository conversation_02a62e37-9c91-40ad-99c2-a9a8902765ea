package com.xinghuo.common.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * 登录异常
 * 用于表示在登录过程中发生的异常，继承自 Exception 类。
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class LoginException extends RuntimeException {

    @Getter
    @Setter
    private Object data;
    /**
     * 构造函数，传入异常信息
     *
     * @param message 异常信息
     */
    public LoginException(String message) {
        super(message);
    }

    public LoginException(String message, Object data) {
        super(message);
        this.data = data;
    }
}
