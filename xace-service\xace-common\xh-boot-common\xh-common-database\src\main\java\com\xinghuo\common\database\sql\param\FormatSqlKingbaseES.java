package com.xinghuo.common.database.sql.param;

import com.xinghuo.common.database.constant.DbFieldConst;
import com.xinghuo.common.database.model.dbfield.JdbcColumnModel;
import com.xinghuo.common.database.source.AbstractDbBase;

import java.util.Map;

/**
 * 类功能
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@SuppressWarnings("AlibabaClassNamingShouldBeCamel")
public class FormatSqlKingbaseES {

    /**
     * 非空时空串报错，因Oracle空串存储为NULL，用一个空格代替空串
     */
    public static void nullValue(String dbEncode, JdbcColumnModel model, Map<String, Object> map){
        if(AbstractDbBase.KINGBASE_ES.equals(dbEncode)){
            // 字符串类型 && 字符串不为空 && 空串
            if(model.getValue() instanceof String && model.getNullSign().equals(DbFieldConst.NOT_NULL)
                    && model.getValue().toString().equals("")){
                map.put(model.getField(), " ");
            }
        }
    }

}
