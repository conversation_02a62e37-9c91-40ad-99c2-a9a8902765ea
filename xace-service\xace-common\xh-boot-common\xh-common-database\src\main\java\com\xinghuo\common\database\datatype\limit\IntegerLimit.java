package com.xinghuo.common.database.datatype.limit;

import com.xinghuo.common.database.datatype.db.DtMySQLEnum;
import com.xinghuo.common.database.datatype.db.interfaces.AbstractDtLimitBase;
import com.xinghuo.common.database.datatype.db.interfaces.DtInterface;
import com.xinghuo.common.database.datatype.model.DtModel;
import com.xinghuo.common.database.datatype.model.DtModelDTO;
import com.xinghuo.common.database.datatype.sync.util.DtSyncUtil;
import com.xinghuo.common.database.source.AbstractDbBase;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 整型数据类型
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
@NoArgsConstructor
public class IntegerLimit extends AbstractDtLimitBase {

    public final static String CATEGORY = "type-Integer";
    public final static String JAVA_TYPE = "int";

    @Override
    public String initDtCategory() {
        return CATEGORY;
    }

    @Override
    public DtModel convert(DtModelDTO viewDtModel){
        DtInterface targetDtEnum = viewDtModel.getConvertTargetDtEnum();
        DtModel toModel = new DtModel(targetDtEnum);
        // 当转换成Oracle的数字类型
        if(targetDtEnum.getDtCategory().equals(NumberLimit.CATEGORY)){
            try{
                // 先当前数据库转成DtMySQL枚举
                DtMySQLEnum dtEnum = (DtMySQLEnum) DtSyncUtil.getToFixCovert(targetDtEnum, AbstractDbBase.MYSQL);
                // 在进行转换对比
                switch (dtEnum){
                    case TINY_INT:
                        toModel.setNumPrecision(3);
                        break;
                    case SMALL_INT:
                        toModel.setNumPrecision(5);
                        break;
                    case MEDIUM_INT:
                        toModel.setNumPrecision(7);
                        break;
                    case INT:
                        toModel.setNumPrecision(10);
                        break;
                    case BIGINT:
                        toModel.setNumPrecision(19);
                        break;
                    default:
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        if(this.isModifyFlag){
            toModel.setFormatLengthStr("");
        }
        return toModel;
    }

}
