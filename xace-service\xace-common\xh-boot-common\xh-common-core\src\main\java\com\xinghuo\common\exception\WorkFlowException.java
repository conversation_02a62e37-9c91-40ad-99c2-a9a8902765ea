package com.xinghuo.common.exception;

import lombok.Getter;

/**
 * 工作流异常
 * 用于表示在工作流程中发生的异常，继承自 Exception 类。
 *
 * @date 2023-10-05
 * <AUTHOR>
 */
@Getter
public class WorkFlowException extends Exception {

    /**
     * 异常状态码，默认为 400
     * -- GETTER --
     *  获取异常状态码
     *
     * @return 异常状态码

     */
    private Integer code = 400;

    /**
     * 构造函数，传入异常状态码和信息
     *
     * @param code    异常状态码
     * @param message 异常信息
     */
    public WorkFlowException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    /**
     * 构造函数，传入异常信息，默认状态码为 400
     *
     * @param message 异常信息
     */
    public WorkFlowException(String message) {
        super(message);
    }

}
