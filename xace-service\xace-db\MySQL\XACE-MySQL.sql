SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for base_advancedqueryscheme
-- ----------------------------
DROP TABLE IF EXISTS `base_advancedqueryscheme`;
CREATE TABLE `base_advancedqueryscheme`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_FullName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '方案名称',
  `F_MatchLogic` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '匹配逻辑',
  `F_ConditionJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '条件规则Json',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_ModuleId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '高级查询方案' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_appdata
-- ----------------------------
DROP TABLE IF EXISTS `base_appdata`;
CREATE TABLE `base_appdata`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_ObjectType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对象类型',
  `F_ObjectId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对象主键',
  `F_ObjectData` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '对象json',
  `F_Description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_SystemId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统id',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'app常用数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_authorize
-- ----------------------------
DROP TABLE IF EXISTS `base_authorize`;
CREATE TABLE `base_authorize`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ItemType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目类型',
  `F_ItemId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目主键',
  `F_ObjectType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对象类型',
  `F_ObjectId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对象主键',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '操作权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_billrule
-- ----------------------------
DROP TABLE IF EXISTS `base_billrule`;
CREATE TABLE `base_billrule`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单据名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单据编号',
  `F_Prefix` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单据前缀',
  `F_DateFormat` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日期格式',
  `F_Digit` int(11) NULL DEFAULT NULL COMMENT '流水位数',
  `F_StartNumber` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流水起始',
  `F_Example` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流水范例',
  `F_ThisNumber` int(11) NULL DEFAULT NULL COMMENT '当前流水号',
  `F_OutputNumber` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '输出流水号',
  `F_Description` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_Category` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务分类',
  `F_CategoryId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务分类id',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '单据规则' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_columnspurview
-- ----------------------------
DROP TABLE IF EXISTS `base_columnspurview`;
CREATE TABLE `base_columnspurview`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键Id',
  `F_FieldList` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '列表字段数组',
  `F_ModuleId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模块ID',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '模块列表权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_comfields
-- ----------------------------
DROP TABLE IF EXISTS `base_comfields`;
CREATE TABLE `base_comfields`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述说明',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_FieldName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '列说名',
  `F_DataType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `F_DataLength` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '长度',
  `F_AllowNull` int(11) NULL DEFAULT NULL COMMENT '是否为空（1允许，0不允许）',
  `F_Field` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '列名',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '常用表字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_commonwords
-- ----------------------------
DROP TABLE IF EXISTS `base_commonwords`;
CREATE TABLE `base_commonwords`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_SystemIds` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '应用id',
  `F_SystemNames` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '应用名称',
  `F_CommonWordsText` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '常用语',
  `F_CommonWordsType` int(11) NULL DEFAULT NULL COMMENT '常用语类型(0:系统,1:个人)',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '常用语' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_datainterface
-- ----------------------------
DROP TABLE IF EXISTS `base_datainterface`;
CREATE TABLE `base_datainterface`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键ID',
  `F_CategoryId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分组ID',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '接口名称',
  `F_Path` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `F_RequestMethod` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '请求方式',
  `F_ResponseType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '返回类型',
  `F_Query` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '查询语句',
  `F_RequestParameters` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '请求参数JSON',
  `F_IpAddress` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接口编码',
  `F_SortCode` bigint(20) NOT NULL COMMENT '排序码(默认0)',
  `F_EnabledMark` int(11) NOT NULL COMMENT '状态(0-默认，禁用，1-启用)',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述或说明',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建用户id',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户id',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志(默认0)',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户id',
  `F_DbLinkId` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '数据源id',
  `F_DataType` int(11) NULL DEFAULT NULL COMMENT '数据类型(1-动态数据SQL查询，2-静态数据)',
  `F_CheckType` int(11) NULL DEFAULT NULL COMMENT '验证类型',
  `F_RequestHeaders` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `F_DataProcessing` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '数据处理',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据接口' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_datainterfacelog
-- ----------------------------
DROP TABLE IF EXISTS `base_datainterfacelog`;
CREATE TABLE `base_datainterfacelog`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `F_InvokId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用接口id',
  `F_InvokTime` datetime NULL DEFAULT NULL COMMENT '调用时间',
  `F_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调用者id',
  `F_InvokIp` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求ip',
  `F_InvokDevice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求设备',
  `F_InvokType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求类型',
  `F_InvokWasteTime` int(11) NULL DEFAULT NULL COMMENT '请求耗时',
  `F_OauthAppId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据接口调用日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_dbbackup
-- ----------------------------
DROP TABLE IF EXISTS `base_dbbackup`;
CREATE TABLE `base_dbbackup`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_BackupDbName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备份库名',
  `F_BackupTime` datetime NULL DEFAULT NULL COMMENT '备份时间',
  `F_FileName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件名称',
  `F_FileSize` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件大小',
  `F_FilePath` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '文件路径',
  `F_Description` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据备份' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_dblink
-- ----------------------------
DROP TABLE IF EXISTS `base_dblink`;
CREATE TABLE `base_dblink`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '连接名称',
  `F_DbType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '连接驱动',
  `F_Host` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主机地址',
  `F_Port` int(11) NULL DEFAULT NULL COMMENT '端口',
  `F_UserName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户',
  `F_Password` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
  `F_ServiceName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '服务名称',
  `F_Description` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_DbSchema` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模式',
  `F_TableSpace` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表空间',
  `F_OracleParam` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'oracle连接参数',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_Oracle_Extend` tinyint(4) NULL DEFAULT NULL COMMENT 'Oracle扩展开关 1:开启 0:关闭',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据连接' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_dictionarydata
-- ----------------------------
DROP TABLE IF EXISTS `base_dictionarydata`;
CREATE TABLE `base_dictionarydata`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编号',
  `F_SimpleSpelling` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '拼音',
  `F_IsDefault` int(11) NULL DEFAULT NULL COMMENT '默认',
  `F_Description` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_DictionaryTypeId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类别主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '字典数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_dictionarytype
-- ----------------------------
DROP TABLE IF EXISTS `base_dictionarytype`;
CREATE TABLE `base_dictionarytype`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编号',
  `F_IsTree` int(11) NULL DEFAULT NULL COMMENT '树形',
  `F_Description` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '字典分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_file
-- ----------------------------
DROP TABLE IF EXISTS `base_file`;
CREATE TABLE `base_file`  (
  `F_Id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
  `F_FileVersion` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '文件版本',
  `F_FileName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '文件名',
  `F_Type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '上传方式: 1.local 本地上传  2.http http上传  3.create 新建',
  `F_Url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'http上传的url',
  `F_OldFileVersionId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '历史版本id',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文件在线编辑' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_filter
-- ----------------------------
DROP TABLE IF EXISTS `base_filter`;
CREATE TABLE `base_filter`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `F_ModuleId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '在线和代码生成记录主键',
  `F_Config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '过滤配置',
  `F_CreateTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_UpdateTime` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_ConfigApp` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '过滤配置app',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '过滤配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_group
-- ----------------------------
DROP TABLE IF EXISTS `base_group`;
CREATE TABLE `base_group`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_FullName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  `F_Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分组管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_imcontent
-- ----------------------------
DROP TABLE IF EXISTS `base_imcontent`;
CREATE TABLE `base_imcontent`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_SendUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送者',
  `F_SendTime` datetime NULL DEFAULT NULL COMMENT '发送时间',
  `F_ReceiveUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接收者',
  `F_ReceiveTime` datetime NULL DEFAULT NULL COMMENT '接收时间',
  `F_Content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '内容',
  `F_ContentType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '内容',
  `F_State` int(11) NULL DEFAULT NULL COMMENT '状态',
  `F_SENDDELETEMARK` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接收者删除标记',
  `F_DELETEMARK` int(20) NULL DEFAULT NULL COMMENT '删除标记',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '聊天内容' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_imreply
-- ----------------------------
DROP TABLE IF EXISTS `base_imreply`;
CREATE TABLE `base_imreply`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户主键',
  `F_ReceiveUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收用户',
  `F_ReceiveTime` datetime NULL DEFAULT NULL COMMENT '接收时间',
  `F_ImreplySendDeleteMark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收者删除标记',
  `F_ImreplyDeleteMark` int(20) NULL DEFAULT NULL COMMENT '删除标记',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '聊天会话' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_interfaceoauth
-- ----------------------------
DROP TABLE IF EXISTS `base_interfaceoauth`;
CREATE TABLE `base_interfaceoauth`  (
  `F_Id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_AppId` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用id',
  `F_AppName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用名称',
  `F_AppSecret` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用秘钥',
  `F_VerifySignature` int(16) NULL DEFAULT NULL COMMENT '验证签名',
  `F_UsefulLife` datetime NULL DEFAULT NULL COMMENT '使用期限',
  `F_WhiteList` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '白名单',
  `F_BlackList` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '黑名单',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后修改人',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '最后修改时间',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '状态',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '说明',
  `F_DataInterfaceIds` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '接口列表',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标记',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '接口认证' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message
-- ----------------------------
DROP TABLE IF EXISTS `base_message`;
CREATE TABLE `base_message`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_Type` int(11) NULL DEFAULT NULL COMMENT '类别',
  `F_Title` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标题',
  `F_BodyText` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '正文',
  `F_PriorityLevel` int(11) NULL DEFAULT NULL COMMENT '优先',
  `F_ToUserIds` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '收件用户',
  `F_IsRead` int(11) NULL DEFAULT NULL COMMENT '是否阅读',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_Files` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '附件',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_FlowType` int(11) NULL DEFAULT NULL COMMENT '流程类型',
  `F_CoverImage` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '封面图片',
  `F_ExpirationTime` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `F_Category` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类 1-公告 2-通知',
  `F_RemindCategory` int(11) NULL DEFAULT NULL COMMENT '提醒方式 1-站内信 2-自定义 3-不通知',
  `F_SendConfigId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送配置',
  `F_Excerpt` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '摘要',
  `F_DefaultTitle` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '消息模板标题',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '消息实例' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_account_config
-- ----------------------------
DROP TABLE IF EXISTS `base_message_account_config`;
CREATE TABLE `base_message_account_config`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置类型',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
  `F_AddressorName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发件人昵称',
  `F_SmtpServer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SMTP服务器',
  `F_SmtpPort` int(11) NULL DEFAULT NULL COMMENT 'SMTP端口',
  `F_SslLink` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SSL安全链接',
  `F_SmtpUser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SMTP用户',
  `F_SmtpPassword` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SMTP密码',
  `F_Channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道',
  `F_SmsSignature` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短信签名',
  `F_AppId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用ID',
  `F_AppSecret` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用Secret',
  `F_EndPoint` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'EndPoint（阿里云）',
  `F_SdkAppId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SDK AppID（腾讯云）',
  `F_AppKey` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'AppKey（腾讯云）',
  `F_ZoneName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地域域名（腾讯云）',
  `F_ZoneParam` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地域参数（腾讯云）',
  `F_EnterpriseId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业id',
  `F_AgentId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'AgentID',
  `F_WebhookType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'WebHook类型',
  `F_WebhookAddress` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'WebHook地址',
  `F_ApproveType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认证类型',
  `F_Bearer` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Bearer令牌',
  `F_UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名（基本认证）',
  `F_Password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码（基本认证）',
  `F_SortCode` int(11) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '状态',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '说明',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(2) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '账号配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_data_type
-- ----------------------------
DROP TABLE IF EXISTS `base_message_data_type`;
CREATE TABLE `base_message_data_type`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据类型（1：消息类型，2：渠道，3：webhook类型，4：消息来源）',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据编码（为防止与系统后续更新的功能的数据编码冲突，客户自定义添加的功能的数据编码请以ZDY开头。例如：ZDY1）',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人员',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `F_DeleteMark` int(2) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_EnabledMark` int(2) NULL DEFAULT NULL COMMENT '状态',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息中心模块数据类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_monitor
-- ----------------------------
DROP TABLE IF EXISTS `base_message_monitor`;
CREATE TABLE `base_message_monitor`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `F_AccountId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账号id',
  `F_AccountName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账号名称',
  `F_AccountCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账号编码',
  `F_MessageType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息类型',
  `F_MessageSource` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息来源',
  `F_SendTime` datetime NULL DEFAULT NULL COMMENT '发送时间',
  `F_MessageTemplateId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息模板id',
  `F_Title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `F_ReceiveUser` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '接收人',
  `F_Content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人员',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `F_DeleteMark` int(2) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_EnabledMark` int(2) NULL DEFAULT NULL COMMENT '状态',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息监控表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_send_config
-- ----------------------------
DROP TABLE IF EXISTS `base_message_send_config`;
CREATE TABLE `base_message_send_config`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
  `F_TemplateType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板类型',
  `F_MessageSource` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息来源',
  `F_SortCode` int(11) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '状态',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '说明',
  `F_UsedId` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '被引用id',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人员',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `F_DeleteMark` int(2) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息发送配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_send_record
-- ----------------------------
DROP TABLE IF EXISTS `base_message_send_record`;
CREATE TABLE `base_message_send_record`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `F_SendConfigId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送配置id',
  `F_MessageSource` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息来源',
  `F_UsedId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被引用id',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人员',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除人员',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_EnabledMark` int(2) NULL DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '账号配置使用记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_send_template
-- ----------------------------
DROP TABLE IF EXISTS `base_message_send_template`;
CREATE TABLE `base_message_send_template`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `F_SendConfigId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息发送配置id',
  `F_MessageType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息类型',
  `F_TemplateId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息模板id',
  `F_AccountConfigId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账号配置id',
  `F_SortCode` int(11) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '状态',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '说明',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人员',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `F_DeleteMark` int(2) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发送配置模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_short_link
-- ----------------------------
DROP TABLE IF EXISTS `base_message_short_link`;
CREATE TABLE `base_message_short_link`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_ShortLink` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短链接',
  `F_RealPcLink` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'PC端链接',
  `F_RealAppLink` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'App端链接',
  `F_BodyText` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '流程内容',
  `F_IsUsed` int(11) NULL DEFAULT NULL COMMENT '是否点击后失效',
  `F_ClickNum` int(11) NULL DEFAULT NULL COMMENT '点击次数',
  `F_UnableNum` int(11) NULL DEFAULT NULL COMMENT '失效次数',
  `F_UnableTime` datetime NULL DEFAULT NULL COMMENT '失效时间',
  `F_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_EnabledMark` int(2) NULL DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息链接表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_sms_field
-- ----------------------------
DROP TABLE IF EXISTS `base_message_sms_field`;
CREATE TABLE `base_message_sms_field`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `F_TemplateId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息模板id',
  `F_FieldId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数id',
  `F_SmsField` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短信变量',
  `F_Field` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人员',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `F_DeleteMark` int(2) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_EnabledMark` int(2) NULL DEFAULT NULL COMMENT '状态',
  `F_IsTitle` int(2) NULL DEFAULT NULL COMMENT '是否标题',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信变量表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_template_config
-- ----------------------------
DROP TABLE IF EXISTS `base_message_template_config`;
CREATE TABLE `base_message_template_config`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
  `F_TemplateType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板类型',
  `F_MessageSource` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息来源',
  `F_MessageType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息类型',
  `F_EnabledMark` int(2) NULL DEFAULT NULL COMMENT '状态',
  `F_SortCode` int(10) NULL DEFAULT NULL COMMENT '排序',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '说明',
  `F_Title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息标题',
  `F_Content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '消息内容',
  `F_TemplateCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板编号',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人员',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `F_DeleteMark` int(2) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_WxSkip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '跳转方式',
  `F_XcxAppId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序id',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_template_param
-- ----------------------------
DROP TABLE IF EXISTS `base_message_template_param`;
CREATE TABLE `base_message_template_param`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `F_TemplateId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息模板id',
  `F_Field` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数名称',
  `F_FieldName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数说明',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人员',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `F_DeleteMark` int(2) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_EnabledMark` int(2) NULL DEFAULT NULL COMMENT '状态',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息模板参数表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_wechat_user
-- ----------------------------
DROP TABLE IF EXISTS `base_message_wechat_user`;
CREATE TABLE `base_message_wechat_user`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `F_GzhId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信公众号id',
  `F_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `F_OpenId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信公众号用户id',
  `F_CloseMark` int(2) NULL DEFAULT NULL COMMENT '是否关注',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人员',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除人员',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_EnabledMark` int(2) NULL DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信公众号用户关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_messagereceive
-- ----------------------------
DROP TABLE IF EXISTS `base_messagereceive`;
CREATE TABLE `base_messagereceive`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_MessageId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '消息主键',
  `F_UserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户主键',
  `F_IsRead` int(11) NULL DEFAULT NULL COMMENT '是否阅读',
  `F_ReadTime` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  `F_ReadCount` int(11) NULL DEFAULT NULL COMMENT '阅读次数',
  `F_BodyText` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '内容',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_Excerpt` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '摘要',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '消息接收' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_module
-- ----------------------------
DROP TABLE IF EXISTS `base_module`;
CREATE TABLE `base_module`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能上级',
  `F_Type` int(11) NULL DEFAULT NULL COMMENT '功能类别',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能编号',
  `F_UrlAddress` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '功能地址',
  `F_IsButtonAuthorize` int(11) NULL DEFAULT NULL COMMENT '按钮权限',
  `F_IsColumnAuthorize` int(11) NULL DEFAULT NULL COMMENT '列表权限',
  `F_IsDataAuthorize` int(11) NULL DEFAULT NULL COMMENT '数据权限',
  `F_PropertyJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展属性',
  `F_Description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_LinkTarget` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接目标',
  `F_Category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单分类',
  `F_Icon` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单图标',
  `F_IsFormAuthorize` int(11) NULL DEFAULT NULL COMMENT '表单权限',
  `F_ModuleId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联功能id',
  `F_SystemId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统id',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统功能' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_modulebutton
-- ----------------------------
DROP TABLE IF EXISTS `base_modulebutton`;
CREATE TABLE `base_modulebutton`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '按钮上级',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '按钮名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '按钮编号',
  `F_Icon` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '按钮图标',
  `F_UrlAddress` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '请求地址',
  `F_PropertyJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '扩展属性',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_ModuleId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '功能主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '按钮权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_modulecolumn
-- ----------------------------
DROP TABLE IF EXISTS `base_modulecolumn`;
CREATE TABLE `base_modulecolumn`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '列表上级',
  `F_FullName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `F_EnCode` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `F_BindTable` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '绑定表格Id',
  `F_BindTableName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '绑定表格描述',
  `F_PropertyJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '扩展属性',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_ModuleId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '功能主键',
  `F_FieldRule` int(11) NULL DEFAULT NULL COMMENT '字段规则',
  `F_ChildTableKey` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '子表规则key',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '列表权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_moduledataauthorize
-- ----------------------------
DROP TABLE IF EXISTS `base_moduledataauthorize`;
CREATE TABLE `base_moduledataauthorize`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字段名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字段编号',
  `F_Type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字段类型',
  `F_ConditionSymbol` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '条件符号',
  `F_ConditionSymbolJson` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '条件符号Json',
  `F_ConditionText` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '条件内容',
  `F_PropertyJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '扩展属性',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_ModuleId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '功能主键',
  `F_FieldRule` int(11) NULL DEFAULT NULL COMMENT '字段规则',
  `F_ChildTableKey` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '子表规则key',
  `F_BindTable` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '绑定表格Id',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据权限配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_moduledataauthorizelink
-- ----------------------------
DROP TABLE IF EXISTS `base_moduledataauthorizelink`;
CREATE TABLE `base_moduledataauthorizelink`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_LinkId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源连接',
  `F_LinkTables` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '连接表名',
  `F_ModuleId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单主键',
  `F_Type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限类型',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据权限连接管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_moduledataauthorizescheme
-- ----------------------------
DROP TABLE IF EXISTS `base_moduledataauthorizescheme`;
CREATE TABLE `base_moduledataauthorizescheme`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '方案编号',
  `F_FullName` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '方案名称',
  `F_ConditionJson` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '条件规则Json',
  `F_ConditionText` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '条件规则描述',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_ModuleId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '功能主键',
  `F_AllData` int(2) NULL DEFAULT NULL COMMENT '全部数据标识',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据权限方案' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_moduleform
-- ----------------------------
DROP TABLE IF EXISTS `base_moduleform`;
CREATE TABLE `base_moduleform`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单上级',
  `F_FullName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `F_EnCode` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `F_PropertyJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展属性',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_ModuleId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能主键',
  `F_FieldRule` int(11) NULL DEFAULT NULL COMMENT '字段规则',
  `F_ChildTableKey` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子表规则key',
  `F_BindTable` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '绑定表格Id',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '表单权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_organize
-- ----------------------------
DROP TABLE IF EXISTS `base_organize`;
CREATE TABLE `base_organize`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机构上级',
  `F_Category` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机构分类',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机构编号',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机构名称',
  `F_ManagerId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机构主管',
  `F_PropertyJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '扩展属性',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_OrganizeIdTree` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '父级组织',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '组织机构' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_organize_relation
-- ----------------------------
DROP TABLE IF EXISTS `base_organize_relation`;
CREATE TABLE `base_organize_relation`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_Organize_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组织主键',
  `F_Object_Type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对象类型（角色：role）',
  `F_Object_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对象主键',
  `F_Sort_Code` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_Creator_Time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_Creator_User_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE,
  INDEX `index`(`F_Organize_Id`) USING BTREE,
  INDEX `objectId`(`F_Object_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '组织关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_organizeadministrator
-- ----------------------------
DROP TABLE IF EXISTS `base_organizeadministrator`;
CREATE TABLE `base_organizeadministrator`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_UserId` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户主键',
  `F_OrganizeId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '机构主键',
  `F_OrganizeType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '机构类型',
  `F_ThisLayerAdd` int(11) NULL DEFAULT NULL COMMENT '本层添加',
  `F_ThisLayerEdit` int(11) NULL DEFAULT NULL COMMENT '本层编辑',
  `F_ThisLayerDelete` int(11) NULL DEFAULT NULL COMMENT '本层删除',
  `F_SubLayerAdd` int(11) NULL DEFAULT NULL COMMENT '子层添加',
  `F_SubLayerEdit` int(11) NULL DEFAULT NULL COMMENT '子层编辑',
  `F_SubLayerDelete` int(11) NULL DEFAULT NULL COMMENT '子层删除',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_ThisLayerSelect` int(2) NULL DEFAULT NULL COMMENT '本层查看',
  `F_SubLayerSelect` int(2) NULL DEFAULT NULL COMMENT '子层查看',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '机构分级管理员' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_portal
-- ----------------------------
DROP TABLE IF EXISTS `base_portal`;
CREATE TABLE `base_portal`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `F_Description` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_FullName` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
  `F_Category` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类（数据字典）',
  `F_FormData` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '表单配置JSON',
  `F_Type` int(11) NULL DEFAULT 0 COMMENT '类型(0-页面设计,1-自定义路径)',
  `F_CustomUrl` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '静态页面路径',
  `F_LinkType` int(11) NULL DEFAULT 0 COMMENT '链接类型(0-页面,1-外链)',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_EnabledLock` int(11) NULL DEFAULT NULL COMMENT '锁定（0-锁定，1-自定义）',
  `F_ParentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父级id',
  `F_Platform` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'PC:网页端 APP:手机端 MOD:模板',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_portal_data
-- ----------------------------
DROP TABLE IF EXISTS `base_portal_data`;
CREATE TABLE `base_portal_data`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
  `F_PortalId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '门户ID',
  `F_Platform` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'PC:网页端 APP:手机端 ',
  `F_FormData` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '表单配置JSON',
  `F_System_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统ID',
  `F_Type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类型（mod：模型、custom：自定义）',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建用户',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '个人门户数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_portal_manage
-- ----------------------------
DROP TABLE IF EXISTS `base_portal_manage`;
CREATE TABLE `base_portal_manage`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键_id',
  `F_Description` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
  `F_Portal_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '门户_id',
  `F_System_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统_id',
  `F_SortCode` bigint(20) NOT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NOT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NOT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建用户_id',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户_id',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户_id',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户_id',
  `F_Home_Page_Mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `F_Platform` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '平台',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '门户管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_position
-- ----------------------------
DROP TABLE IF EXISTS `base_position`;
CREATE TABLE `base_position`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '岗位名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '岗位编号',
  `F_Type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '岗位类型',
  `F_PropertyJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '扩展属性',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_OrganizeId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机构主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '岗位信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_print_log
-- ----------------------------
DROP TABLE IF EXISTS `base_print_log`;
CREATE TABLE `base_print_log`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `F_PrintMan` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '打印人',
  `F_PrintTime` datetime NULL DEFAULT NULL COMMENT '打印时间',
  `F_PrintNum` int(3) NULL DEFAULT NULL COMMENT '打印条数',
  `F_PrintTitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '打印功能名称',
  `F_PrintId` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '基于哪一个模板',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '打印模板日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_printdev
-- ----------------------------
DROP TABLE IF EXISTS `base_printdev`;
CREATE TABLE `base_printdev`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键_id',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `F_Encode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `F_Category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类',
  `F_Type` int(11) NOT NULL COMMENT '类型',
  `F_Description` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `F_SortCode` bigint(20) NOT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NOT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NOT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建用户_id',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户_id',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户_id',
  `F_DbLinkId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '连接数据 _id',
  `F_SqlTemplate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'sql语句',
  `F_LeftFields` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '左侧字段',
  `F_PrintTemplate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '打印模板',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_PageParam` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '纸张参数',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '打印模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_province
-- ----------------------------
DROP TABLE IF EXISTS `base_province`;
CREATE TABLE `base_province`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '区域上级',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '区域编号',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '区域名称',
  `F_QuickQuery` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快速查询',
  `F_Type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '区域类型',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '行政区划' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_province_atlas
-- ----------------------------
DROP TABLE IF EXISTS `base_province_atlas`;
CREATE TABLE `base_province_atlas`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域上级',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域编号',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域名称',
  `F_QuickQuery` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快速查询',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域类型',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_DivisionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '行政区划编码',
  `F_AtlasCenter` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '中心经纬度',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '行政区划' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_role
-- ----------------------------
DROP TABLE IF EXISTS `base_role`;
CREATE TABLE `base_role`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色编号',
  `F_Type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色类型',
  `F_PropertyJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '扩展属性',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_Global_Mark` tinyint(5) NOT NULL COMMENT '全局标识',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统角色' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_schedule
-- ----------------------------
DROP TABLE IF EXISTS `base_schedule`;
CREATE TABLE `base_schedule`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  `F_Urgent` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '紧急程度',
  `F_Title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `F_Content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `F_AllDay` int(11) NULL DEFAULT NULL COMMENT '全天',
  `F_StartDay` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_StartTime` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开始日期',
  `F_EndDay` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_EndTime` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结束日期',
  `F_Duration` int(11) NULL DEFAULT NULL COMMENT '时长',
  `F_Color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '颜色',
  `F_ReminderTime` int(11) NULL DEFAULT NULL COMMENT '提醒',
  `F_ReminderType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提醒方式',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_Send` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送配置',
  `F_SendName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送配置',
  `F_Repetition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重复提醒',
  `F_RepeatTime` datetime NULL DEFAULT NULL COMMENT '结束重复',
  `F_PushTime` datetime NULL DEFAULT NULL COMMENT '推送时间',
  `F_GroupId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组id',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '日程' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_schedule_log
-- ----------------------------
DROP TABLE IF EXISTS `base_schedule_log`;
CREATE TABLE `base_schedule_log`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  `F_Urgent` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '紧急程度',
  `F_Title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `F_Content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `F_AllDay` int(11) NULL DEFAULT NULL COMMENT '全天',
  `F_StartDay` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_StartTime` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开始日期',
  `F_EndDay` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_EndTime` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结束日期',
  `F_Duration` int(11) NULL DEFAULT NULL COMMENT '时长',
  `F_Color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '颜色',
  `F_ReminderTime` int(11) NULL DEFAULT NULL COMMENT '提醒',
  `F_ReminderType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提醒方式',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_Send` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送配置',
  `F_SendName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送配置',
  `F_Repetition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重复提醒',
  `F_RepeatTime` datetime NULL DEFAULT NULL COMMENT '结束重复',
  `F_PushTime` datetime NULL DEFAULT NULL COMMENT '推送时间',
  `F_GroupId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组id',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_OperationType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作类型',
  `F_UserId` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参与用户',
  `F_ScheduleId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日程id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '日程' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_schedule_user
-- ----------------------------
DROP TABLE IF EXISTS `base_schedule_user`;
CREATE TABLE `base_schedule_user`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_ScheduleId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日程id',
  `F_ToUserIds` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '日程用户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_signimg
-- ----------------------------
DROP TABLE IF EXISTS `base_signimg`;
CREATE TABLE `base_signimg`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_SignImg` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '签名图片',
  `F_IsDefault` int(11) NULL DEFAULT NULL COMMENT '是否默认',
  `F_Description` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '个性签名\r\n' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_sms_template
-- ----------------------------
DROP TABLE IF EXISTS `base_sms_template`;
CREATE TABLE `base_sms_template`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_Company` int(11) NULL DEFAULT NULL COMMENT '短信提供商',
  `F_AppId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用编号',
  `F_SignContent` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签名内容',
  `F_TemplateId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板编号',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板名称',
  `F_TemplateJson` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板参数JSON',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板编码',
  `F_Endpoint` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Endpoint',
  `F_Region` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'region',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短息模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_socialsusersentity
-- ----------------------------
DROP TABLE IF EXISTS `base_socialsusersentity`;
CREATE TABLE `base_socialsusersentity`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `F_SocialType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方类型',
  `F_SocialId` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方账号id',
  `F_SocialName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方账号',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_Description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标记',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '第三方绑定表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_synthirdinfo
-- ----------------------------
DROP TABLE IF EXISTS `base_synthirdinfo`;
CREATE TABLE `base_synthirdinfo`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ThirdType` int(11) NULL DEFAULT NULL COMMENT '第三方类型(1:企业微信;2:钉钉)',
  `F_DataType` int(11) NULL DEFAULT NULL COMMENT '数据类型(1:组织(公司与部门);2:用户)',
  `F_SysObjId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统对象ID',
  `F_ThirdObjId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '第三对象ID',
  `F_SynState` int(11) NULL DEFAULT NULL COMMENT '同步状态(0:未同步;1:同步成功;2:同步失败)',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '第三方同步' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_sysconfig
-- ----------------------------
DROP TABLE IF EXISTS `base_sysconfig`;
CREATE TABLE `base_sysconfig`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_Name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_Key` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '键',
  `F_Value` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '值',
  `F_Category` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_syslog
-- ----------------------------
DROP TABLE IF EXISTS `base_syslog`;
CREATE TABLE `base_syslog`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_UserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户主键',
  `F_UserName` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户主键',
  `F_Category` int(11) NULL DEFAULT NULL COMMENT '日志分类',
  `F_Type` int(11) NULL DEFAULT NULL COMMENT '日志类型',
  `F_Level` int(11) NULL DEFAULT NULL COMMENT '日志级别',
  `F_IPAddress` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `F_IPAddressName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'IP所在城市',
  `F_RequestURL` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '请求地址',
  `F_RequestMethod` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求方法',
  `F_RequestDuration` int(11) NULL DEFAULT NULL COMMENT '请求耗时',
  `F_Abstracts` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '日志摘要',
  `F_Json` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '日志内容',
  `F_PlatForm` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '平台设备',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '操作日期',
  `F_ModuleId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '功能主键',
  `F_ModuleName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '功能名称',
  `F_ObjectId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对象Id',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_system
-- ----------------------------
DROP TABLE IF EXISTS `base_system`;
CREATE TABLE `base_system`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统编号',
  `F_Icon` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统图标',
  `F_IsMain` int(11) NULL DEFAULT NULL COMMENT '是否是主系统（0-不是，1-是）',
  `F_PropertyJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展属性',
  `F_Description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统应用' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_timetask
-- ----------------------------
DROP TABLE IF EXISTS `base_timetask`;
CREATE TABLE `base_timetask`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务编码',
  `F_FullName` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务名称',
  `F_ExecuteType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '执行类型',
  `F_ExecuteContent` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '执行内容',
  `F_ExecuteCycleJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '执行周期',
  `F_LastRunTime` datetime NULL DEFAULT NULL COMMENT '最后运行时间',
  `F_NextRunTime` datetime NULL DEFAULT NULL COMMENT '下次运行时间',
  `F_RunCount` int(11) NULL DEFAULT NULL COMMENT '运行次数',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '定时任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_timetasklog
-- ----------------------------
DROP TABLE IF EXISTS `base_timetasklog`;
CREATE TABLE `base_timetasklog`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_TaskId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '定时任务主键',
  `F_RunTime` datetime NULL DEFAULT NULL COMMENT '执行时间',
  `F_RunResult` int(11) NULL DEFAULT NULL COMMENT '执行结果',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '执行说明',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '定时任务记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_user
-- ----------------------------
DROP TABLE IF EXISTS `base_user`;
CREATE TABLE `base_user`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_Account` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账户',
  `F_RealName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `F_QuickQuery` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快速查询',
  `F_NickName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '呢称',
  `F_HeadIcon` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '头像',
  `F_Gender` int(11) NULL DEFAULT NULL COMMENT '性别',
  `F_Birthday` datetime NULL DEFAULT NULL COMMENT '生日',
  `F_MobilePhone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机',
  `F_TelePhone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电话',
  `F_Landline` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'F_Landline',
  `F_Email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `F_Nation` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '民族',
  `F_NativePlace` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '籍贯',
  `F_EntryDate` datetime NULL DEFAULT NULL COMMENT '入职日期',
  `F_CertificatesType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '证件类型',
  `F_CertificatesNumber` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '证件号码',
  `F_Education` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文化程度',
  `F_UrgentContacts` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'F_UrgentContacts',
  `F_UrgentTelePhone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '紧急电话',
  `F_PostalAddress` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '通讯地址',
  `F_Signature` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '自我介绍',
  `F_Password` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
  `F_Secretkey` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '秘钥',
  `F_FirstLogTime` datetime NULL DEFAULT NULL COMMENT '首次登录时间',
  `F_FirstLogIP` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '首次登录IP',
  `F_PrevLogTime` datetime NULL DEFAULT NULL COMMENT '前次登录时间',
  `F_PrevLogIP` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '前次登录IP',
  `F_LastLogTime` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `F_LastLogIP` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `F_LogSuccessCount` int(11) NULL DEFAULT NULL COMMENT '登录成功次数',
  `F_LogErrorCount` int(11) NULL DEFAULT NULL COMMENT '登录错误次数',
  `F_ChangePasswordDate` datetime NULL DEFAULT NULL COMMENT '最后修改密码时间',
  `F_Language` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统语言',
  `F_Theme` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统样式',
  `F_CommonMenu` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '常用菜单',
  `F_IsAdministrator` int(11) NULL DEFAULT NULL COMMENT '是否管理员',
  `F_PropertyJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '扩展属性',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_ManagerId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主管主键',
  `F_OrganizeId` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织主键',
  `F_PositionId` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '岗位主键',
  `F_RoleId` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '角色主键',
  `F_PortalId` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '门户主键',
  `F_LockMark` int(11) NULL DEFAULT NULL COMMENT '是否锁定',
  `F_UnlockTime` datetime NULL DEFAULT NULL COMMENT '解锁时间',
  `F_GroupId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分组id',
  `F_SystemId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统id',
  `F_AppSystemId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'App系统id',
  `F_DingJobNumber` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '钉钉工号',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_user_device
-- ----------------------------
DROP TABLE IF EXISTS `base_user_device`;
CREATE TABLE `base_user_device`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NOT NULL COMMENT '主键id',
  `F_ClientId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '设备id',
  `F_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '用户id',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_DeleteMark` int(2) NULL DEFAULT NULL COMMENT '删除标识',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_EnabledMark` int(2) NULL DEFAULT NULL COMMENT '状态',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_german2_ci COMMENT = '用户登录设备id表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_user_old_password
-- ----------------------------
DROP TABLE IF EXISTS `base_user_old_password`;
CREATE TABLE `base_user_old_password`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_UserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'userid',
  `F_Account` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账户',
  `F_OldPassword` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '旧密码',
  `F_Secretkey` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '秘钥',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_TenantId` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户旧密码记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_userrelation
-- ----------------------------
DROP TABLE IF EXISTS `base_userrelation`;
CREATE TABLE `base_userrelation`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_UserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户主键',
  `F_ObjectType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对象类型',
  `F_ObjectId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对象主键',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_visualdev
-- ----------------------------
DROP TABLE IF EXISTS `base_visualdev`;
CREATE TABLE `base_visualdev`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_FullName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
  `F_State` int(11) NULL DEFAULT NULL COMMENT '状态(0-暂存（默认），1-发布)',
  `F_Type` int(11) NULL DEFAULT NULL COMMENT '类型(1-应用开发,2-移动开发,3-流程表单)',
  `F_Table` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关联的表',
  `F_Category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类（数据字典）',
  `F_FormData` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表单配置JSON',
  `F_ColumnData` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '列表配置JSON',
  `F_Fields` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '功能字段JSON',
  `F_TemplateData` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '前端模板JSON',
  `F_DbLinkId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联数据连接id',
  `F_FlowTemplateJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '工作流模板JSON',
  `F_WebType` int(11) NULL DEFAULT NULL COMMENT '页面类型（1、纯表单，2、表单加列表，3、表单列表工作流）',
  `F_FlowId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联工作流连接id',
  `F_AppColumnData` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'app列表配置JSON',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_EnableFlow` int(2) NULL DEFAULT NULL COMMENT '启用流程',
  `F_InterfaceId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口id',
  `F_InterfaceName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口名称',
  `F_InterfaceParam` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '接口参数',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '可视化开发功能表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_visualdev_modeldata
-- ----------------------------
DROP TABLE IF EXISTS `base_visualdev_modeldata`;
CREATE TABLE `base_visualdev_modeldata`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_VisualDevId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能ID',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_ParentId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区分主子表-',
  `F_Data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '数据包',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '0代码功能数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_visualdev_release
-- ----------------------------
DROP TABLE IF EXISTS `base_visualdev_release`;
CREATE TABLE `base_visualdev_release`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_FullName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
  `F_State` int(11) NULL DEFAULT NULL COMMENT '状态(0-暂存（默认），1-发布)',
  `F_Type` int(11) NOT NULL COMMENT '类型(1-应用开发,2-移动开发,3-流程表单)',
  `F_Table` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关联的表',
  `F_Category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类（数据字典）',
  `F_FormData` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表单配置JSON',
  `F_ColumnData` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '列表配置JSON',
  `F_DbLinkId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联数据连接id',
  `F_WebType` int(11) NULL DEFAULT NULL COMMENT '页面类型（1、纯表单，2、表单加列表，3、表单列表工作流）',
  `F_FlowId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联工作流连接id',
  `F_AppColumnData` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'app列表配置JSON',
  `F_EnableFlow` int(2) NULL DEFAULT 0 COMMENT '启用流程',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_InterfaceId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口id',
  `F_InterfaceName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口名称',
  `F_InterfaceParam` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '接口参数',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '可视化开发功能已发布表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_visualdev_short_link
-- ----------------------------
DROP TABLE IF EXISTS `base_visualdev_short_link`;
CREATE TABLE `base_visualdev_short_link`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_ShortLink` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短链接',
  `F_FormUse` int(11) NULL DEFAULT NULL COMMENT '外链填单开关',
  `F_FormLink` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外链填单',
  `F_FormPassUse` int(11) NULL DEFAULT NULL COMMENT '外链密码开关',
  `F_FormPassword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外链填单密码',
  `F_ColumnUse` int(11) NULL DEFAULT NULL COMMENT '公开查询开关',
  `F_ColumnLink` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公开查询',
  `F_ColumnPassUse` int(11) NULL DEFAULT NULL COMMENT '查询密码开关',
  `F_ColumnPassword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公开查询密码',
  `F_ColumnCondition` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '查询条件',
  `F_ColumnText` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '显示内容',
  `F_RealPcLink` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'PC端链接',
  `F_RealAppLink` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'App端链接',
  `F_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_EnabledMark` int(2) NULL DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '表单外链配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for blade_visual
-- ----------------------------
DROP TABLE IF EXISTS `blade_visual`;
CREATE TABLE `blade_visual`  (
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大屏标题',
  `background_url` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '大屏背景',
  `category` int(11) NULL DEFAULT NULL COMMENT '大屏类型',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布密码',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_dept` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `status` int(11) NOT NULL COMMENT '状态',
  `is_deleted` int(11) NOT NULL COMMENT '是否已删除',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '可视化表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for blade_visual_category
-- ----------------------------
DROP TABLE IF EXISTS `blade_visual_category`;
CREATE TABLE `blade_visual_category`  (
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `category_key` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类键值',
  `category_value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名称',
  `is_deleted` int(11) NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '可视化分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for blade_visual_config
-- ----------------------------
DROP TABLE IF EXISTS `blade_visual_config`;
CREATE TABLE `blade_visual_config`  (
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `visual_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '可视化表主键',
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置json',
  `component` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '组件json',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '可视化配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for blade_visual_db
-- ----------------------------
DROP TABLE IF EXISTS `blade_visual_db`;
CREATE TABLE `blade_visual_db`  (
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `driver_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '驱动类',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '连接地址',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_dept` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `status` int(11) NULL DEFAULT NULL COMMENT '状态',
  `is_deleted` int(11) NULL DEFAULT NULL COMMENT '是否已删除',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '可视化数据源配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for blade_visual_map
-- ----------------------------
DROP TABLE IF EXISTS `blade_visual_map`;
CREATE TABLE `blade_visual_map`  (
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地图名称',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '地图数据',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '可视化地图配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ct293714178055641925
-- ----------------------------
DROP TABLE IF EXISTS `ct293714178055641925`;
CREATE TABLE `ct293714178055641925`  (
  `selectField107` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '乐器',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_foreignId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ct294012196562705414
-- ----------------------------
DROP TABLE IF EXISTS `ct294012196562705414`;
CREATE TABLE `ct294012196562705414`  (
  `dateField117` datetime NULL DEFAULT NULL COMMENT '开始日期',
  `dateField118` datetime NULL DEFAULT NULL COMMENT '结束日期',
  `comInputField119` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司名称',
  `comInputField120` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '担任职务',
  `textareaField121` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主要工作内容',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_foreignId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ct294022926200511493
-- ----------------------------
DROP TABLE IF EXISTS `ct294022926200511493`;
CREATE TABLE `ct294022926200511493`  (
  `comInputField145` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包保人社区工作者姓名',
  `comInputField146` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包保人社区工作者联系方式',
  `comInputField147` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包保人医务工作者姓名',
  `comInputField148` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包保人医务工作者联系方式',
  `comInputField149` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包保人公安民警姓名',
  `comInputField150` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包保人公安民警联系方式',
  `comInputField151` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包保人志愿者姓名',
  `comInputField152` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包保人志愿者联系方式',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_foreignId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ct294031961024928774
-- ----------------------------
DROP TABLE IF EXISTS `ct294031961024928774`;
CREATE TABLE `ct294031961024928774`  (
  `selectField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联目标',
  `selectField109` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '目标达成度',
  `numInputField112` decimal(10, 0) NULL DEFAULT NULL COMMENT '评分',
  `comInputField111` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '情况描述',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_foreignId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ct294031961024928775
-- ----------------------------
DROP TABLE IF EXISTS `ct294031961024928775`;
CREATE TABLE `ct294031961024928775`  (
  `selectField114` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联结果',
  `selectField115` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关键结果达成度',
  `numInputField116` decimal(10, 0) NULL DEFAULT NULL COMMENT '评分',
  `comInputField117` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '情况描述',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_foreignId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ct294037561750363142
-- ----------------------------
DROP TABLE IF EXISTS `ct294037561750363142`;
CREATE TABLE `ct294037561750363142`  (
  `comInputField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '巡检结果',
  `dateField109` datetime NULL DEFAULT NULL COMMENT '巡检日期',
  `comInputField110` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '情况说明',
  `userSelectField111` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '巡检人',
  `comInputField124` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '巡检人手机',
  `selectField112` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '巡检分类',
  `numInputField113` decimal(10, 0) NULL DEFAULT NULL COMMENT '设备完好率',
  `uploadFzField114` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '现场照片',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_foreignId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ct294037561750363143
-- ----------------------------
DROP TABLE IF EXISTS `ct294037561750363143`;
CREATE TABLE `ct294037561750363143`  (
  `comInputField116` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '维修结果',
  `dateField117` datetime NULL DEFAULT NULL COMMENT '维修日期',
  `comInputField118` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '维修内容',
  `userSelectField119` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '维修人',
  `comInputField125` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '维修人手机',
  `uploadFzField122` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '现场照片',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_foreignId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ct294099893327272966
-- ----------------------------
DROP TABLE IF EXISTS `ct294099893327272966`;
CREATE TABLE `ct294099893327272966`  (
  `comInputField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出差地点',
  `dateField109` datetime NULL DEFAULT NULL COMMENT '开始日期',
  `dateField110` datetime NULL DEFAULT NULL COMMENT '结束日期',
  `comInputField111` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出差事由',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_foreignId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ct294376098702073542
-- ----------------------------
DROP TABLE IF EXISTS `ct294376098702073542`;
CREATE TABLE `ct294376098702073542`  (
  `comInputField106` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名',
  `comInputField107` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  `numInputField108` decimal(10, 0) NULL DEFAULT NULL COMMENT '销售价',
  `numInputField109` decimal(10, 0) NULL DEFAULT NULL COMMENT '当前库存',
  `numInputField110` decimal(10, 0) NULL DEFAULT NULL COMMENT '数量',
  `numInputField111` decimal(10, 0) NULL DEFAULT NULL COMMENT '单品总价',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_foreignId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ct294382866144468678
-- ----------------------------
DROP TABLE IF EXISTS `ct294382866144468678`;
CREATE TABLE `ct294382866144468678`  (
  `comInputField111` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `comInputField112` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  `numInputField113` decimal(10, 0) NULL DEFAULT NULL COMMENT '采购价',
  `numInputField114` decimal(10, 0) NULL DEFAULT NULL COMMENT '数量',
  `calculateField118` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单品总价',
  `numInputField116` decimal(10, 0) NULL DEFAULT NULL COMMENT '当前库存',
  `numInputField117` decimal(10, 0) NULL DEFAULT NULL COMMENT '入库后库存',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_foreignId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ct395899726056134917
-- ----------------------------
DROP TABLE IF EXISTS `ct395899726056134917`;
CREATE TABLE `ct395899726056134917`  (
  `comSelectField111` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '默认当前组织',
  `depSelectField112` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '默认当前部门',
  `userSelectField113` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '默认当前用户',
  `dateField114` datetime NULL DEFAULT NULL COMMENT '默认当前系统时间',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_foreignId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外键',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '3.4.6新增功能汇总示例-子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for data_report
-- ----------------------------
DROP TABLE IF EXISTS `data_report`;
CREATE TABLE `data_report`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '	主键ID',
  `F_CategoryId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组ID',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报表名称',
  `F_Content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报表内容',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
  `F_SortCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '排序码(默认0)',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '状态(0-默认，禁用，1-启用)',
  `F_Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述或说明',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志(默认0)',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户id',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据报表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_bigdata
-- ----------------------------
DROP TABLE IF EXISTS `ext_bigdata`;
CREATE TABLE `ext_bigdata`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编号',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '大数据测试' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_customer
-- ----------------------------
DROP TABLE IF EXISTS `ext_customer`;
CREATE TABLE `ext_customer`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
  `F_CustomerName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `F_Address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `F_Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_ContactTel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_document
-- ----------------------------
DROP TABLE IF EXISTS `ext_document`;
CREATE TABLE `ext_document`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文档父级',
  `F_Type` int(11) NULL DEFAULT NULL COMMENT '文档分类',
  `F_FullName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件名称',
  `F_FilePath` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '文件路径',
  `F_FileSize` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件大小',
  `F_FileExtension` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件后缀',
  `F_ReadcCount` int(11) NULL DEFAULT NULL COMMENT '阅读数量',
  `F_IsShare` int(11) NULL DEFAULT NULL COMMENT '是否共享',
  `F_ShareTime` datetime NULL DEFAULT NULL COMMENT '共享时间',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_UploaderUrl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '下载地址',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '知识文档' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_documentshare
-- ----------------------------
DROP TABLE IF EXISTS `ext_documentshare`;
CREATE TABLE `ext_documentshare`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_DocumentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文档主键',
  `F_ShareUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '共享人员',
  `F_ShareTime` datetime NULL DEFAULT NULL COMMENT '共享时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '知识文档共享' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_emailconfig
-- ----------------------------
DROP TABLE IF EXISTS `ext_emailconfig`;
CREATE TABLE `ext_emailconfig`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_POP3Host` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'POP3服务',
  `F_POP3Port` int(11) NULL DEFAULT NULL COMMENT 'POP3端口',
  `F_SMTPHost` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SMTP服务',
  `F_SMTPPort` int(11) NULL DEFAULT NULL COMMENT 'SMTP端口',
  `F_Account` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账户',
  `F_Password` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
  `F_Ssl` int(11) NULL DEFAULT NULL COMMENT 'SSL登录',
  `F_SenderName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发件人名称',
  `F_FolderJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '我的文件夹',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '邮件配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_emailreceive
-- ----------------------------
DROP TABLE IF EXISTS `ext_emailreceive`;
CREATE TABLE `ext_emailreceive`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_Type` int(11) NULL DEFAULT NULL COMMENT '类型',
  `F_MAccount` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱账户',
  `F_MID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'F_MID',
  `F_Sender` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发件人',
  `F_SenderName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发件人名称',
  `F_Subject` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主题',
  `F_BodyText` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '正文',
  `F_Attachment` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '附件',
  `F_Read` int(11) NULL DEFAULT NULL COMMENT '阅读',
  `F_Date` datetime NULL DEFAULT NULL,
  `F_Starred` int(11) NULL DEFAULT NULL COMMENT '星标',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '邮件接收' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_emailsend
-- ----------------------------
DROP TABLE IF EXISTS `ext_emailsend`;
CREATE TABLE `ext_emailsend`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_Type` int(11) NULL DEFAULT NULL COMMENT '类型',
  `F_Sender` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '发件人',
  `F_To` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '收件人',
  `F_CC` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '抄送人',
  `F_BCC` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '密送人',
  `F_Colour` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '颜色',
  `F_Subject` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主题',
  `F_BodyText` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '正文',
  `F_Attachment` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '附件',
  `F_State` int(11) NULL DEFAULT NULL COMMENT '状态',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '邮件发送' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_employee
-- ----------------------------
DROP TABLE IF EXISTS `ext_employee`;
CREATE TABLE `ext_employee`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工号',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `F_Gender` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '性别',
  `F_DepartmentName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '部门',
  `F_PositionName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '职位',
  `F_WorkingNature` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用工性质',
  `F_IDNumber` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `F_Telephone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `F_AttendWorkTime` datetime NULL DEFAULT NULL COMMENT '参加工作',
  `F_Birthday` datetime NULL DEFAULT NULL COMMENT '出生年月',
  `F_Education` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '最高学历',
  `F_Major` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所学专业',
  `F_GraduationAcademy` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '毕业院校',
  `F_GraduationTime` datetime NULL DEFAULT NULL COMMENT '毕业时间',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '职员信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_order
-- ----------------------------
DROP TABLE IF EXISTS `ext_order`;
CREATE TABLE `ext_order`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_CustomerId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户Id',
  `F_CustomerName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `F_SalesmanId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务员Id',
  `F_SalesmanName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务员',
  `F_OrderDate` datetime NULL DEFAULT NULL COMMENT '订单日期',
  `F_OrderCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单编号',
  `F_TransportMode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '运输方式',
  `F_DeliveryDate` datetime NULL DEFAULT NULL COMMENT '发货日期',
  `F_DeliveryAddress` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '发货地址',
  `F_PaymentMode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '付款方式',
  `F_ReceivableMoney` decimal(18, 2) NULL DEFAULT NULL COMMENT '应收金额',
  `F_EarnestRate` decimal(18, 2) NULL DEFAULT NULL COMMENT '定金比率',
  `F_PrepayEarnest` decimal(18, 2) NULL DEFAULT NULL COMMENT '预付定金',
  `F_CurrentState` int(11) NULL DEFAULT NULL COMMENT '当前状态',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '附件信息',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '引擎id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '订单信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_orderentry
-- ----------------------------
DROP TABLE IF EXISTS `ext_orderentry`;
CREATE TABLE `ext_orderentry`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_OrderId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单主键',
  `F_GoodsId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品Id',
  `F_GoodsCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `F_GoodsName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `F_Specifications` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
  `F_Unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位',
  `F_Qty` decimal(18, 2) NULL DEFAULT NULL COMMENT '数量',
  `F_Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额',
  `F_Discount` decimal(18, 2) NULL DEFAULT NULL COMMENT '折扣%',
  `F_Cess` decimal(18, 2) NULL DEFAULT NULL COMMENT '税率%',
  `F_ActualPrice` decimal(18, 2) NULL DEFAULT NULL COMMENT '实际单价',
  `F_ActualAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '实际金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '订单明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_orderreceivable
-- ----------------------------
DROP TABLE IF EXISTS `ext_orderreceivable`;
CREATE TABLE `ext_orderreceivable`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_OrderId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单主键',
  `F_Abstract` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '收款摘要',
  `F_ReceivableDate` datetime NULL DEFAULT NULL COMMENT '收款日期',
  `F_ReceivableRate` decimal(18, 2) NULL DEFAULT NULL COMMENT '收款比率',
  `F_ReceivableMoney` decimal(18, 2) NULL DEFAULT NULL COMMENT '收款金额',
  `F_ReceivableMode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收款方式',
  `F_ReceivableState` int(11) NULL DEFAULT NULL COMMENT '收款状态',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '订单收款' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_product
-- ----------------------------
DROP TABLE IF EXISTS `ext_product`;
CREATE TABLE `ext_product`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `F_Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单编号',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户类别',
  `F_CustomerId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户id',
  `F_CustomerName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `F_SalesmanId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '制单人id',
  `F_SalesmanName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '制单人名称',
  `F_SalesmanDate` datetime NULL DEFAULT NULL COMMENT '制单日期',
  `F_AuditName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核人',
  `F_AuditDate` datetime NULL DEFAULT NULL COMMENT '审核日期',
  `F_AuditState` int(11) NULL DEFAULT NULL COMMENT '审核状态',
  `F_GoodsWarehouse` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发货仓库',
  `F_GoodsDate` datetime NULL DEFAULT NULL COMMENT '发货通知时间',
  `F_Consignor` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发货通知人',
  `F_GoodsState` int(11) NULL DEFAULT NULL COMMENT '发货通知',
  `F_CloseState` int(11) NULL DEFAULT NULL COMMENT '关闭状态',
  `F_CloseDate` datetime NULL DEFAULT NULL COMMENT '关闭日期',
  `F_GatheringType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收款方式',
  `F_Business` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务员',
  `F_Address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '送货地址',
  `F_ContactTel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `F_ContactName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人',
  `F_HarvestMsg` int(11) NULL DEFAULT NULL COMMENT '收货消息',
  `F_HarvestWarehouse` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货仓库',
  `F_IssuingName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '代发客户',
  `F_PartPrice` decimal(18, 2) NULL DEFAULT NULL COMMENT '让利金额',
  `F_ReducedPrice` decimal(18, 2) NULL DEFAULT NULL COMMENT '优惠金额',
  `F_DiscountPrice` decimal(18, 2) NULL DEFAULT NULL COMMENT '折后金额',
  `F_Description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '销售订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_productclassify
-- ----------------------------
DROP TABLE IF EXISTS `ext_productclassify`;
CREATE TABLE `ext_productclassify`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上级',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_productentry
-- ----------------------------
DROP TABLE IF EXISTS `ext_productentry`;
CREATE TABLE `ext_productentry`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_ProductId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单主键',
  `F_ProductCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品编号',
  `F_ProductName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `F_ProductSpecification` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品规格',
  `F_Qty` int(11) NULL DEFAULT NULL COMMENT '数量',
  `F_CommandType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '控制方式',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订货类型',
  `F_Money` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Util` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位',
  `F_Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '折后单价',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额',
  `F_Activity` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动',
  `F_Description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_productgoods
-- ----------------------------
DROP TABLE IF EXISTS `ext_productgoods`;
CREATE TABLE `ext_productgoods`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_ClassifyId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类主键',
  `F_Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品编号',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品分类',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额',
  `F_Money` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_ProductSpecification` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品规格',
  `F_Qty` int(11) NULL DEFAULT NULL COMMENT '库存数',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品商品' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_projectgantt
-- ----------------------------
DROP TABLE IF EXISTS `ext_projectgantt`;
CREATE TABLE `ext_projectgantt`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ParentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目上级',
  `F_ProjectId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目主键',
  `F_Type` int(11) NULL DEFAULT NULL COMMENT '项目类型',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目编号',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目名称',
  `F_TimeLimit` decimal(18, 0) NULL DEFAULT NULL COMMENT '项目工期',
  `F_Sign` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目标记',
  `F_SignColor` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标记颜色',
  `F_StartTime` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_EndTime` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_Schedule` int(11) NULL DEFAULT NULL COMMENT '当前进度',
  `F_ManagerIds` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '负责人',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_State` int(11) NULL DEFAULT NULL COMMENT '项目状态（1-正在进行，2-暂停）',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '项目计划' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_schedule
-- ----------------------------
DROP TABLE IF EXISTS `ext_schedule`;
CREATE TABLE `ext_schedule`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_Title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日程标题',
  `F_Content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '日程内容',
  `F_Colour` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日程颜色',
  `F_ColourCss` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '颜色样式',
  `F_StartTime` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_EndTime` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_AppAlert` int(11) NULL DEFAULT NULL COMMENT 'APP提醒',
  `F_Early` int(11) NULL DEFAULT NULL COMMENT '提醒设置',
  `F_MailAlert` int(11) NULL DEFAULT NULL COMMENT '邮件提醒',
  `F_WeChatAlert` int(11) NULL DEFAULT NULL COMMENT '微信提醒',
  `F_MobileAlert` int(11) NULL DEFAULT NULL COMMENT '短信提醒',
  `F_SystemAlert` int(11) NULL DEFAULT NULL COMMENT '系统提醒',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '日程安排' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_tableexample
-- ----------------------------
DROP TABLE IF EXISTS `ext_tableexample`;
CREATE TABLE `ext_tableexample`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_InteractionDate` datetime NULL DEFAULT NULL COMMENT '交互日期',
  `F_ProjectCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目编码',
  `F_ProjectName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目名称',
  `F_Principal` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `F_JackStands` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '立顶人',
  `F_ProjectType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目类型',
  `F_ProjectPhase` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目阶段',
  `F_CustomerName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `F_CostAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '费用金额',
  `F_TunesAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '已用金额',
  `F_ProjectedIncome` decimal(18, 2) NULL DEFAULT NULL COMMENT '预计收入',
  `F_Registrant` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登记人',
  `F_RegisterDate` datetime NULL DEFAULT NULL COMMENT '登记时间',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_Sign` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '标记',
  `F_PostilJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '批注列表Json',
  `F_PostilCount` int(11) NULL DEFAULT NULL COMMENT '批注总数',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '编辑时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编辑用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '表格示例数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_worklog
-- ----------------------------
DROP TABLE IF EXISTS `ext_worklog`;
CREATE TABLE `ext_worklog`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `F_Title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日志标题',
  `F_TodayContent` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '今天内容',
  `F_TomorrowContent` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '明天内容',
  `F_Question` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '遇到问题',
  `F_ToUserId` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '发送给谁',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '工作日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ext_worklogshare
-- ----------------------------
DROP TABLE IF EXISTS `ext_worklogshare`;
CREATE TABLE `ext_worklogshare`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_WorkLogId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日志主键',
  `F_ShareUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '共享人员',
  `F_ShareTime` datetime NULL DEFAULT NULL COMMENT '共享时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '工作日志分享' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for file_detail
-- ----------------------------
DROP TABLE IF EXISTS `file_detail`;
CREATE TABLE `file_detail`  (
  `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '文件id',
  `url` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '文件访问地址',
  `size` bigint(20) NULL DEFAULT NULL COMMENT '文件大小，单位字节',
  `filename` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件名称',
  `original_filename` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '原始文件名',
  `base_path` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基础存储路径',
  `path` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '存储路径',
  `ext` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件扩展名',
  `content_type` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'MIME类型',
  `platform` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '存储平台',
  `th_url` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '缩略图访问路径',
  `th_filename` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '缩略图名称',
  `th_size` bigint(20) NULL DEFAULT NULL COMMENT '缩略图大小，单位字节',
  `th_content_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '缩略图MIME类型',
  `object_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件所属对象id',
  `object_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件所属对象类型，例如用户头像，评价图片',
  `attr` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '附加属性',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文件记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_candidates
-- ----------------------------
DROP TABLE IF EXISTS `flow_candidates`;
CREATE TABLE `flow_candidates`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_TaskNodeId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '节点id',
  `F_TaskId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务id',
  `F_HandleId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审批人id',
  `F_Account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审批人账号',
  `F_Candidates` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '候选人',
  `F_TaskOperatorId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经办主键',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审批类型',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '流程候选人' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_comment
-- ----------------------------
DROP TABLE IF EXISTS `flow_comment`;
CREATE TABLE `flow_comment`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_TaskId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务主键',
  `F_Text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文本',
  `F_Image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '图片',
  `F_File` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附件',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '流程评论' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_delegate
-- ----------------------------
DROP TABLE IF EXISTS `flow_delegate`;
CREATE TABLE `flow_delegate`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ToUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '被委托人',
  `F_ToUserName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '被委托人',
  `F_FlowId` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '委托流程（为空是全部流程）',
  `F_FlowName` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '委托流程名称',
  `F_FlowCategory` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程分类',
  `F_StartTime` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_EndTime` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_UserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '委托人',
  `F_UserName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '委托人名称',
  `F_Type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '委托类型（0-发起委托，1-审批委托）',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '流程委托' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_engine
-- ----------------------------
DROP TABLE IF EXISTS `flow_engine`;
CREATE TABLE `flow_engine`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程编码',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程名称',
  `F_Type` int(11) NULL DEFAULT NULL COMMENT '流程类型',
  `F_Category` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程分类',
  `F_Form` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程表单',
  `F_VisibleType` int(11) NULL DEFAULT NULL COMMENT '可见类型',
  `F_Icon` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程图标',
  `F_IconBackground` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图标背景色',
  `F_Version` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程版本',
  `F_FlowTemplateJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '流程模板',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_FormTemplateJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '表单模板',
  `F_FormType` int(11) NULL DEFAULT NULL COMMENT '表单分类',
  `F_Tables` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '关联的表',
  `F_DbLinkId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联数据连接id',
  `F_AppFormUrl` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'app表单路径',
  `F_FormUrl` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'pc表单路径',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '流程引擎' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_engineform
-- ----------------------------
DROP TABLE IF EXISTS `flow_engineform`;
CREATE TABLE `flow_engineform`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单编码',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单名称',
  `F_Category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单分类',
  `F_UrlAddress` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'Web地址',
  `F_AppUrlAddress` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'APP地址',
  `F_PropertyJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '属性字段',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_FlowType` int(11) NULL DEFAULT NULL COMMENT '流程类型',
  `F_FormType` int(11) NULL DEFAULT NULL COMMENT '表单类型',
  `F_InterfaceUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口路径',
  `F_DraftJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表单json草稿',
  `F_DbLinkId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联数据链接',
  `F_TableJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关联的表',
  `F_FlowId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '引擎id',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '流程表单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_engineform_relation
-- ----------------------------
DROP TABLE IF EXISTS `flow_engineform_relation`;
CREATE TABLE `flow_engineform_relation`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  `F_FormId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单id',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '流程表单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_enginevisible
-- ----------------------------
DROP TABLE IF EXISTS `flow_enginevisible`;
CREATE TABLE `flow_enginevisible`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_OperatorType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经办类型',
  `F_OperatorId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经办主键',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_TYPE` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '1.发起 2.协管',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '流程可见' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_rejectdata
-- ----------------------------
DROP TABLE IF EXISTS `flow_rejectdata`;
CREATE TABLE `flow_rejectdata`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TaskJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '任务json',
  `F_TaskNodeJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '节点json',
  `F_TaskOperatorJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '经办json',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '冻结审批' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_task
-- ----------------------------
DROP TABLE IF EXISTS `flow_task`;
CREATE TABLE `flow_task`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ProcessId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '实例进程',
  `F_EnCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务编号',
  `F_FullName` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程编号',
  `F_FlowName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程名称',
  `F_FlowType` int(11) NULL DEFAULT NULL COMMENT '流程分类',
  `F_FlowCategory` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程类型',
  `F_FlowForm` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '流程表单',
  `F_FlowFormContentJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '表单内容',
  `F_FlowTemplateJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '流程模板',
  `F_FlowVersion` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程版本',
  `F_StartTime` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_EndTime` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_ThisStep` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '当前步骤',
  `F_ThisStepId` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '当前步骤Id',
  `F_Grade` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '重要等级',
  `F_Status` int(11) NULL DEFAULT NULL COMMENT '任务状态',
  `F_Completion` int(11) NULL DEFAULT NULL COMMENT '完成情况',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_ParentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父流程id',
  `F_IsAsync` int(11) NULL DEFAULT NULL COMMENT '同步异步（0：同步，1：异步）',
  `F_IsBatch` int(11) NULL DEFAULT NULL COMMENT '是否批量（0：否，1：是）',
  `F_TaskNodeId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点主键',
  `F_FormType` int(11) NULL DEFAULT NULL COMMENT '表单分类',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_TemplateId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程模板id',
  `F_DelegateUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '委托用户',
  `F_RejectDataId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冻结审批',
  `F_Suspend` int(11) NULL DEFAULT NULL COMMENT '挂起任务状态',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '流程任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_taskcirculate
-- ----------------------------
DROP TABLE IF EXISTS `flow_taskcirculate`;
CREATE TABLE `flow_taskcirculate`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ObjectType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对象类型',
  `F_ObjectId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对象主键',
  `F_NodeCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点编号',
  `F_NodeName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点名称',
  `F_TaskNodeId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点主键',
  `F_TaskId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务主键',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '流程传阅' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_tasknode
-- ----------------------------
DROP TABLE IF EXISTS `flow_tasknode`;
CREATE TABLE `flow_tasknode`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_NodeCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点编号',
  `F_NodeName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点名称',
  `F_NodeType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点类型',
  `F_NodePropertyJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '节点属性Json',
  `F_NodeUp` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上一节点',
  `F_NodeNext` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `F_Completion` int(11) NULL DEFAULT NULL COMMENT '是否完成',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_TaskId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务主键',
  `F_State` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
  `F_Candidates` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '候选人',
  `F_CounterSign` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '依次审批',
  `F_DraftData` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '草稿数据',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_FormId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表单id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '流程节点' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_taskoperator
-- ----------------------------
DROP TABLE IF EXISTS `flow_taskoperator`;
CREATE TABLE `flow_taskoperator`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_HandleType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经办对象',
  `F_HandleId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经办主键',
  `F_HandleStatus` int(11) NULL DEFAULT NULL COMMENT '处理状态',
  `F_HandleTime` datetime NULL DEFAULT NULL COMMENT '处理时间',
  `F_NodeCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点编号',
  `F_NodeName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点名称',
  `F_Completion` int(11) NULL DEFAULT NULL COMMENT '是否完成',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_TaskNodeId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点主键',
  `F_TaskId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务主键',
  `F_Type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点类型',
  `F_State` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
  `F_ParentId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父节点id',
  `F_DraftData` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '草稿数据',
  `F_Automation` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '自动审批',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_RollbackId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '回滚id(初始前签有值，后签无值)',
  `F_Reject` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冻结审批人',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '流程经办' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_taskoperatorrecord
-- ----------------------------
DROP TABLE IF EXISTS `flow_taskoperatorrecord`;
CREATE TABLE `flow_taskoperatorrecord`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_NodeCode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点编号',
  `F_NodeName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点名称',
  `F_HandleStatus` int(11) NULL DEFAULT NULL COMMENT '经办状态',
  `F_HandleId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经办人员',
  `F_HandleTime` datetime NULL DEFAULT NULL COMMENT '经办时间',
  `F_HandleOpinion` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '经办理由',
  `F_TaskOperatorId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经办主键',
  `F_TaskNodeId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点主键',
  `F_TaskId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务主键',
  `F_SignImg` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '签名图片',
  `F_Status` int(11) NULL DEFAULT NULL COMMENT '审批标识',
  `F_OperatorId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流转操作人',
  `F_FileList` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '经办文件',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_DraftData` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '审批数据',
  `F_ApproverType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '加签类型',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '流程经办记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_taskoperatoruser
-- ----------------------------
DROP TABLE IF EXISTS `flow_taskoperatoruser`;
CREATE TABLE `flow_taskoperatoruser`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TaskId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务主键',
  `F_TaskNodeId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '节点主键',
  `F_HandleId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经办主键',
  `F_HandleType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经办对象',
  `F_NodeCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '节点编号',
  `F_NodeName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '节点名称',
  `F_Completion` int(11) NULL DEFAULT NULL COMMENT '是否完成',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '节点类型',
  `F_ParentId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父节点id',
  `F_Automation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自动审批',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_State` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_Reject` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '冻结审批人',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '流程依次审批' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_template
-- ----------------------------
DROP TABLE IF EXISTS `flow_template`;
CREATE TABLE `flow_template`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_EnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程编码',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程名称',
  `F_Type` int(11) NULL DEFAULT NULL COMMENT '流程类型',
  `F_Category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程分类',
  `F_VisibleType` int(11) NULL DEFAULT NULL COMMENT '可见类型',
  `F_Icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `F_IconBackground` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标背景色',
  `F_Description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '流程模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_templatejson
-- ----------------------------
DROP TABLE IF EXISTS `flow_templatejson`;
CREATE TABLE `flow_templatejson`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_TemplateId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程模板id',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程名称',
  `F_VisibleType` int(11) NULL DEFAULT NULL COMMENT '可见类型',
  `F_Version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程版本',
  `F_FlowTemplateJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '流程模板',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_GroupId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '流程模板实例' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flow_user
-- ----------------------------
DROP TABLE IF EXISTS `flow_user`;
CREATE TABLE `flow_user`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_OrganizeId` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '组织主键',
  `F_PositionId` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '岗位主键',
  `F_ManagerId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主管主键',
  `F_Superior` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上级用户',
  `F_Subordinate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '下属用户',
  `F_TaskId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务主键',
  `F_Department` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '公司下所有部门',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '流程发起用户信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt293714178051447621
-- ----------------------------
DROP TABLE IF EXISTS `mt293714178051447621`;
CREATE TABLE `mt293714178051447621`  (
  `comInputField115` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  `comInputField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `dateField102` datetime NULL DEFAULT NULL COMMENT '报名时间',
  `selectField111` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `comInputField110` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年纪',
  `comInputField104` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `comInputField112` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '籍贯',
  `comInputField113` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '家庭地址',
  `numInputField116` decimal(10, 0) NULL DEFAULT NULL COMMENT '报名费用',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt293732572561709893
-- ----------------------------
DROP TABLE IF EXISTS `mt293732572561709893`;
CREATE TABLE `mt293732572561709893`  (
  `billRuleField119` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  `comInputField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '购药人',
  `comInputField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品名称',
  `comInputField104` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `comInputField105` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '现住址',
  `comInputField106` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属街道社区',
  `comInputField107` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话',
  `radioField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否有重点地区旅居史',
  `radioField109` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '症状描述（是否发热、咳嗽）',
  `comInputField110` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作单位',
  `comInputField111` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '症状',
  `comInputField112` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营业员',
  `comInputField113` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人电话',
  `dateField114` datetime NULL DEFAULT NULL COMMENT '销售时间',
  `comInputField115` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药房名称',
  `comInputField116` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '许可证',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt293737725377420101
-- ----------------------------
DROP TABLE IF EXISTS `mt293737725377420101`;
CREATE TABLE `mt293737725377420101`  (
  `billRuleField110` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  `comInputField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `comInputField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号/护照',
  `uploadFzField109` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '身份证',
  `dateField105` datetime NULL DEFAULT NULL COMMENT '核酸检测时间',
  `comInputField106` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系方式（手机或座机）',
  `comInputField107` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '家庭住址',
  `selectField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所在社区',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt293740367726025541
-- ----------------------------
DROP TABLE IF EXISTS `mt293740367726025541`;
CREATE TABLE `mt293740367726025541`  (
  `billRuleField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品编码',
  `selectField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品分类',
  `comInputField116` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `selectField115` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品型号',
  `numInputField105` decimal(10, 0) NULL DEFAULT NULL COMMENT '采购单价',
  `numInputField106` decimal(10, 0) NULL DEFAULT NULL COMMENT '销售单价',
  `uploadImgField107` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '产品图片',
  `textareaField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品描述',
  `createUserField110` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人',
  `createTimeField111` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modifyTimeField113` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt293756710756064069
-- ----------------------------
DROP TABLE IF EXISTS `mt293756710756064069`;
CREATE TABLE `mt293756710756064069`  (
  `billRuleField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  `userSelectField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `dateField108` datetime NULL DEFAULT NULL COMMENT '申请时间',
  `dateField104` datetime NULL DEFAULT NULL COMMENT '用车时间',
  `dateField105` datetime NULL DEFAULT NULL COMMENT '归还时间',
  `textareaField107` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请备注',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt294012196562705413
-- ----------------------------
DROP TABLE IF EXISTS `mt294012196562705413`;
CREATE TABLE `mt294012196562705413`  (
  `comInputField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `radioField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `numInputField113` decimal(10, 0) NULL DEFAULT NULL COMMENT '年纪',
  `comInputField104` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `comInputField105` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `uploadFzField106` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '简历附件',
  `textareaField115` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '家庭地址',
  `comInputField107` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `comInputField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出生日期',
  `comInputField109` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '毕业院校',
  `comInputField110` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专业名称',
  `selectField112` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最高学历',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt294022926196317189
-- ----------------------------
DROP TABLE IF EXISTS `mt294022926196317189`;
CREATE TABLE `mt294022926196317189`  (
  `comInputField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `radioField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `selectField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '送诊',
  `comInputField104` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '送诊地址',
  `selectField105` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发热',
  `selectField106` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件类型',
  `comInputField107` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件号',
  `comInputField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `comInputField109` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员状态',
  `textareaField110` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员状态备注',
  `addressField111` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '区县',
  `comInputField153` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '街道',
  `comInputField114` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '社区',
  `comInputField117` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体地址',
  `selectField119` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管控状态',
  `selectField120` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管控隔离类型',
  `selectField121` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员类型（一级）',
  `selectField122` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员类型（二级）',
  `comInputField123` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密接相关人',
  `dateField125` datetime NULL DEFAULT NULL COMMENT '返回时间',
  `selectField126` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交通方式',
  `textareaField127` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '返回后活动路径',
  `comInputField128` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '返回经过地',
  `comInputField129` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '同行人员',
  `comInputField130` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接触人员',
  `dateField131` datetime NULL DEFAULT NULL COMMENT '填写时间',
  `comInputField132` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '机构名称',
  `comInputField133` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '机构编号',
  `selectField134` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '社区/农村',
  `dateField135` datetime NULL DEFAULT NULL COMMENT '离开日期',
  `addressField138` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '返回前前出发地',
  `comInputField139` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核酸检测',
  `comInputField140` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第一次检测结果',
  `comInputField141` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第二次检测结果',
  `selectField142` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否才去信息化管控',
  `dateField143` datetime NULL DEFAULT NULL COMMENT '本次管控日期',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt294027801932110853
-- ----------------------------
DROP TABLE IF EXISTS `mt294027801932110853`;
CREATE TABLE `mt294027801932110853`  (
  `comInputField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `comInputField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工号',
  `currOrganizeField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门',
  `numInputField104` decimal(10, 0) NULL DEFAULT NULL COMMENT '基本工资',
  `numInputField105` decimal(10, 0) NULL DEFAULT NULL COMMENT '岗位工资',
  `numInputField106` decimal(10, 0) NULL DEFAULT NULL COMMENT '补贴金额',
  `numInputField107` decimal(10, 0) NULL DEFAULT NULL COMMENT '绩效/每月',
  `numInputField108` decimal(10, 0) NULL DEFAULT NULL COMMENT '迟到早退扣款',
  `numInputField109` decimal(10, 0) NULL DEFAULT NULL COMMENT '病假扣款',
  `numInputField110` decimal(10, 0) NULL DEFAULT NULL COMMENT '事假扣款',
  `numInputField111` decimal(10, 0) NULL DEFAULT NULL COMMENT '旷工扣款',
  `numInputField112` decimal(10, 0) NULL DEFAULT NULL COMMENT '应发工资',
  `numInputField113` decimal(10, 0) NULL DEFAULT NULL COMMENT '个人养老保险',
  `numInputField114` decimal(10, 0) NULL DEFAULT NULL COMMENT '个人医疗保险',
  `numInputField115` decimal(10, 0) NULL DEFAULT NULL COMMENT '个人事业险',
  `numInputField116` decimal(10, 0) NULL DEFAULT NULL COMMENT '个人大病险',
  `numInputField117` decimal(10, 0) NULL DEFAULT NULL COMMENT '个人公积金',
  `numInputField118` decimal(10, 0) NULL DEFAULT NULL COMMENT '累计收入',
  `numInputField119` decimal(10, 0) NULL DEFAULT NULL COMMENT '累计扣减个税',
  `numInputField120` decimal(10, 0) NULL DEFAULT NULL COMMENT '累计应纳税所得额',
  `numInputField121` decimal(10, 0) NULL DEFAULT NULL COMMENT '本期个税',
  `textareaField122` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt294031961024928773
-- ----------------------------
DROP TABLE IF EXISTS `mt294031961024928773`;
CREATE TABLE `mt294031961024928773`  (
  `selectField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估周期',
  `userSelectField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估人',
  `comInputField118` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工号',
  `depSelectField105` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt294037561750363141
-- ----------------------------
DROP TABLE IF EXISTS `mt294037561750363141`;
CREATE TABLE `mt294037561750363141`  (
  `comInputField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备名称',
  `comInputField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备编号',
  `comInputField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所在位置',
  `userSelectField104` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '责任人',
  `comInputField105` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `createUserField126` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人',
  `createTimeField123` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modifyTimeField127` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `uploadImgField106` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '设备图片',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `f_version` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `f_flowtaskid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程任务主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt294090217118276613
-- ----------------------------
DROP TABLE IF EXISTS `mt294090217118276613`;
CREATE TABLE `mt294090217118276613`  (
  `selectField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上报部门',
  `selectField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药剂科分部门',
  `dateField104` datetime NULL DEFAULT NULL COMMENT '上报时间',
  `dateField105` datetime NULL DEFAULT NULL COMMENT '差错时间',
  `comInputField106` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '病历/门诊号',
  `comInputField107` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者姓名',
  `selectField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `comInputField109` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年龄',
  `comInputField110` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '诊断',
  `comInputField111` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发现人员',
  `comInputField112` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '差错人员',
  `dateField129` datetime NULL DEFAULT NULL COMMENT '发生日期',
  `comInputField113` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药品名称',
  `userSelectField125` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当前处理人',
  `selectField124` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程状态',
  `textareaField114` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '差错详情',
  `textareaField115` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '建议改进措施',
  `textareaField126` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理结果',
  `comInputField130` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来文号',
  `dateField131` datetime NULL DEFAULT NULL COMMENT '来文日期',
  `selectField127` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '环节',
  `selectField128` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原因',
  `selectField121` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事件等级',
  `selectField122` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事件级别',
  `uploadFzField123` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附件',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt294099237023526085
-- ----------------------------
DROP TABLE IF EXISTS `mt294099237023526085`;
CREATE TABLE `mt294099237023526085`  (
  `userSelectField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户选择',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt294099893327272965
-- ----------------------------
DROP TABLE IF EXISTS `mt294099893327272965`;
CREATE TABLE `mt294099893327272965`  (
  `userSelectField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出差申请人',
  `currOrganizeField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `dateField104` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `dateField105` datetime NULL DEFAULT NULL COMMENT '出差日期',
  `dateField106` datetime NULL DEFAULT NULL COMMENT '归来日期',
  `createUserField112` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `createTimeField113` datetime NULL DEFAULT NULL COMMENT '申请时间',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt294104577349819397
-- ----------------------------
DROP TABLE IF EXISTS `mt294104577349819397`;
CREATE TABLE `mt294104577349819397`  (
  `depSelectField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门',
  `dateField103` datetime NULL DEFAULT NULL COMMENT '当前时间',
  `dateField104` datetime NULL DEFAULT NULL COMMENT '预算年份',
  `dateField105` datetime NULL DEFAULT NULL COMMENT '预算月份',
  `numInputField107` decimal(10, 0) NULL DEFAULT NULL COMMENT '预算金额',
  `comInputField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '金额大写',
  `textareaField109` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `createUserField110` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `createTimeField111` datetime NULL DEFAULT NULL COMMENT '申请时间',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt294376098702073541
-- ----------------------------
DROP TABLE IF EXISTS `mt294376098702073541`;
CREATE TABLE `mt294376098702073541`  (
  `billRuleField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内部订单号',
  `comInputField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `comInputField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `textareaField104` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt294382866144468677
-- ----------------------------
DROP TABLE IF EXISTS `mt294382866144468677`;
CREATE TABLE `mt294382866144468677`  (
  `billRuleField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单号',
  `comInputField104` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商',
  `selectField105` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '仓库',
  `depSelectField106` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购部门',
  `comInputField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门代号',
  `userSelectField109` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务负责人',
  `uploadFzField119` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '发票上传',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt382811547782648325
-- ----------------------------
DROP TABLE IF EXISTS `mt382811547782648325`;
CREATE TABLE `mt382811547782648325`  (
  `comInputField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单行输入',
  `textareaField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '多行输入',
  `numInputField103` decimal(65, 0) NULL DEFAULT NULL COMMENT '数字输入',
  `f_flowid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程引擎id',
  `f_version` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `f_flowtaskid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程任务主键',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '0111流程表单-主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt385453315686752389
-- ----------------------------
DROP TABLE IF EXISTS `mt385453315686752389`;
CREATE TABLE `mt385453315686752389`  (
  `depSelectField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门',
  `dateField103` datetime NULL DEFAULT NULL COMMENT '当前时间',
  `dateField104` datetime NULL DEFAULT NULL COMMENT '预算年份',
  `dateField105` datetime NULL DEFAULT NULL COMMENT '预算月份',
  `numInputField107` decimal(65, 0) NULL DEFAULT NULL COMMENT '预算金额',
  `comInputField108` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '金额大写',
  `textareaField109` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `createUserField110` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `createTimeField111` datetime NULL DEFAULT NULL COMMENT '申请时间',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  `f_version` bigint(20) NULL DEFAULT NULL COMMENT '乐观锁',
  `f_flowtaskid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程任务主键',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '预算管理-主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt395179351563312389
-- ----------------------------
DROP TABLE IF EXISTS `mt395179351563312389`;
CREATE TABLE `mt395179351563312389`  (
  `pid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上级',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `encode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  `f_version` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `f_flowtaskid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程任务主键',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '列表树形表格-主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt395899725691230469
-- ----------------------------
DROP TABLE IF EXISTS `mt395899725691230469`;
CREATE TABLE `mt395899725691230469`  (
  `comInputField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设计表单名称显示',
  `comInputField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '控件类型中文名',
  `comInputField106` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'css示例添加',
  `selectField121` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '添加多个快捷入口',
  `uploadFzField107` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '支持自定义路径和分用户存储',
  `uploadFzField108` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '支持文件类型多选',
  `comInputField118` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '打印模板多选',
  `comInputField117` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据条件过滤',
  `comInputField119` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批量打印/下载',
  `comInputField120` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义按钮调整',
  `comInputField122` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保存并新增',
  `comInputField123` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支持控件多选查询',
  `treeSelectField126` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列表树形表格',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  `f_version` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `f_flowtaskid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程任务主键',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '3.4.6新增功能汇总示例-主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt395964134161651973
-- ----------------------------
DROP TABLE IF EXISTS `mt395964134161651973`;
CREATE TABLE `mt395964134161651973`  (
  `relationFormField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `popupSelectField104` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员名称',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  `f_version` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `f_flowtaskid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程任务主键',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '列表数据链接查看详情页-主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt400655094485373381
-- ----------------------------
DROP TABLE IF EXISTS `mt400655094485373381`;
CREATE TABLE `mt400655094485373381`  (
  `comInputField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单行输入',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  `f_version` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `f_flowtaskid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程任务主键',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'cccccc-主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mt420589912199274117
-- ----------------------------
DROP TABLE IF EXISTS `mt420589912199274117`;
CREATE TABLE `mt420589912199274117`  (
  `selectField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省',
  `selectField102` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '市',
  `selectField103` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  `f_version` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `f_flowtaskid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程任务主键',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '行政区划联动-主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_charts
-- ----------------------------
DROP TABLE IF EXISTS `report_charts`;
CREATE TABLE `report_charts`  (
  `ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `QYBM` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `FXDMC` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `FXDJ` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PGRY` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PGRQ` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `FXFXFF` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `LECE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `LECC` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `LSL` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `LSS` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PGFXZ` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `FPRQ` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `STATUS` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `createDate` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '报表图表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_department
-- ----------------------------
DROP TABLE IF EXISTS `report_department`;
CREATE TABLE `report_department`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `departmentName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `departmentNum` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `organizationName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报表部门示例' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_user
-- ----------------------------
DROP TABLE IF EXISTS `report_user`;
CREATE TABLE `report_user`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `education` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sex` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `salary` decimal(10, 2) NULL DEFAULT NULL,
  `departmentnum` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `peoplenum` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `month` int(2) NULL DEFAULT NULL,
  `year` int(4) NULL DEFAULT NULL,
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报表用户示例' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_carapplication
-- ----------------------------
DROP TABLE IF EXISTS `test_carapplication`;
CREATE TABLE `test_carapplication`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Applicationno` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请单号',
  `F_Applicant` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_Applicantdate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `f_Destination` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '目的地',
  `F_Startdate` datetime NULL DEFAULT NULL COMMENT '起始时间',
  `F_Enddate` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_Vehiclenumber` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆号码',
  `F_Driver` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '驾驶员',
  `F_Estimated` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预计里程/KM',
  `F_Entourage` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '随行人员',
  `F_Reasons` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用车事由',
  `F_Remarks` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用车申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_cost
-- ----------------------------
DROP TABLE IF EXISTS `test_cost`;
CREATE TABLE `test_cost`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Reimbursementid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表主键',
  `F_Cost_classes` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '费用类别',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '报销金额',
  `F_Remarks` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '费用明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_customer
-- ----------------------------
DROP TABLE IF EXISTS `test_customer`;
CREATE TABLE `test_customer`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_SortCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '序号',
  `F_Depname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_Username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务员姓名',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户类型',
  `F_Grade` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户等级',
  `F_Customername` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户全称',
  `F_Leveladdress` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省份',
  `F_Channel` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道全称',
  `F_Trade` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '行业',
  `F_Nature` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业性质',
  `F_Model` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业规模',
  `F_Address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业地址（详细地址）',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '大客户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_details
-- ----------------------------
DROP TABLE IF EXISTS `test_details`;
CREATE TABLE `test_details`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Receivableid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表主键',
  `F_Month` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费月份',
  `F_Rent` decimal(18, 2) NULL DEFAULT NULL COMMENT '房租',
  `F_Water_rent` decimal(18, 2) NULL DEFAULT NULL COMMENT '水费',
  `F_Powerrate` decimal(18, 2) NULL DEFAULT NULL COMMENT '电费',
  `F_Property` decimal(18, 2) NULL DEFAULT NULL COMMENT '物业费',
  `F_Cleaning` decimal(18, 2) NULL DEFAULT NULL COMMENT '垃圾费',
  `F_Pollutant` decimal(18, 2) NULL DEFAULT NULL COMMENT '排污费',
  `F_Totalbilling` decimal(18, 2) NULL DEFAULT NULL COMMENT '累计应收费用',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '应收费用明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_position
-- ----------------------------
DROP TABLE IF EXISTS `test_position`;
CREATE TABLE `test_position`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Applys` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_Dep_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_Station_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '岗位名称',
  `F_Arrival_Date` date NULL DEFAULT NULL COMMENT '期望到岗日期',
  `F_Drade` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优先级',
  `F_Worknature` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作性质',
  `F_Stationpay` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '岗位平均薪资',
  `F_Add_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '增补原因',
  `F_Education` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最低学历',
  `F_Gender` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `F_Working_seniority` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作经验年限',
  `F_Marriage` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '婚姻状况',
  `F_Age` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年龄范围',
  `F_Stationpays` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '薪资范围',
  `F_Duty` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职责描述',
  `F_Stationask` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '岗位要求',
  `F_Stationkey` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '岗位关键字',
  `F_Creator_Time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_Creator_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModify_Time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModify_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '招聘表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_projects
-- ----------------------------
DROP TABLE IF EXISTS `test_projects`;
CREATE TABLE `test_projects`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_Projectname` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名称',
  `F_Projecttype` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目类型',
  `F_Projectcode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目编号',
  `F_Projectfunding` decimal(18, 2) NULL DEFAULT NULL COMMENT '项目经费',
  `F_Depname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责部门',
  `F_Person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `F_Degree` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目密级',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `billno` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单据规则',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目立项申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_purchase
-- ----------------------------
DROP TABLE IF EXISTS `test_purchase`;
CREATE TABLE `test_purchase`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Purchase_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购单号',
  `F_Sort_Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '排序',
  `F_Applys` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购申请人',
  `F_Dep_Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_Purchase` datetime NULL DEFAULT NULL COMMENT '采购申请日期',
  `F_Reasons` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购原因',
  `F_Remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_Descz` bigint(20) NULL DEFAULT NULL,
  `F_sasa` time NULL DEFAULT NULL,
  `F_asad` int(11) NULL DEFAULT NULL,
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_purchase_form
-- ----------------------------
DROP TABLE IF EXISTS `test_purchase_form`;
CREATE TABLE `test_purchase_form`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_Supplier_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商识别码',
  `F_SortCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '排序',
  `F_Buyer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购员',
  `F_Depname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_Purchase_Date` datetime NULL DEFAULT NULL COMMENT '采购日期',
  `F_Statuss` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入库状态',
  `F_Totals` decimal(38, 0) NULL DEFAULT NULL COMMENT '订单合计金额',
  `F_Supplier` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商对接人',
  `F_Phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `F_Add_ress` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货地址',
  `F_Creator_Time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_purchase_form_order
-- ----------------------------
DROP TABLE IF EXISTS `test_purchase_form_order`;
CREATE TABLE `test_purchase_form_order`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Purchase_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表主键',
  `F_Product` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品',
  `F_Product_name_2` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `F_Product_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品编码',
  `F_Product_Category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品分类',
  `F_Specifications` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格',
  `F_Unit` decimal(18, 2) NULL DEFAULT NULL COMMENT '单位',
  `F_Unitprice` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Discount` decimal(18, 2) NULL DEFAULT NULL COMMENT '折扣（%）',
  `F_Actualunit_price_name` decimal(18, 2) NULL DEFAULT NULL COMMENT '实际单价（元）',
  `F_Numbers` decimal(18, 2) NULL DEFAULT NULL COMMENT '数量',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购订单明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_purchaseorder
-- ----------------------------
DROP TABLE IF EXISTS `test_purchaseorder`;
CREATE TABLE `test_purchaseorder`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Purchaseid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表主键',
  `F_Product` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品',
  `F_Product_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `F_Product_Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品编码',
  `F_Product_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品分类',
  `F_Specifications` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格',
  `F_Unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'F_Unit',
  `F_Numbers` decimal(18, 2) NULL DEFAULT NULL COMMENT '数量',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购单明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_receivable
-- ----------------------------
DROP TABLE IF EXISTS `test_receivable`;
CREATE TABLE `test_receivable`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Building` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '楼宇号',
  `F_Room` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房间号',
  `F_Company_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司名称',
  `F_Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对接人',
  `F_Phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `F_Ost` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职务',
  `F_Sort_Code` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_version` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `f_flowtaskid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程任务主键',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '应收费用主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_reimbursement
-- ----------------------------
DROP TABLE IF EXISTS `test_reimbursement`;
CREATE TABLE `test_reimbursement`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Reimburse_ment` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报销单号',
  `F_Reimbursement_date_AP` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名称',
  `F_Userid` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报销人',
  `F_Dep_Name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_Totala_Mount` decimal(18, 2) NULL DEFAULT NULL COMMENT '总金额',
  `F_Reasons` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报销事由',
  `F_Remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModify_Time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModify_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '财务报销信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_subpackage
-- ----------------------------
DROP TABLE IF EXISTS `test_subpackage`;
CREATE TABLE `test_subpackage`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Sort_Code` bigint(20) NULL DEFAULT NULL COMMENT '序号',
  `F_Applys` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '填单人',
  `F_Arrival_Date` datetime NULL DEFAULT NULL COMMENT '填单日期',
  `F_Project_s` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属项目',
  `F_Dep_name_A` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_Contractor` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '承包方',
  `F_Signer` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同签订人',
  `F_Firstparty` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `F_Contract` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同',
  `F_Contract_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
  `F_Contract_date` datetime NULL DEFAULT NULL COMMENT '合同签订日期',
  `F_Start_date_AA` datetime NULL DEFAULT NULL COMMENT '开始日期',
  `F_Enddate_QP` datetime NULL DEFAULT NULL COMMENT '结束日期',
  `F_Contract_money` decimal(18, 2) NULL DEFAULT NULL COMMENT '合同金额',
  `F_Remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `F_fujian` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附件',
  `F_Invoiced` decimal(18, 2) NULL DEFAULT NULL COMMENT '已付款金额',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分包合同表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_vehicleinfo
-- ----------------------------
DROP TABLE IF EXISTS `test_vehicleinfo`;
CREATE TABLE `test_vehicleinfo`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_SortCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '序号',
  `F_Numberplate` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆号牌',
  `F_Brand` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆品牌',
  `F_Model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆型号',
  `F_Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆类型',
  `F_Colour` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车身颜色',
  `F_Number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '乘坐人数',
  `F_Purchaseprice` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '购置价格',
  `F_Purchasedate` datetime NULL DEFAULT NULL COMMENT '购置日期',
  `F_Subordinate` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属领导',
  `F_Depname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_EnableMark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆状态',
  `F_Mileagetraveled` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '已行驶里程/KM',
  `F_Credentials` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '随车证件',
  `F_Remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息',
  `F_CreatorTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '车辆信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_warehousing
-- ----------------------------
DROP TABLE IF EXISTS `test_warehousing`;
CREATE TABLE `test_warehousing`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Purchase_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入库单号',
  `F_Sort_Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '排序',
  `F_Applys` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入库员',
  `F_Dep_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_Purchase` datetime NULL DEFAULT NULL COMMENT '入库日期',
  `F_Creator_Time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_Creator_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModify_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购入库表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_warehousingorder
-- ----------------------------
DROP TABLE IF EXISTS `test_warehousingorder`;
CREATE TABLE `test_warehousingorder`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_Purchaseid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表主键',
  `F_Product` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品',
  `F_Product_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `F_Product_Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品编码',
  `F_Product_category_bb` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品分类',
  `F_Specifications` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格',
  `F_Unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位',
  `F_Actualunitprice` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Numbers` decimal(18, 2) NULL DEFAULT NULL COMMENT '采购数量',
  `F_Creator_Time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '入库单明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for undo_log
-- ----------------------------
DROP TABLE IF EXISTS `undo_log`;
CREATE TABLE `undo_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `branch_id` bigint(20) NOT NULL,
  `xid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `context` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `rollback_info` longblob NOT NULL,
  `log_status` int(11) NOT NULL,
  `log_created` datetime NULL DEFAULT NULL,
  `log_modified` datetime NULL DEFAULT NULL,
  `ext` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ux_undo_log`(`xid`, `branch_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '事务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_applybanquet
-- ----------------------------
DROP TABLE IF EXISTS `wform_applybanquet`;
CREATE TABLE `wform_applybanquet`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人员',
  `F_Position` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属职务',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_BanquetNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '宴请人数',
  `F_BanquetPeople` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '宴请人员',
  `F_Total` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '人员总数',
  `F_Place` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '宴请地点',
  `F_ExpectedCost` decimal(18, 2) NULL DEFAULT NULL COMMENT '预计费用',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '宴请申请' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_applydelivergoods
-- ----------------------------
DROP TABLE IF EXISTS `wform_applydelivergoods`;
CREATE TABLE `wform_applydelivergoods`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_CustomerName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `F_Contacts` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系人',
  `F_ContactPhone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `F_CustomerAddres` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户地址',
  `F_GoodsBelonged` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '货品所属',
  `F_InvoiceDate` datetime NULL DEFAULT NULL COMMENT '发货日期',
  `F_FreightCompany` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '货运公司',
  `F_DeliveryType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发货类型',
  `F_RransportNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '货运单号',
  `F_FreightCharges` decimal(18, 2) NULL DEFAULT NULL COMMENT '货运费',
  `F_CargoInsurance` decimal(18, 2) NULL DEFAULT NULL COMMENT '保险金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_InvoiceValue` decimal(18, 2) NULL DEFAULT NULL COMMENT '发货金额',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '发货申请单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_applydelivergoodsentry
-- ----------------------------
DROP TABLE IF EXISTS `wform_applydelivergoodsentry`;
CREATE TABLE `wform_applydelivergoodsentry`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_InvoiceId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发货主键',
  `F_GoodsName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `F_Specifications` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
  `F_Unit` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '单位',
  `F_Qty` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数量',
  `F_Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '发货明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_applymeeting
-- ----------------------------
DROP TABLE IF EXISTS `wform_applymeeting`;
CREATE TABLE `wform_applymeeting`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_Position` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属职务',
  `F_ConferenceName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会议名称',
  `F_ConferenceTheme` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会议主题',
  `F_ConferenceType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会议类型',
  `F_EstimatePeople` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预计人数',
  `F_ConferenceRoom` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '会议室',
  `F_Administrator` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '管理人',
  `F_LookPeople` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '查看人',
  `F_Memo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '纪要员',
  `F_Attendees` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出席人',
  `F_ApplyMaterial` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请材料',
  `F_EstimatedAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '预计金额',
  `F_OtherAttendee` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '其他出席人',
  `F_StartDate` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_EndDate` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_Describe` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '会议描述',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '会议申请' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_archivalborrow
-- ----------------------------
DROP TABLE IF EXISTS `wform_archivalborrow`;
CREATE TABLE `wform_archivalborrow`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_BorrowingDepartment` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '借阅部门',
  `F_ArchivesName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '档案名称',
  `F_ArchivalAttributes` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '档案属性',
  `F_BorrowMode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '借阅方式',
  `F_ApplyReason` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '申请原因',
  `F_ArchivesId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '档案编号',
  `F_BorrowingDate` datetime NULL DEFAULT NULL COMMENT '借阅时间',
  `F_ReturnDate` datetime NULL DEFAULT NULL COMMENT '归还时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '档案借阅申请' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_articleswarehous
-- ----------------------------
DROP TABLE IF EXISTS `wform_articleswarehous`;
CREATE TABLE `wform_articleswarehous`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_Department` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_Articles` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用品库存',
  `F_Classification` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用品分类',
  `F_ArticlesId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用品编号',
  `F_Company` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位',
  `F_EstimatePeople` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数量',
  `F_ApplyReasons` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '申请原因',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用品入库申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_batchpack
-- ----------------------------
DROP TABLE IF EXISTS `wform_batchpack`;
CREATE TABLE `wform_batchpack`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ProductName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `F_Production` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生产车间',
  `F_Compactor` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编制人员',
  `F_CompactorDate` datetime NULL DEFAULT NULL COMMENT '编制日期',
  `F_Standard` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '产品规格',
  `F_WarehousNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入库序号',
  `F_ProductionQuty` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '批产数量',
  `F_OperationDate` datetime NULL DEFAULT NULL COMMENT '操作日期',
  `F_Regulations` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工艺规程',
  `F_Packing` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '包装规格',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '批包装指令' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_batchtable
-- ----------------------------
DROP TABLE IF EXISTS `wform_batchtable`;
CREATE TABLE `wform_batchtable`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_FileTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件标题',
  `F_DraftedPerson` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主办单位',
  `F_FillNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件编号',
  `F_SendUnit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发往单位',
  `F_Typing` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '打字',
  `F_WritingDate` datetime NULL DEFAULT NULL COMMENT '发文日期',
  `F_ShareNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '份数',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '行文呈批表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_conbilling
-- ----------------------------
DROP TABLE IF EXISTS `wform_conbilling`;
CREATE TABLE `wform_conbilling`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_Drawer` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开票人',
  `F_BillDate` datetime NULL DEFAULT NULL COMMENT '开票日期',
  `F_CompanyName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司名称',
  `F_ConName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联名称',
  `F_Bank` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开户银行',
  `F_Amount` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开户账号',
  `F_BillAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '开票金额',
  `F_TaxId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '税号',
  `F_InvoiceId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发票号',
  `F_InvoAddress` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发票地址',
  `F_PayAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '付款金额',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '合同开票流程' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_contractapproval
-- ----------------------------
DROP TABLE IF EXISTS `wform_contractapproval`;
CREATE TABLE `wform_contractapproval`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_FirstPartyUnit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '甲方单位',
  `F_SecondPartyUnit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '乙方单位',
  `F_FirstPartyPerson` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '甲方负责人',
  `F_SecondPartyPerson` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '乙方负责人',
  `F_FirstPartyContact` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '甲方联系方式',
  `F_SecondPartyContact` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '乙方联系方式',
  `F_ContractName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同名称',
  `F_ContractClass` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同分类',
  `F_ContractType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同类型',
  `F_ContractId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同编号',
  `F_BusinessPerson` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务人员',
  `F_IncomeAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '收入金额',
  `F_InputPerson` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '填写人员',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_PrimaryCoverage` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '主要内容',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_SigningDate` datetime NULL DEFAULT NULL COMMENT '签约时间',
  `F_StartDate` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_EndDate` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '合同审批' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_contractapprovalsheet
-- ----------------------------
DROP TABLE IF EXISTS `wform_contractapprovalsheet`;
CREATE TABLE `wform_contractapprovalsheet`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_ContractId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编号支出',
  `F_ContractNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同号',
  `F_FirstParty` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '签署方(甲方)',
  `F_SecondParty` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '乙方',
  `F_ContractName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同名称',
  `F_ContractType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同类型',
  `F_PersonCharge` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合作负责人',
  `F_LeadDepartment` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_SignArea` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '签订地区',
  `F_IncomeAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '收入金额',
  `F_TotalExpenditure` decimal(18, 2) NULL DEFAULT NULL COMMENT '支出总额',
  `F_ContractPeriod` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同期限',
  `F_PaymentMethod` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '付款方式',
  `F_BudgetaryApproval` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预算批付',
  `F_StartContractDate` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_EndContractDate` datetime NULL DEFAULT NULL,
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_ContractContent` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '内容简要',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '合同申请单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_debitbill
-- ----------------------------
DROP TABLE IF EXISTS `wform_debitbill`;
CREATE TABLE `wform_debitbill`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_Departmental` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作部门',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_StaffName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工姓名',
  `F_StaffPost` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工职务',
  `F_StaffId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工编号',
  `F_LoanMode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '借款方式',
  `F_AmountDebit` decimal(18, 2) NULL DEFAULT NULL COMMENT '借支金额',
  `F_TransferAccount` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '转账账户',
  `F_RepaymentBill` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '还款票据',
  `F_TeachingDate` datetime NULL DEFAULT NULL COMMENT '还款日期',
  `F_PaymentMethod` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付方式',
  `F_Reason` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '借款原因',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '借支单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_documentapproval
-- ----------------------------
DROP TABLE IF EXISTS `wform_documentapproval`;
CREATE TABLE `wform_documentapproval`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_FileName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件名称',
  `F_DraftedPerson` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拟稿人',
  `F_ServiceUnit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发文单位',
  `F_FillPreparation` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件拟办',
  `F_FillNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件编号',
  `F_ReceiptDate` datetime NULL DEFAULT NULL COMMENT '收文日期',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_ModifyOpinion` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修改意见',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文件签批意见表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_documentsigning
-- ----------------------------
DROP TABLE IF EXISTS `wform_documentsigning`;
CREATE TABLE `wform_documentsigning`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_FileName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件名称',
  `F_FillNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件编号',
  `F_DraftedPerson` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拟稿人',
  `F_Reader` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '签阅人',
  `F_FillPreparation` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件拟办',
  `F_CheckDate` datetime NULL DEFAULT NULL COMMENT '签阅时间',
  `F_PublicationDate` datetime NULL DEFAULT NULL COMMENT '发稿日期',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_DocumentContent` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '文件内容',
  `F_AdviceColumn` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '建议栏',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文件签阅表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_expenseexpenditure
-- ----------------------------
DROP TABLE IF EXISTS `wform_expenseexpenditure`;
CREATE TABLE `wform_expenseexpenditure`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '流程等级',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人员',
  `F_Department` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所在部门',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_ContractNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同编号',
  `F_NonContract` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '非合同支出',
  `F_AccountOpeningBank` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开户银行',
  `F_BankAccount` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '银行账号',
  `F_OpenAccount` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开户姓名',
  `F_Total` decimal(18, 2) NULL DEFAULT NULL COMMENT '合计费用',
  `F_PaymentMethod` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付方式',
  `F_AmountPayment` decimal(18, 2) NULL DEFAULT NULL COMMENT '支付金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '费用支出单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_finishedproduct
-- ----------------------------
DROP TABLE IF EXISTS `wform_finishedproduct`;
CREATE TABLE `wform_finishedproduct`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_WarehouseName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入库人',
  `F_Warehouse` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '仓库',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_ReservoirDate` datetime NULL DEFAULT NULL COMMENT '入库时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '成品入库单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_finishedproductentry
-- ----------------------------
DROP TABLE IF EXISTS `wform_finishedproductentry`;
CREATE TABLE `wform_finishedproductentry`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_WarehouseId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入库单号',
  `F_GoodsName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `F_Specifications` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
  `F_Unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位',
  `F_Qty` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数量',
  `F_Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序号',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '成品入库单明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_incomerecognition
-- ----------------------------
DROP TABLE IF EXISTS `wform_incomerecognition`;
CREATE TABLE `wform_incomerecognition`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主题',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_SettlementMonth` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '结算月份',
  `F_CustomerName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `F_ContractNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同编号',
  `F_TotalAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '合同金额',
  `F_MoneyBank` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '到款银行',
  `F_ActualAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '到款金额',
  `F_ContactName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系人姓名',
  `F_ContacPhone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `F_ContactQQ` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系QQ',
  `F_UnpaidAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '未付金额',
  `F_AmountPaid` decimal(18, 2) NULL DEFAULT NULL COMMENT '已付金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_PaymentDate` datetime NULL DEFAULT NULL COMMENT '到款日期\r\n',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '收入确认分析表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_leaveapply
-- ----------------------------
DROP TABLE IF EXISTS `wform_leaveapply`;
CREATE TABLE `wform_leaveapply`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单据编号',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人员',
  `F_ApplyDept` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请部门',
  `F_ApplyPost` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请职位',
  `F_LeaveType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请假类别',
  `F_LeaveReason` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '请假原因',
  `F_LeaveDayCount` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请假天数',
  `F_LeaveHour` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请假小时',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_LeaveStartTime` datetime NULL DEFAULT NULL COMMENT '请假时间',
  `F_LeaveEndTime` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '流程表单【请假申请】' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_letterservice
-- ----------------------------
DROP TABLE IF EXISTS `wform_letterservice`;
CREATE TABLE `wform_letterservice`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_HostUnit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主办单位',
  `F_Title` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发文标题',
  `F_IssuedNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发文字号',
  `F_WritingDate` datetime NULL DEFAULT NULL COMMENT '发文日期',
  `F_ShareNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '份数',
  `F_MainDelivery` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主送',
  `F_Copy` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '抄送',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '发文单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_materialrequisition
-- ----------------------------
DROP TABLE IF EXISTS `wform_materialrequisition`;
CREATE TABLE `wform_materialrequisition`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_LeadPeople` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '领料人',
  `F_LeadDepartment` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '领料部门',
  `F_LeadDate` datetime NULL DEFAULT NULL COMMENT '领料日期',
  `F_Warehouse` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '仓库',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '领料单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_materialrequisitionentry
-- ----------------------------
DROP TABLE IF EXISTS `wform_materialrequisitionentry`;
CREATE TABLE `wform_materialrequisitionentry`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_LeadeId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '领料主键',
  `F_GoodsName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `F_Specifications` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
  `F_Unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位',
  `F_MaterialDemand` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '需数量',
  `F_Proportioning` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '配数量',
  `F_Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '领料明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_monthlyreport
-- ----------------------------
DROP TABLE IF EXISTS `wform_monthlyreport`;
CREATE TABLE `wform_monthlyreport`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `F_ApplyDept` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_ApplyPost` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属职务',
  `F_PlanEndTime` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `F_OveralEvaluat` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '总体评价',
  `F_NPWorkMatter` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作事项',
  `F_NPFinishTime` datetime NULL DEFAULT NULL COMMENT '次月日期',
  `F_NFinishMethod` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '次月目标',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '月工作总结' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_officesupplies
-- ----------------------------
DROP TABLE IF EXISTS `wform_officesupplies`;
CREATE TABLE `wform_officesupplies`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请部门',
  `F_Department` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_UseStock` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '领用仓库',
  `F_Classification` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用品分类',
  `F_ArticlesName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用品名称',
  `F_ArticlesNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用品数量',
  `F_ArticlesId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用品编号',
  `F_ApplyReasons` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '申请原因',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请时间',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '领用办公用品申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_outboundorder
-- ----------------------------
DROP TABLE IF EXISTS `wform_outboundorder`;
CREATE TABLE `wform_outboundorder`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_CustomerName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `F_Warehouse` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '仓库',
  `F_OutStorage` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '仓库人',
  `F_BusinessPeople` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务人员',
  `F_BusinessType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务类型',
  `F_OutboundDate` datetime NULL DEFAULT NULL COMMENT '出库日期',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '出库单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_outboundorderentry
-- ----------------------------
DROP TABLE IF EXISTS `wform_outboundorderentry`;
CREATE TABLE `wform_outboundorderentry`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_OutboundId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出库单号',
  `F_GoodsName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `F_Specifications` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
  `F_Unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位',
  `F_Qty` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数量',
  `F_Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '出库单明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_outgoingapply
-- ----------------------------
DROP TABLE IF EXISTS `wform_outgoingapply`;
CREATE TABLE `wform_outgoingapply`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_Department` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所在部门',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_OutgoingTotle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '外出总计',
  `F_StartTime` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_EndTime` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_Destination` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '目的地',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_OutgoingCause` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '外出事由',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '外出申请单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_paydistribution
-- ----------------------------
DROP TABLE IF EXISTS `wform_paydistribution`;
CREATE TABLE `wform_paydistribution`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_Month` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属月份',
  `F_IssuingUnit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发放单位',
  `F_Department` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工部门',
  `F_Position` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工职位',
  `F_BaseSalary` decimal(18, 2) NULL DEFAULT NULL COMMENT '基本薪资',
  `F_ActualAttendance` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出勤天数',
  `F_Allowance` decimal(18, 2) NULL DEFAULT NULL COMMENT '员工津贴',
  `F_IncomeTax` decimal(18, 2) NULL DEFAULT NULL COMMENT '所得税',
  `F_Insurance` decimal(18, 2) NULL DEFAULT NULL COMMENT '员工保险',
  `F_Performance` decimal(18, 2) NULL DEFAULT NULL COMMENT '员工绩效',
  `F_OvertimePay` decimal(18, 2) NULL DEFAULT NULL COMMENT '加班费用',
  `F_GrossPay` decimal(18, 2) NULL DEFAULT NULL COMMENT '应发工资',
  `F_Payroll` decimal(18, 2) NULL DEFAULT NULL COMMENT '实发工资',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '薪酬发放' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_paymentapply
-- ----------------------------
DROP TABLE IF EXISTS `wform_paymentapply`;
CREATE TABLE `wform_paymentapply`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_Departmental` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请部门',
  `F_PurposeName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用途名称',
  `F_ProjectCategory` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目类别',
  `F_ProjectLeader` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目负责人',
  `F_OpeningBank` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开户银行',
  `F_BeneficiaryAccount` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收款账号',
  `F_ReceivableContact` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `F_PaymentUnit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '付款单位',
  `F_ApplyAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '申请金额',
  `F_SettlementMethod` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '结算方式',
  `F_PaymentType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '付款类型',
  `F_AmountPaid` decimal(18, 2) NULL DEFAULT NULL COMMENT '付款金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '付款申请单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_postbatchtab
-- ----------------------------
DROP TABLE IF EXISTS `wform_postbatchtab`;
CREATE TABLE `wform_postbatchtab`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_FileTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件标题',
  `F_DraftedPerson` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主办单位',
  `F_SendUnit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发往单位',
  `F_WritingNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发文编号',
  `F_WritingDate` datetime NULL DEFAULT NULL COMMENT '发文日期',
  `F_ShareNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '份数',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '发文呈批表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_procurementmaterial
-- ----------------------------
DROP TABLE IF EXISTS `wform_procurementmaterial`;
CREATE TABLE `wform_procurementmaterial`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_Departmental` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请部门',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_PurchaseUnit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采购单位',
  `F_DeliveryMode` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '送货方式',
  `F_DeliveryAddress` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '送货地址',
  `F_PaymentMethod` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '付款方式',
  `F_PaymentMoney` decimal(18, 2) NULL DEFAULT NULL COMMENT '付款金额',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_Reason` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '用途原因',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '采购原材料' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_procurementmaterialentry
-- ----------------------------
DROP TABLE IF EXISTS `wform_procurementmaterialentry`;
CREATE TABLE `wform_procurementmaterialentry`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_ProcurementId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采购主键',
  `F_GoodsName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `F_Specifications` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
  `F_Unit` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '单位',
  `F_Qty` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数量',
  `F_Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '采购明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_purchaselist
-- ----------------------------
DROP TABLE IF EXISTS `wform_purchaselist`;
CREATE TABLE `wform_purchaselist`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_Departmental` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所在部门',
  `F_VendorName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '供应商名称',
  `F_Buyer` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采购人员',
  `F_PurchaseDate` datetime NULL DEFAULT NULL COMMENT '采购日期',
  `F_Warehouse` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '仓库',
  `F_Telephone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `F_PaymentMethod` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付方式',
  `F_PaymentMoney` decimal(18, 2) NULL DEFAULT NULL COMMENT '支付总额',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_Reason` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '用途原因',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '日常物品采购清单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_purchaselistentry
-- ----------------------------
DROP TABLE IF EXISTS `wform_purchaselistentry`;
CREATE TABLE `wform_purchaselistentry`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_PurchaseId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采购主键',
  `F_GoodsName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `F_Specifications` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
  `F_Unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位',
  `F_Qty` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数量',
  `F_Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '日常物品采购清单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_quotationapproval
-- ----------------------------
DROP TABLE IF EXISTS `wform_quotationapproval`;
CREATE TABLE `wform_quotationapproval`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '流程等级',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_Writer` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '填报人',
  `F_WriteDate` datetime NULL DEFAULT NULL COMMENT '填表日期',
  `F_CustomerName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `F_QuotationType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `F_PartnerName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合作人名',
  `F_StandardFile` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模板参考',
  `F_CustSituation` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '情况描述',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报价类型',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_FlowTaskId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程任务Id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '报价审批表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_receiptprocessing
-- ----------------------------
DROP TABLE IF EXISTS `wform_receiptprocessing`;
CREATE TABLE `wform_receiptprocessing`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_FileTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件标题',
  `F_CommunicationUnit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '来文单位',
  `F_LetterNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '来文字号',
  `F_ReceiptDate` datetime NULL DEFAULT NULL COMMENT '收文日期',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '收文处理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_receiptsign
-- ----------------------------
DROP TABLE IF EXISTS `wform_receiptsign`;
CREATE TABLE `wform_receiptsign`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ReceiptTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收文标题',
  `F_Department` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收文部门',
  `F_Collector` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收文人',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_ReceiptPaper` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '收文简述',
  `F_ReceiptDate` datetime NULL DEFAULT NULL COMMENT '收文日期',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '收文签呈单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_rewardpunishment
-- ----------------------------
DROP TABLE IF EXISTS `wform_rewardpunishment`;
CREATE TABLE `wform_rewardpunishment`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工姓名',
  `F_FillFromDate` datetime NULL DEFAULT NULL COMMENT '填表日期',
  `F_Department` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工部门',
  `F_Position` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工职位',
  `F_RewardPun` decimal(18, 2) NULL DEFAULT NULL COMMENT '赏罚金额',
  `F_Reason` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '赏罚原因',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '行政赏罚单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_salesorder
-- ----------------------------
DROP TABLE IF EXISTS `wform_salesorder`;
CREATE TABLE `wform_salesorder`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '流程等级',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_Salesman` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务人员',
  `F_CustomerName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `F_Contacts` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系人',
  `F_ContactPhone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `F_CustomerAddres` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户地址',
  `F_TicketNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发票编号',
  `F_InvoiceType` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发票类型',
  `F_PaymentMethod` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '付款方式',
  `F_PaymentMoney` decimal(18, 2) NULL DEFAULT NULL COMMENT '付款金额',
  `F_SalesDate` datetime NULL DEFAULT NULL COMMENT '销售日期',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_TicketDate` datetime NULL DEFAULT NULL COMMENT '开票日期',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '销售订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_salesorderentry
-- ----------------------------
DROP TABLE IF EXISTS `wform_salesorderentry`;
CREATE TABLE `wform_salesorderentry`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_SalesOrderId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单主键',
  `F_GoodsName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `F_Specifications` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
  `F_Unit` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '单位',
  `F_Qty` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数量',
  `F_Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL,
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '销售订单明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_salessupport
-- ----------------------------
DROP TABLE IF EXISTS `wform_salessupport`;
CREATE TABLE `wform_salessupport`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_ApplyDept` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请部门',
  `F_Customer` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '相关客户',
  `F_Project` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '相关项目',
  `F_PSaleSupInfo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '售前支持',
  `F_StartDate` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_EndDate` datetime NULL DEFAULT NULL COMMENT '结束日期',
  `F_PSaleSupDays` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支持天数',
  `F_PSalePreDays` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '准备天数',
  `F_ConsulManager` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机构咨询',
  `F_PSalSupConsul` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '售前顾问',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `F_SalSupConclu` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '销售总结',
  `F_ConsultResult` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '交付说明',
  `F_IEvaluation` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '咨询评价',
  `F_Conclusion` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '发起人总结',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '销售支持表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_staffovertime
-- ----------------------------
DROP TABLE IF EXISTS `wform_staffovertime`;
CREATE TABLE `wform_staffovertime`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_Department` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请部门',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_TotleTime` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '总计时间',
  `F_StartTime` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_EndTime` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `F_Category` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '记入类别',
  `F_Cause` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '加班事由',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '员工加班申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_supplementcard
-- ----------------------------
DROP TABLE IF EXISTS `wform_supplementcard`;
CREATE TABLE `wform_supplementcard`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_FullName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工姓名',
  `F_Department` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所在部门',
  `F_Position` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所在职务',
  `F_Witness` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '证明人',
  `F_SupplementNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '补卡次数',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_StartTime` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `F_EndTime` datetime NULL DEFAULT NULL COMMENT '结束日期',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '补卡申请' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_travelapply
-- ----------------------------
DROP TABLE IF EXISTS `wform_travelapply`;
CREATE TABLE `wform_travelapply`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_TravelMan` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '出差人',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请日期',
  `F_Departmental` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `F_Position` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属职务',
  `F_StartDate` datetime NULL DEFAULT NULL COMMENT '开始日期',
  `F_EndDate` datetime NULL DEFAULT NULL COMMENT '结束日期',
  `F_StartPlace` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '起始地点',
  `F_Destination` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '目的地',
  `F_PrepaidTravel` decimal(18, 2) NULL DEFAULT NULL COMMENT '预支旅费',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '出差预支申请单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_travelreimbursement
-- ----------------------------
DROP TABLE IF EXISTS `wform_travelreimbursement`;
CREATE TABLE `wform_travelreimbursement`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '流程等级',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_ApplyUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人',
  `F_Departmental` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请部门',
  `F_BillsNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '票据数',
  `F_BusinessMission` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '出差任务',
  `F_SetOutDate` datetime NULL DEFAULT NULL COMMENT '出发日期',
  `F_ReturnDate` datetime NULL DEFAULT NULL COMMENT '回归日期',
  `F_Destination` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '到达地',
  `F_PlaneTicket` decimal(18, 2) NULL DEFAULT NULL COMMENT '机票费',
  `F_Fare` decimal(18, 2) NULL DEFAULT NULL COMMENT '车船费',
  `F_GetAccommodation` decimal(18, 2) NULL DEFAULT NULL COMMENT '住宿费用',
  `F_TravelAllowance` decimal(18, 2) NULL DEFAULT NULL COMMENT '出差补助',
  `F_Other` decimal(18, 2) NULL DEFAULT NULL COMMENT '其他费用',
  `F_Total` decimal(18, 2) NULL DEFAULT NULL COMMENT '合计',
  `F_ReimbursementAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '报销金额',
  `F_LoanAmount` decimal(18, 2) NULL DEFAULT NULL COMMENT '借款金额',
  `F_SumOfMoney` decimal(18, 2) NULL DEFAULT NULL COMMENT '补找金额',
  `F_TravelerUser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `F_VehicleMileage` decimal(18, 2) NULL DEFAULT NULL COMMENT '车辆里程',
  `F_RoadFee` decimal(18, 2) NULL DEFAULT NULL COMMENT '过路费',
  `F_ParkingRate` decimal(18, 2) NULL DEFAULT NULL COMMENT '停车费',
  `F_MealAllowance` decimal(18, 2) NULL DEFAULT NULL COMMENT '餐补费用',
  `F_BreakdownFee` decimal(18, 2) NULL DEFAULT NULL COMMENT '故障报修费',
  `F_ReimbursementId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报销编号',
  `F_RailTransit` decimal(18, 2) NULL DEFAULT NULL COMMENT '轨道交通费',
  `F_ApplyDate` datetime NULL DEFAULT NULL COMMENT '申请时间',
  `f_versions` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `f_flowtaskid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程任务主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '差旅报销申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_vehicleapply
-- ----------------------------
DROP TABLE IF EXISTS `wform_vehicleapply`;
CREATE TABLE `wform_vehicleapply`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_CarMan` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用车人',
  `F_Department` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所在部门',
  `F_PlateNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '车牌号',
  `F_Destination` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '目的地',
  `F_RoadFee` decimal(18, 2) NULL DEFAULT NULL COMMENT '路费金额',
  `F_KilometreNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公里数',
  `F_Entourage` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '随行人数',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_StartDate` datetime NULL DEFAULT NULL COMMENT '开始日期',
  `F_EndDate` datetime NULL DEFAULT NULL COMMENT '结束日期',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '车辆申请' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_violationhandling
-- ----------------------------
DROP TABLE IF EXISTS `wform_violationhandling`;
CREATE TABLE `wform_violationhandling`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_PlateNum` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '车牌号',
  `F_Driver` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '驾驶人',
  `F_LeadingOfficial` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `F_ViolationSite` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '违章地点',
  `F_ViolationBehavior` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '违章行为',
  `F_Deduction` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '违章扣分',
  `F_AmountMoney` decimal(18, 2) NULL DEFAULT NULL COMMENT '违章罚款',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_NoticeDate` datetime NULL DEFAULT NULL COMMENT '通知日期',
  `F_PeccancyDate` datetime NULL DEFAULT NULL COMMENT '违章日期',
  `F_LimitDate` datetime NULL DEFAULT NULL COMMENT '限处理日期',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '违章处理申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_warehousereceipt
-- ----------------------------
DROP TABLE IF EXISTS `wform_warehousereceipt`;
CREATE TABLE `wform_warehousereceipt`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_SupplierName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '供应商名称',
  `F_ContactPhone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `F_WarehousCategory` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入库类别',
  `F_Warehouse` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '仓库',
  `F_WarehousesPeople` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入库人',
  `F_DeliveryNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '送货单号',
  `F_WarehouseNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入库单号',
  `F_WarehousDate` datetime NULL DEFAULT NULL COMMENT '入库日期',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '入库申请单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_warehousereceiptentry
-- ----------------------------
DROP TABLE IF EXISTS `wform_warehousereceiptentry`;
CREATE TABLE `wform_warehousereceiptentry`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_WarehouseId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入库主键',
  `F_GoodsName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `F_Specifications` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号',
  `F_Unit` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位',
  `F_Qty` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数量',
  `F_Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '单价',
  `F_Amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '金额',
  `F_Description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序码',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '出库单明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_workcontactsheet
-- ----------------------------
DROP TABLE IF EXISTS `wform_workcontactsheet`;
CREATE TABLE `wform_workcontactsheet`  (
  `F_Id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自然主键',
  `F_FlowId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程主键',
  `F_FlowTitle` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程标题',
  `F_FlowUrgent` int(11) NULL DEFAULT NULL COMMENT '紧急程度',
  `F_BillNo` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程单据',
  `F_DrawPeople` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发件人',
  `F_IssuingDepartment` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发件部门',
  `F_ServiceDepartment` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收件部门',
  `F_Recipients` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收件人',
  `F_Coordination` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '协调事项',
  `F_FileJson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '相关附件',
  `F_ToDate` datetime NULL DEFAULT NULL COMMENT '发件日期',
  `F_CollectionDate` datetime NULL DEFAULT NULL COMMENT '收件日期',
  `F_TenantId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '工作联系单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wform_zjf_wikxqi
-- ----------------------------
DROP TABLE IF EXISTS `wform_zjf_wikxqi`;
CREATE TABLE `wform_zjf_wikxqi`  (
  `userSelectField101` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户选择',
  `f_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`f_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;


INSERT INTO base_advancedqueryscheme SELECT * FROM ${dbName}.base_advancedqueryscheme;
INSERT INTO base_appdata SELECT * FROM ${dbName}.base_appdata;
INSERT INTO base_authorize SELECT * FROM ${dbName}.base_authorize;
INSERT INTO base_billrule SELECT * FROM ${dbName}.base_billrule;
INSERT INTO base_columnspurview SELECT * FROM ${dbName}.base_columnspurview;
INSERT INTO base_comfields SELECT * FROM ${dbName}.base_comfields;
INSERT INTO base_commonwords SELECT * FROM ${dbName}.base_commonwords;
INSERT INTO base_datainterface SELECT * FROM ${dbName}.base_datainterface;
INSERT INTO base_datainterfacelog SELECT * FROM ${dbName}.base_datainterfacelog;
INSERT INTO base_dbbackup SELECT * FROM ${dbName}.base_dbbackup;
INSERT INTO base_dblink SELECT * FROM ${dbName}.base_dblink;
INSERT INTO base_dictionarydata SELECT * FROM ${dbName}.base_dictionarydata;
INSERT INTO base_dictionarytype SELECT * FROM ${dbName}.base_dictionarytype;
INSERT INTO base_file SELECT * FROM ${dbName}.base_file;
INSERT INTO base_filter SELECT * FROM ${dbName}.base_filter;
INSERT INTO base_group SELECT * FROM ${dbName}.base_group;
INSERT INTO base_imcontent SELECT * FROM ${dbName}.base_imcontent;
INSERT INTO base_imreply SELECT * FROM ${dbName}.base_imreply;
INSERT INTO base_interfaceoauth SELECT * FROM ${dbName}.base_interfaceoauth;
INSERT INTO base_message SELECT * FROM ${dbName}.base_message;
INSERT INTO base_message_account_config SELECT * FROM ${dbName}.base_message_account_config;
INSERT INTO base_message_data_type SELECT * FROM ${dbName}.base_message_data_type;
INSERT INTO base_message_monitor SELECT * FROM ${dbName}.base_message_monitor;
INSERT INTO base_message_send_config SELECT * FROM ${dbName}.base_message_send_config;
INSERT INTO base_message_send_record SELECT * FROM ${dbName}.base_message_send_record;
INSERT INTO base_message_send_template SELECT * FROM ${dbName}.base_message_send_template;
INSERT INTO base_message_short_link SELECT * FROM ${dbName}.base_message_short_link;
INSERT INTO base_message_sms_field SELECT * FROM ${dbName}.base_message_sms_field;
INSERT INTO base_message_template_config SELECT * FROM ${dbName}.base_message_template_config;
INSERT INTO base_message_template_param SELECT * FROM ${dbName}.base_message_template_param;
INSERT INTO base_message_wechat_user SELECT * FROM ${dbName}.base_message_wechat_user;
INSERT INTO base_messagereceive SELECT * FROM ${dbName}.base_messagereceive;
INSERT INTO base_module SELECT * FROM ${dbName}.base_module;
INSERT INTO base_modulebutton SELECT * FROM ${dbName}.base_modulebutton;
INSERT INTO base_modulecolumn SELECT * FROM ${dbName}.base_modulecolumn;
INSERT INTO base_moduledataauthorize SELECT * FROM ${dbName}.base_moduledataauthorize;
INSERT INTO base_moduledataauthorizelink SELECT * FROM ${dbName}.base_moduledataauthorizelink;
INSERT INTO base_moduledataauthorizescheme SELECT * FROM ${dbName}.base_moduledataauthorizescheme;
INSERT INTO base_moduleform SELECT * FROM ${dbName}.base_moduleform;
INSERT INTO base_organize SELECT * FROM ${dbName}.base_organize;
INSERT INTO base_organize_relation SELECT * FROM ${dbName}.base_organize_relation;
INSERT INTO base_organizeadministrator SELECT * FROM ${dbName}.base_organizeadministrator;
INSERT INTO base_portal SELECT * FROM ${dbName}.base_portal;
INSERT INTO base_portal_data SELECT * FROM ${dbName}.base_portal_data;
INSERT INTO base_portal_manage SELECT * FROM ${dbName}.base_portal_manage;
INSERT INTO base_position SELECT * FROM ${dbName}.base_position;
INSERT INTO base_print_log SELECT * FROM ${dbName}.base_print_log;
INSERT INTO base_printdev SELECT * FROM ${dbName}.base_printdev;
INSERT INTO base_province SELECT * FROM ${dbName}.base_province;
INSERT INTO base_province_atlas SELECT * FROM ${dbName}.base_province_atlas;
INSERT INTO base_role SELECT * FROM ${dbName}.base_role;
INSERT INTO base_schedule SELECT * FROM ${dbName}.base_schedule;
INSERT INTO base_schedule_log SELECT * FROM ${dbName}.base_schedule_log;
INSERT INTO base_schedule_user SELECT * FROM ${dbName}.base_schedule_user;
INSERT INTO base_signimg SELECT * FROM ${dbName}.base_signimg;
INSERT INTO base_sms_template SELECT * FROM ${dbName}.base_sms_template;
INSERT INTO base_socialsusersentity SELECT * FROM ${dbName}.base_socialsusersentity;
INSERT INTO base_synthirdinfo SELECT * FROM ${dbName}.base_synthirdinfo;
INSERT INTO base_sysconfig SELECT * FROM ${dbName}.base_sysconfig;
INSERT INTO base_syslog SELECT * FROM ${dbName}.base_syslog;
INSERT INTO base_system SELECT * FROM ${dbName}.base_system;
INSERT INTO base_timetask SELECT * FROM ${dbName}.base_timetask;
INSERT INTO base_timetasklog SELECT * FROM ${dbName}.base_timetasklog;
INSERT INTO base_user SELECT * FROM ${dbName}.base_user;
INSERT INTO base_user_device SELECT * FROM ${dbName}.base_user_device;
INSERT INTO base_user_old_password SELECT * FROM ${dbName}.base_user_old_password;
INSERT INTO base_userrelation SELECT * FROM ${dbName}.base_userrelation;
INSERT INTO base_visualdev SELECT * FROM ${dbName}.base_visualdev;
INSERT INTO base_visualdev_modeldata SELECT * FROM ${dbName}.base_visualdev_modeldata;
INSERT INTO base_visualdev_release SELECT * FROM ${dbName}.base_visualdev_release;
INSERT INTO base_visualdev_short_link SELECT * FROM ${dbName}.base_visualdev_short_link;
INSERT INTO blade_visual SELECT * FROM ${dbName}.blade_visual;
INSERT INTO blade_visual_category SELECT * FROM ${dbName}.blade_visual_category;
INSERT INTO blade_visual_config SELECT * FROM ${dbName}.blade_visual_config;
INSERT INTO blade_visual_db SELECT * FROM ${dbName}.blade_visual_db;
INSERT INTO blade_visual_map SELECT * FROM ${dbName}.blade_visual_map;
INSERT INTO ct293714178055641925 SELECT * FROM ${dbName}.ct293714178055641925;
INSERT INTO ct294012196562705414 SELECT * FROM ${dbName}.ct294012196562705414;
INSERT INTO ct294022926200511493 SELECT * FROM ${dbName}.ct294022926200511493;
INSERT INTO ct294031961024928774 SELECT * FROM ${dbName}.ct294031961024928774;
INSERT INTO ct294031961024928775 SELECT * FROM ${dbName}.ct294031961024928775;
INSERT INTO ct294037561750363142 SELECT * FROM ${dbName}.ct294037561750363142;
INSERT INTO ct294037561750363143 SELECT * FROM ${dbName}.ct294037561750363143;
INSERT INTO ct294099893327272966 SELECT * FROM ${dbName}.ct294099893327272966;
INSERT INTO ct294376098702073542 SELECT * FROM ${dbName}.ct294376098702073542;
INSERT INTO ct294382866144468678 SELECT * FROM ${dbName}.ct294382866144468678;
INSERT INTO ct395899726056134917 SELECT * FROM ${dbName}.ct395899726056134917;
INSERT INTO data_report SELECT * FROM ${dbName}.data_report;
INSERT INTO ext_bigdata SELECT * FROM ${dbName}.ext_bigdata;
INSERT INTO ext_customer SELECT * FROM ${dbName}.ext_customer;
INSERT INTO ext_document SELECT * FROM ${dbName}.ext_document;
INSERT INTO ext_documentshare SELECT * FROM ${dbName}.ext_documentshare;
INSERT INTO ext_emailconfig SELECT * FROM ${dbName}.ext_emailconfig;
INSERT INTO ext_emailreceive SELECT * FROM ${dbName}.ext_emailreceive;
INSERT INTO ext_emailsend SELECT * FROM ${dbName}.ext_emailsend;
INSERT INTO ext_employee SELECT * FROM ${dbName}.ext_employee;
INSERT INTO ext_order SELECT * FROM ${dbName}.ext_order;
INSERT INTO ext_orderentry SELECT * FROM ${dbName}.ext_orderentry;
INSERT INTO ext_orderreceivable SELECT * FROM ${dbName}.ext_orderreceivable;
INSERT INTO ext_product SELECT * FROM ${dbName}.ext_product;
INSERT INTO ext_productclassify SELECT * FROM ${dbName}.ext_productclassify;
INSERT INTO ext_productentry SELECT * FROM ${dbName}.ext_productentry;
INSERT INTO ext_productgoods SELECT * FROM ${dbName}.ext_productgoods;
INSERT INTO ext_projectgantt SELECT * FROM ${dbName}.ext_projectgantt;
INSERT INTO ext_schedule SELECT * FROM ${dbName}.ext_schedule;
INSERT INTO ext_tableexample SELECT * FROM ${dbName}.ext_tableexample;
INSERT INTO ext_worklog SELECT * FROM ${dbName}.ext_worklog;
INSERT INTO ext_worklogshare SELECT * FROM ${dbName}.ext_worklogshare;
INSERT INTO file_detail SELECT * FROM ${dbName}.file_detail;
INSERT INTO flow_candidates SELECT * FROM ${dbName}.flow_candidates;
INSERT INTO flow_comment SELECT * FROM ${dbName}.flow_comment;
INSERT INTO flow_delegate SELECT * FROM ${dbName}.flow_delegate;
INSERT INTO flow_engine SELECT * FROM ${dbName}.flow_engine;
INSERT INTO flow_engineform SELECT * FROM ${dbName}.flow_engineform;
INSERT INTO flow_engineform_relation SELECT * FROM ${dbName}.flow_engineform_relation;
INSERT INTO flow_enginevisible SELECT * FROM ${dbName}.flow_enginevisible;
INSERT INTO flow_rejectdata SELECT * FROM ${dbName}.flow_rejectdata;
INSERT INTO flow_task SELECT * FROM ${dbName}.flow_task;
INSERT INTO flow_taskcirculate SELECT * FROM ${dbName}.flow_taskcirculate;
INSERT INTO flow_tasknode SELECT * FROM ${dbName}.flow_tasknode;
INSERT INTO flow_taskoperator SELECT * FROM ${dbName}.flow_taskoperator;
INSERT INTO flow_taskoperatorrecord SELECT * FROM ${dbName}.flow_taskoperatorrecord;
INSERT INTO flow_taskoperatoruser SELECT * FROM ${dbName}.flow_taskoperatoruser;
INSERT INTO flow_template SELECT * FROM ${dbName}.flow_template;
INSERT INTO flow_templatejson SELECT * FROM ${dbName}.flow_templatejson;
INSERT INTO flow_user SELECT * FROM ${dbName}.flow_user;
INSERT INTO mt293714178051447621 SELECT * FROM ${dbName}.mt293714178051447621;
INSERT INTO mt293732572561709893 SELECT * FROM ${dbName}.mt293732572561709893;
INSERT INTO mt293737725377420101 SELECT * FROM ${dbName}.mt293737725377420101;
INSERT INTO mt293740367726025541 SELECT * FROM ${dbName}.mt293740367726025541;
INSERT INTO mt293756710756064069 SELECT * FROM ${dbName}.mt293756710756064069;
INSERT INTO mt294012196562705413 SELECT * FROM ${dbName}.mt294012196562705413;
INSERT INTO mt294022926196317189 SELECT * FROM ${dbName}.mt294022926196317189;
INSERT INTO mt294027801932110853 SELECT * FROM ${dbName}.mt294027801932110853;
INSERT INTO mt294031961024928773 SELECT * FROM ${dbName}.mt294031961024928773;
INSERT INTO mt294037561750363141 SELECT * FROM ${dbName}.mt294037561750363141;
INSERT INTO mt294090217118276613 SELECT * FROM ${dbName}.mt294090217118276613;
INSERT INTO mt294099237023526085 SELECT * FROM ${dbName}.mt294099237023526085;
INSERT INTO mt294099893327272965 SELECT * FROM ${dbName}.mt294099893327272965;
INSERT INTO mt294104577349819397 SELECT * FROM ${dbName}.mt294104577349819397;
INSERT INTO mt294376098702073541 SELECT * FROM ${dbName}.mt294376098702073541;
INSERT INTO mt294382866144468677 SELECT * FROM ${dbName}.mt294382866144468677;
INSERT INTO mt382811547782648325 SELECT * FROM ${dbName}.mt382811547782648325;
INSERT INTO mt385453315686752389 SELECT * FROM ${dbName}.mt385453315686752389;
INSERT INTO mt395179351563312389 SELECT * FROM ${dbName}.mt395179351563312389;
INSERT INTO mt395899725691230469 SELECT * FROM ${dbName}.mt395899725691230469;
INSERT INTO mt395964134161651973 SELECT * FROM ${dbName}.mt395964134161651973;
INSERT INTO mt400655094485373381 SELECT * FROM ${dbName}.mt400655094485373381;
INSERT INTO mt420589912199274117 SELECT * FROM ${dbName}.mt420589912199274117;
INSERT INTO report_charts SELECT * FROM ${dbName}.report_charts;
INSERT INTO report_department SELECT * FROM ${dbName}.report_department;
INSERT INTO report_user SELECT * FROM ${dbName}.report_user;
INSERT INTO test_carapplication SELECT * FROM ${dbName}.test_carapplication;
INSERT INTO test_cost SELECT * FROM ${dbName}.test_cost;
INSERT INTO test_customer SELECT * FROM ${dbName}.test_customer;
INSERT INTO test_details SELECT * FROM ${dbName}.test_details;
INSERT INTO test_position SELECT * FROM ${dbName}.test_position;
INSERT INTO test_projects SELECT * FROM ${dbName}.test_projects;
INSERT INTO test_purchase SELECT * FROM ${dbName}.test_purchase;
INSERT INTO test_purchase_form SELECT * FROM ${dbName}.test_purchase_form;
INSERT INTO test_purchase_form_order SELECT * FROM ${dbName}.test_purchase_form_order;
INSERT INTO test_purchaseorder SELECT * FROM ${dbName}.test_purchaseorder;
INSERT INTO test_receivable SELECT * FROM ${dbName}.test_receivable;
INSERT INTO test_reimbursement SELECT * FROM ${dbName}.test_reimbursement;
INSERT INTO test_subpackage SELECT * FROM ${dbName}.test_subpackage;
INSERT INTO test_vehicleinfo SELECT * FROM ${dbName}.test_vehicleinfo;
INSERT INTO test_warehousing SELECT * FROM ${dbName}.test_warehousing;
INSERT INTO test_warehousingorder SELECT * FROM ${dbName}.test_warehousingorder;
INSERT INTO undo_log SELECT * FROM ${dbName}.undo_log;
INSERT INTO wform_applybanquet SELECT * FROM ${dbName}.wform_applybanquet;
INSERT INTO wform_applydelivergoods SELECT * FROM ${dbName}.wform_applydelivergoods;
INSERT INTO wform_applydelivergoodsentry SELECT * FROM ${dbName}.wform_applydelivergoodsentry;
INSERT INTO wform_applymeeting SELECT * FROM ${dbName}.wform_applymeeting;
INSERT INTO wform_archivalborrow SELECT * FROM ${dbName}.wform_archivalborrow;
INSERT INTO wform_articleswarehous SELECT * FROM ${dbName}.wform_articleswarehous;
INSERT INTO wform_batchpack SELECT * FROM ${dbName}.wform_batchpack;
INSERT INTO wform_batchtable SELECT * FROM ${dbName}.wform_batchtable;
INSERT INTO wform_conbilling SELECT * FROM ${dbName}.wform_conbilling;
INSERT INTO wform_contractapproval SELECT * FROM ${dbName}.wform_contractapproval;
INSERT INTO wform_contractapprovalsheet SELECT * FROM ${dbName}.wform_contractapprovalsheet;
INSERT INTO wform_debitbill SELECT * FROM ${dbName}.wform_debitbill;
INSERT INTO wform_documentapproval SELECT * FROM ${dbName}.wform_documentapproval;
INSERT INTO wform_documentsigning SELECT * FROM ${dbName}.wform_documentsigning;
INSERT INTO wform_expenseexpenditure SELECT * FROM ${dbName}.wform_expenseexpenditure;
INSERT INTO wform_finishedproduct SELECT * FROM ${dbName}.wform_finishedproduct;
INSERT INTO wform_finishedproductentry SELECT * FROM ${dbName}.wform_finishedproductentry;
INSERT INTO wform_incomerecognition SELECT * FROM ${dbName}.wform_incomerecognition;
INSERT INTO wform_leaveapply SELECT * FROM ${dbName}.wform_leaveapply;
INSERT INTO wform_letterservice SELECT * FROM ${dbName}.wform_letterservice;
INSERT INTO wform_materialrequisition SELECT * FROM ${dbName}.wform_materialrequisition;
INSERT INTO wform_materialrequisitionentry SELECT * FROM ${dbName}.wform_materialrequisitionentry;
INSERT INTO wform_monthlyreport SELECT * FROM ${dbName}.wform_monthlyreport;
INSERT INTO wform_officesupplies SELECT * FROM ${dbName}.wform_officesupplies;
INSERT INTO wform_outboundorder SELECT * FROM ${dbName}.wform_outboundorder;
INSERT INTO wform_outboundorderentry SELECT * FROM ${dbName}.wform_outboundorderentry;
INSERT INTO wform_outgoingapply SELECT * FROM ${dbName}.wform_outgoingapply;
INSERT INTO wform_paydistribution SELECT * FROM ${dbName}.wform_paydistribution;
INSERT INTO wform_paymentapply SELECT * FROM ${dbName}.wform_paymentapply;
INSERT INTO wform_postbatchtab SELECT * FROM ${dbName}.wform_postbatchtab;
INSERT INTO wform_procurementmaterial SELECT * FROM ${dbName}.wform_procurementmaterial;
INSERT INTO wform_procurementmaterialentry SELECT * FROM ${dbName}.wform_procurementmaterialentry;
INSERT INTO wform_purchaselist SELECT * FROM ${dbName}.wform_purchaselist;
INSERT INTO wform_purchaselistentry SELECT * FROM ${dbName}.wform_purchaselistentry;
INSERT INTO wform_quotationapproval SELECT * FROM ${dbName}.wform_quotationapproval;
INSERT INTO wform_receiptprocessing SELECT * FROM ${dbName}.wform_receiptprocessing;
INSERT INTO wform_receiptsign SELECT * FROM ${dbName}.wform_receiptsign;
INSERT INTO wform_rewardpunishment SELECT * FROM ${dbName}.wform_rewardpunishment;
INSERT INTO wform_salesorder SELECT * FROM ${dbName}.wform_salesorder;
INSERT INTO wform_salesorderentry SELECT * FROM ${dbName}.wform_salesorderentry;
INSERT INTO wform_salessupport SELECT * FROM ${dbName}.wform_salessupport;
INSERT INTO wform_staffovertime SELECT * FROM ${dbName}.wform_staffovertime;
INSERT INTO wform_supplementcard SELECT * FROM ${dbName}.wform_supplementcard;
INSERT INTO wform_travelapply SELECT * FROM ${dbName}.wform_travelapply;
INSERT INTO wform_travelreimbursement SELECT * FROM ${dbName}.wform_travelreimbursement;
INSERT INTO wform_vehicleapply SELECT * FROM ${dbName}.wform_vehicleapply;
INSERT INTO wform_violationhandling SELECT * FROM ${dbName}.wform_violationhandling;
INSERT INTO wform_warehousereceipt SELECT * FROM ${dbName}.wform_warehousereceipt;
INSERT INTO wform_warehousereceiptentry SELECT * FROM ${dbName}.wform_warehousereceiptentry;
INSERT INTO wform_workcontactsheet SELECT * FROM ${dbName}.wform_workcontactsheet;
INSERT INTO wform_zjf_wikxqi SELECT * FROM ${dbName}.wform_zjf_wikxqi;