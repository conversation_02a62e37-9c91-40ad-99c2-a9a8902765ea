package com.xinghuo.common.database.plugins;

import com.xinghuo.common.database.util.ResetSetHolder;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;

import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Mybatis ResultSet 拦截器   * 用于在处理查询结果集之前设置当前线程的 ResultSet，以便在 MyBatis 插件中使用
 *
 * 该拦截器的主要作用是在处理 MyBatis 查询结果集之前，将当前线程的 ResultSet 设置到 ResetSetHolder 中。
 * 这样设计的目的可能是为了在后续的 MyBatis 插件中能够方便地获取和使用当前线程的 ResultSet。
 *
 * 具体来说，可能有以下用途：
 * * 获取查询结果集信息： 在 MyBatis 插件中可能需要获取查询结果集的元数据或其他信息，而 ResultSetInterceptor 可以在查询执行完成后保存当前线程的 ResultSet，以便后续插件使用。
 * * 处理查询结果： 有些自定义的查询结果处理逻辑可能需要使用 ResultSet 的信息，而 ResetSetHolder 提供了一种在不同插件之间传递 ResultSet 的机制。
 * @see ResetSetHolder
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Intercepts({@Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class})})
public class ResultSetInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 获取方法参数
        Object[] args = invocation.getArgs();
        // 获取 Statement 对象
        Statement statement = (Statement) args[0];
        // 获取 ResultSet 对象
        ResultSet rs = statement.getResultSet();

        // 如果 ResultSet 不为空，则将其设置到 ResetSetHolder 中，方便后续的 MyBatis 插件使用
        if (rs != null) {
            ResetSetHolder.setResultSet(rs);
        }

        // 执行原始方法
        Object result;
        try {
            result = invocation.proceed();
        } finally {
            // 清除 ResetSetHolder 中的 ResultSet
            ResetSetHolder.clear();
        }
        return result;
    }
}
