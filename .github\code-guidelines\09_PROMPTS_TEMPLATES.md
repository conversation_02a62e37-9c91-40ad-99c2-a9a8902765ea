# AI 代码生成提示词模板

本文档提供了使用 AI 辅助工具（如 GitHub Copilot、ChatGPT 等）生成代码时的提示词模板，以确保生成的代码符合项目规范。

## 1. Entity 层生成模板

```
请根据以下表结构生成 Entity 实体类：
表名：{表名}
字段：
- {字段名1} {类型} {描述}
- {字段名2} {类型} {描述}
...

要求：
1. 类名以 Entity 结尾
2. 使用 @Data 和其他必要的 lombok 注解
3. 继承 BaseEntityV2.CUBaseEntityV2
4. 使用 @TableName 指定表名
5. 每个字段添加 @TableField 注解和注释
6. 遵循项目 Entity 层规范
```

### 示例：生成商品实体类

```
请根据以下表结构生成 Entity 实体类：
表名：product_info
字段：
- product_name varchar(100) 商品名称
- category_id bigint 分类ID
- price decimal(10,2) 商品价格
- stock int 库存数量
- description text 商品描述
- status tinyint(1) 状态(1-上架,0-下架)
- image_url varchar(255) 主图URL

要求：
1. 类名以 Entity 结尾
2. 使用 @Data 和其他必要的 lombok 注解
3. 继承 BaseEntityV2.CUBaseEntityV2
4. 使用 @TableName 指定表名
5. 每个字段添加 @TableField 注解和注释
6. 遵循项目 Entity 层规范
```

## 2. DTO 对象生成模板

```
请根据以下实体类生成 CreateDTO 和 UpdateDTO：
实体类：{实体类名}
字段：
- {字段名1} {类型} {描述} {是否必填}
- {字段名2} {类型} {描述} {是否必填}
...

要求：
1. CreateDTO 包含创建实体所需的全部字段
2. UpdateDTO 包含可更新的字段
3. 添加字段校验注解（@NotNull, @NotBlank 等）
4. 每个字段添加注释
5. 遵循项目 DTO 层规范
```

### 示例：根据实体生成 DTO

```
请根据以下实体类生成 CreateDTO 和 UpdateDTO：
实体类：ProductEntity
字段：
- productName String 商品名称 必填
- categoryId Long 分类ID 必填
- price BigDecimal 商品价格 必填
- stock Integer 库存数量 必填
- description String 商品描述 选填
- status Integer 状态(1-上架,0-下架) 必填
- imageUrl String 主图URL 选填

要求：
1. CreateDTO 包含创建实体所需的全部字段
2. UpdateDTO 包含可更新的字段
3. 添加字段校验注解（@NotNull, @NotBlank 等）
4. 每个字段添加注释
5. 遵循项目 DTO 层规范
```

## 3. VO 对象生成模板

```
请根据以下实体类生成 VO 类：
实体类：{实体类名}
额外属性：
- {属性名1} {类型} {描述} {来源}
- {属性名2} {类型} {描述} {来源}
...

要求：
1. 包含实体类的全部属性
2. 添加额外需要的展示属性
3. 每个字段添加注释
4. 使用 @Data 注解
5. 遵循项目 VO 层规范
```

### 示例：生成商品 VO

```
请根据以下实体类生成 VO 类：
实体类：ProductEntity
额外属性：
- categoryName String 分类名称 来自关联表
- statusName String 状态名称 由状态代码转换

要求：
1. 包含实体类的全部属性
2. 添加额外需要的展示属性
3. 每个字段添加注释
4. 使用 @Data 注解
5. 遵循项目 VO 层规范
```

## 4. Mapper 生成模板

```
请根据以下实体类生成 Mapper 接口：
实体类：{实体类名}
特殊方法：
- {方法名1} {描述}
- {方法名2} {描述}
...

要求：
1. 继承 BaseMapper<{实体类}>
2. 添加 @Mapper 注解
3. 实现特殊查询方法（如有需要）
4. 遵循项目 Mapper 层规范
```

### 示例：生成商品 Mapper

```
请根据以下实体类生成 Mapper 接口：
实体类：ProductEntity
特殊方法：
- selectProductWithCategory 查询商品并关联分类信息
- countByCategory 统计分类下的商品数量

要求：
1. 继承 BaseMapper<ProductEntity>
2. 添加 @Mapper 注解
3. 实现特殊查询方法（如有需要）
4. 遵循项目 Mapper 层规范
```

## 5. Service 接口生成模板

```
请根据以下业务需求生成 Service 接口：
业务实体：{实体名称}
实体类型：{实体类名}
基本功能：{是否包含CRUD}
特殊功能：
- {功能1} {描述}
- {功能2} {描述}
...

要求：
1. 接口名为 {实体名}Service
2. 继承 BaseService<{实体类名}>
3. 包含基础的 CRUD 方法（如需要）：getList, getInfo, saveInfo, updateInfo, deleteById
4. 添加特殊业务方法
5. 每个方法添加完整的 JavaDoc 注释
6. 遵循项目 Service 层规范
```

### 示例：生成订单 Service 接口

```
请根据以下业务需求生成 Service 接口：
业务实体：Order
实体类型：OrderEntity
基本功能：包含完整CRUD
特殊功能：
- 订单支付功能
- 订单取消功能
- 订单发货功能
- 查询用户所有订单

要求：
1. 接口名为 OrderService
2. 继承 BaseService<OrderEntity>
3. 包含基础的 CRUD 方法：getList, getInfo, saveInfo, updateInfo, deleteById
4. 添加特殊业务方法
5. 每个方法添加完整的 JavaDoc 注释
6. 遵循项目 Service 层规范
```

## 6. Service 实现类生成模板

```
请根据以下 Service 接口生成实现类：
接口：{接口名}
依赖：
- {依赖项1} {用途}
- {依赖项2} {用途}
...

要求：
1. 类名为 {接口名}Impl
2. 使用 @Service 和 @Slf4j 注解
3. 继承 ExtendedBaseServiceImpl<{对应Mapper类}, {实体类}>
4. 使用 @Resource 进行依赖注入
5. 实现接口中的全部方法
6. 添加适当的事务注解
7. 实现每个方法的业务逻辑
8. 添加日志记录
9. 遵循项目 Service 实现层规范
```

### 示例：生成订单 Service 实现类

```
请根据以下 Service 接口生成实现类：
接口：OrderService
依赖：
- OrderMapper 数据访问
- ProductService 商品服务
- UserService 用户服务
- MessageService 消息服务

要求：
1. 类名为 OrderServiceImpl
2. 使用 @Service 和 @Slf4j 注解
3. 继承 ExtendedBaseServiceImpl<OrderMapper, OrderEntity>
4. 使用 @Resource 注解进行依赖注入
5. 实现接口中的全部方法
6. 添加适当的事务注解
7. 实现每个方法的业务逻辑
8. 添加日志记录
9. 遵循项目 Service 实现层规范
```

## 7. Controller 生成模板

```
请根据以下 Service 生成 RESTful Controller：
业务服务：{Service名称}
API路径：{基础路径}
功能：
- {功能1} {HTTP方法} {子路径}
- {功能2} {HTTP方法} {子路径}
...

要求：
1. 类名为 {实体名}Controller
2. 使用 @RestController 和 @RequestMapping 注解
3. 使用 @Resource 进行依赖注入
4. 使用 Swagger 注解进行 API 文档标注
5. 添加参数校验
6. 统一返回 ActionResult 对象
7. 遵循项目 Controller 层规范
```

### 示例：生成订单 Controller

```
请根据以下 Service 生成 RESTful Controller：
业务服务：OrderService
API路径：/api/orders
功能：
- 查询订单列表 GET /
- 获取订单详情 GET /{id}
- 创建订单 POST /
- 取消订单 POST /{id}/cancel
- 支付订单 POST /{id}/pay
- 确认收货 POST /{id}/confirm

要求：
1. 类名为 OrderController
2. 使用 @RestController 和 @RequestMapping 注解
3. 使用 @Resource 进行依赖注入
4. 使用 Swagger 注解进行 API 文档标注
5. 添加参数校验
6. 统一返回 ActionResult 对象
7. 遵循项目 Controller 层规范
```

## 8. 完整 CRUD 结构生成模板

```
请根据以下实体信息生成完整的 CRUD 代码结构：
实体信息：
- 表名：{表名}
- 实体类名：{实体类名}
- 主要字段：
  - {字段名1} {类型} {描述}
  - {字段名2} {类型} {描述}
  ...
- 模块包名：{包名}

要求生成：
1. Entity 实体类
2. DTO (Create和Update)
3. VO 对象
4. Mapper 接口
5. Service 接口及实现类（继承BaseService和ExtendedBaseServiceImpl）
6. Controller 类
7. 遵循项目各层规范
```

### 示例：生成完整商品模块

```
请根据以下实体信息生成完整的 CRUD 代码结构：
实体信息：
- 表名：product_info
- 实体类名：ProductEntity
- 主要字段：
  - productName String 商品名称
  - categoryId Long 分类ID
  - price BigDecimal 商品价格
  - stock Integer 库存数量
  - description String 商品描述
  - status Integer 状态(1-上架,0-下架)
  - imageUrl String 主图URL
- 模块包名：com.xinghuo.product

要求生成：
1. Entity 实体类
2. DTO (Create和Update)
3. VO 对象
4. Mapper 接口
5. Service 接口及实现类（继承BaseService和ExtendedBaseServiceImpl）
6. Controller 类
7. 遵循项目各层规范
```

## 9. 单元测试生成模板

```
请为以下服务类生成单元测试：
类名：{类名}
需测试方法：
- {方法1} {简要描述}
- {方法2} {简要描述}
...

要求：
1. 使用 JUnit5 和 Mockito
2. 模拟所有依赖
3. 覆盖正常场景和异常场景
4. 遵循测试命名和结构规范
```

### 示例：生成订单服务单元测试

```
请为以下服务类生成单元测试：
类名：OrderServiceImpl
需测试方法：
- create 创建订单
- cancel 取消订单
- pay 支付订单

要求：
1. 使用 JUnit5 和 Mockito
2. 模拟所有依赖
3. 覆盖正常场景和异常场景
4. 遵循测试命名和结构规范
```

## 10. 根据数据库表生成代码模板

```
请根据以下数据库表结构生成 Entity 类：
CREATE TABLE `{表名}` (
  {表结构SQL}
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='{表注释}';

要求：
1. 生成符合项目规范的 Entity 类
2. 正确映射字段类型
3. 继承 BaseEntityV2.CUBaseEntityV2
4. 添加适当的注解
```

### 示例：从建表SQL生成实体

```
请根据以下数据库表结构生成 Entity 类：
CREATE TABLE `user_address` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `recipient_name` varchar(50) NOT NULL COMMENT '收件人姓名',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `province` varchar(20) NOT NULL COMMENT '省份',
  `city` varchar(20) NOT NULL COMMENT '城市',
  `district` varchar(20) NOT NULL COMMENT '区县',
  `detail_address` varchar(200) NOT NULL COMMENT '详细地址',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `created_by` varchar(32) NOT NULL COMMENT '创建者',
  `last_updated_at` datetime NOT NULL COMMENT '最后更新时间',
  `last_updated_by` varchar(32) NOT NULL COMMENT '最后更新人',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户地址表';

要求：
1. 生成符合项目规范的 Entity 类
2. 正确映射字段类型
3. 继承 BaseEntityV2.CUBaseEntityV2
4. 添加适当的注解
```

## 11. 从Excel表格描述生成代码模板

```
请根据以下Excel表格描述生成 Entity 类：
表名称：{表名}
表描述：{表描述}

| 列名 | 数据类型 | 是否必填 | 描述 |
|------|----------|----------|------|
| {列名1} | {类型1} | {是/否} | {描述1} |
| {列名2} | {类型2} | {是/否} | {描述2} |
...

要求：
1. 生成符合项目规范的 Entity 类
2. 正确映射字段类型
3. 继承 BaseEntityV2.CUBaseEntityV2
4. 添加适当的注解和注释
```

### 示例：从Excel描述生成实体

```
请根据以下Excel表格描述生成 Entity 类：
表名称：customer_feedback
表描述：客户反馈表

| 列名 | 数据类型 | 是否必填 | 描述 |
|------|----------|----------|------|
| id | varchar(32) | 是 | 主键ID |
| customer_id | varchar(32) | 是 | 客户ID |
| content | text | 是 | 反馈内容 |
| feedback_type | tinyint | 是 | 反馈类型（1-建议，2-投诉，3-咨询） |
| status | tinyint | 是 | 处理状态（0-未处理，1-处理中，2-已处理） |
| created_at | datetime | 是 | 创建时间 |
| created_by | varchar(32) | 是 | 创建者 |
| last_updated_at | datetime | 是 | 最后更新时间 |
| last_updated_by | varchar(32) | 是 | 最后更新人 |
| handler_id | varchar(32) | 否 | 处理人ID |
| handle_time | datetime | 否 | 处理时间 |
| handle_result | text | 否 | 处理结果 |
| attachments | varchar(500) | 否 | 附件URL，多个用逗号分隔 |
| is_deleted | tinyint(1) | 是 | 是否删除 |

要求：
1. 生成符合项目规范的 Entity 类
2. 正确映射字段类型
3. 继承 BaseEntityV2.CUBaseEntityV2
4. 添加适当的注解和注释
```

## 12. 业务方法实现模板

```
请实现以下业务方法：
方法签名：{方法签名}
业务需求：{详细描述业务逻辑}
涉及依赖：
- {依赖1} {用途}
- {依赖2} {用途}
...

要求：
1. 实现完整的业务逻辑
2. 添加适当的参数校验
3. 包含异常处理
4. 添加日志记录
5. 添加适当的注释
```

### 示例：实现订单创建方法

```
请实现以下业务方法：
方法签名：public OrderVO createOrder(OrderCreateDTO dto)
业务需求：
  1. 根据用户ID和商品列表创建订单
  2. 检查商品库存，不足则抛出异常
  3. 计算订单总价
  4. 扣减商品库存
  5. 创建订单记录和订单明细记录
  6. 生成唯一订单号
  7. 如有优惠券，应用优惠券并更新优惠券状态
  8. 创建成功后发送订单创建事件通知
  
涉及依赖：
- OrderMapper 订单数据访问
- OrderItemMapper 订单明细数据访问
- ProductMapper 商品数据访问
- CouponService 优惠券服务
- EventPublisher 事件发布服务
- IdGenerator ID生成器

要求：
1. 实现完整的业务逻辑
2. 添加适当的参数校验
3. 包含异常处理
4. 添加日志记录
5. 添加适当的注释
```

