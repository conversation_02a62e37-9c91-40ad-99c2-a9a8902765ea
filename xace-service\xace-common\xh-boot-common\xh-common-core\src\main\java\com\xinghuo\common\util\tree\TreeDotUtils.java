package com.xinghuo.common.util.tree;


import cn.hutool.core.collection.CollUtil;
import com.xinghuo.common.util.core.StrXhUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 树工具类
 * <AUTHOR>
 * @date 2023-10-05
 */
public class TreeDotUtils {

    /**
     * 将List转换为Tree
     * @param tList 待转换的列表，其中元素需实现SumTree接口
     * @param parentId 作为树节点父ID的字段，默认为空
     * @param <T> 泛型参数，需实现SumTree接口
     * @return 转换后的树结构列表
     */
    public static <T extends SumTree> List<SumTree<T>> convertListToTreeDot(List<T> tList, String parentId) {
        List<SumTree<T>> sumTrees = new ArrayList<>();
        List<T> list = new ArrayList<>();
        CollUtil.addAll(list,tList);
        if (StrXhUtil.isNotEmpty(parentId)) {
            // 根据指定的父ID过滤列表并构建树
            List<T> data = list.stream().filter(t -> parentId.equals(t.getParentId())).toList();
            list.removeAll(data);
            for (T t : data) {
                // 如果列表中不存在具有当前节点ID的节点，则将其添加到树中
                if (!isTreeDotExist(list, t.getParentId())) {
                    SumTree<T> tSumTree = getTreeDotByT(t, list);
                    sumTrees.add(tSumTree);
                }
            }
        }
        return sumTrees;
    }

    /**
     * 将List转换为Tree，自动寻找根节点
     * @param tList 待转换的列表，其中元素需实现SumTree接口
     * @param <T> 泛型参数，需实现SumTree接口
     * @return 转换后的树结构列表
     */
    public static <T extends SumTree> List<SumTree<T>> convertListToTreeDot(List<T> tList) {
        List<SumTree<T>> sumTrees = new ArrayList<>();
        if (tList != null && tList.size() > 0) {
            // 遍历列表，寻找并添加所有根节点到树
            for (int i = 0; i < tList.size(); i++) {
                T t = tList.get(i);
                if (!isTreeDotExist(tList, t.getParentId())) {
                    SumTree<T> tSumTree = getTreeDotByT(t, tList);
                    sumTrees.add(tSumTree);
                }
            }
        }
        return sumTrees;
    }

    /**
     * 将List转换为Tree，过滤特定子集
     * @param tList 待转换的列表，其中元素需实现SumTree接口
     * @param <T> 泛型参数，需实现SumTree接口
     * @return 转换后的树结构列表，过滤掉特定子集
     */
    public static <T extends SumTree> List<SumTree<T>> convertListToTreeDotFilter(List<T> tList) {
        List<SumTree<T>> sumTrees = new ArrayList<>();
        if (tList != null && tList.size() > 0) {
            // 遍历列表，寻找并添加符合特定条件（如父ID为特定值）的根节点到树
            for (int i = 0; i < tList.size(); i++) {
                T t = tList.get(i);
                if (!isTreeDotExist(tList, t.getParentId())) {
                    SumTree<T> tSumTree = getTreeDotByT(t, tList);
                    if ("-1".equals(tSumTree.getParentId()) || "0".equals(tSumTree.getParentId())) {
                        sumTrees.add(tSumTree);
                    }
                }
            }
        }
        return sumTrees;
    }

    /**
     * 判断列表中是否存在具有指定ID的节点
     * @param tList 节点列表
     * @param id 指定的节点ID
     * @param <T> 泛型参数，需实现SumTree接口
     * @return 如果列表中存在具有指定ID的节点，则返回true；否则返回false。
     */
    private static <T extends SumTree> Boolean isTreeDotExist(List<T> tList, String id) {
        for (T t : tList) {
            if (t.getId().equals(id)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取指定父节点的子树列表
     * @param parentTreeDot 指定的父节点
     * @param tList 节点列表
     * @param <T> 泛型参数，需实现SumTree接口
     * @return 指定父节点的子树列表
     */
    private static <T extends SumTree> List<SumTree<T>> getChildTreeDotList(SumTree<T> parentTreeDot, List<T> tList) {
        List<SumTree<T>> childTreeDotList = new ArrayList<>();
        List<T> data = tList.stream().filter(t -> parentTreeDot.getId().equals(t.getParentId())).collect(Collectors.toList());
        for (T t : data) {
            // 如果父ID匹配，则将该节点添加到子树列表中
            if (parentTreeDot.getId().equals(t.getParentId())) {
                SumTree<T> tSumTree = getTreeDotByT(t, tList);
                childTreeDotList.add(tSumTree);
            }
        }
        return childTreeDotList;
    }

    /**
     * 根据实体获取TreeDot对象
     * @param t 实体对象
     * @param tList 节点列表
     * @param <T> 泛型参数，需实现SumTree接口
     * @return 根据实体构建的TreeDot对象
     */
    private static <T extends SumTree> SumTree<T> getTreeDotByT(T t, List<T> tList) {
        SumTree<T> sumTree = t;
        // 获取并设置子节点信息
        List<SumTree<T>> children = getChildTreeDotList(sumTree, tList);
        sumTree.setHasChildren(children.size() == 0 ? false : true);
        if (children.size() == 0) {
            children = null;
        }
        sumTree.setChildren(children);
        return sumTree;
    }

}

