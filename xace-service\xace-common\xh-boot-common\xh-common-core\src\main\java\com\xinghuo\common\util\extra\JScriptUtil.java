package com.xinghuo.common.util.extra;

import cn.hutool.script.ScriptUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.json.JsonXhUtil;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.Objects;

/**
 * Java执行js代码工具类
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class JScriptUtil {

    /**
     * 数据接口通用定义函数
     * 用于构造JS函数模板，其中`${jsContent}`和`${data}`是占位符，
     * 分别在调用时被真实的JS内容和传入的数据替换。
     */
    public static final String JSCONTENT = "var method = function(data) {" +
            "${jsContent}" +
            "};" +
            "var result = method(${data});" +
            "if(typeof(result)=='object'){JSON.stringify(result);}else{result;};;";

    /**
     * 调用js代码
     * @param script 脚本内容
     * @return 如果JS内返回的是对象 返回内容为ScriptObjectMirror
     */
    public static Object callJs(String script) throws ScriptException {
        ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
        ScriptEngine scriptEngine = scriptEngineManager.getEngineByName("js");

        // 执行传入的JS脚本
        ScriptUtil.eval(script) ;
        return scriptEngine.eval(script);
    }

    /**
     * 调用js代码, 处理JSON数据 返回JSON数据
     *
     * @param dataProcessing JS数据处理函数代码片段
     * @param data JSON对象/数组
     * @return JSON对象/数组
     */
    public static Object callJs(String dataProcessing, Object data) throws ScriptException {
        // 获取处理函数的具体内容
        String jsContent = getJsContent(dataProcessing);
        if (StrXhUtil.isEmpty(dataProcessing)) {
            return data;
        }
        // 构造完整的JS脚本并执行
        String replace = JSCONTENT.replace("${jsContent}", jsContent);
        replace = replace.replace("${data}", Objects.requireNonNull(JsonXhUtil.toJSONString(data)));
        Object result ;
        try {
            result = callJs(replace);
        } catch (Exception e) {
            throw e;
        }
        try {
            // 尝试将JS返回结果转换为List<Map<String, Object>>格式
            return JsonXhUtil.getJsonToListMap(result.toString());
        } catch (Exception e) {
            try {
                // 如果转换失败，尝试将结果转换为Map<String, Object>格式
                return JsonXhUtil.stringToMap(result.toString());
            } catch (Exception ee) {
                // 如果仍然转换失败，则直接返回JS执行的结果
                return result;
            }
        }
    }

    /**
     * 返回js内容
     * 从传入的字符串中提取真实的JS内容，即去除外层的定义函数部分。
     *
     * @param dataProcessing 包含JS函数定义的字符串
     * @return 提取后的JS内容
     */
    public static String getJsContent(String dataProcessing) {
        if (StrXhUtil.isNotEmpty(dataProcessing) && !dataProcessing.isEmpty()) {
            // 移除函数定义部分，只保留函数体的内容
            int indexOf = dataProcessing.indexOf("{");
            if (indexOf > -1) {
                dataProcessing = dataProcessing.substring(indexOf + 1);
            }
            int lastIndexOf = dataProcessing.lastIndexOf("}");
            if (lastIndexOf > -1) {
                dataProcessing = dataProcessing.substring(0, lastIndexOf);
            }
        }
        return dataProcessing;
    }

}
