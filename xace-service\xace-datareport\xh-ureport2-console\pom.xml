<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>xh-ureport2-console</artifactId>
	<version>2.0.0</version>
	<parent>
		<groupId>com.xinghuo</groupId>
		<artifactId>xh-ureport2-datareport</artifactId>
		<version>2.0.0</version>
	</parent>
	<dependencies>
		<dependency>
			<groupId>com.xinghuo</groupId>
			<artifactId>xh-ureport2-core</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.xinghuo</groupId>
			<artifactId>xh-ureport2-font</artifactId>
			<version>2.0.0</version>
		</dependency>
        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
            <version>8.16</version>
        </dependency>
        <dependency>
			<groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-common-core</artifactId>
            <version>2.1.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

	<build>
		<finalName>xh-datareport-${xace.version}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot.version}</version>
				<configuration>
					<!-- 指定该Main Class为全局的唯一入口 -->
					<mainClass>com.bstek.ureport.console.DataReportApplication</mainClass>
					<layout>ZIP</layout>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中-->
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
