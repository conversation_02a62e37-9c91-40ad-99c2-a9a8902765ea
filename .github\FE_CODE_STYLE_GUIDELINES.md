# xace-web-vue3 前端代码风格与规范导航

本文档提供 `xace-web-vue3` 仓库前端代码风格与规范的整体导航，帮助开发者快速找到相关指南。

## 文档目录

1. [核心技术栈与架构](./fe-code-guidelines/01_TECH_STACK.md)
   - Vue 3, TypeScript, Vite, Ant Design Vue
   - 文件结构和代码组织

2. [项目结构与文件命名](./fe-code-guidelines/02_PROJECT_STRUCTURE.md)
   - 目录结构
   - 命名约定
   - 模块划分

3. [Vue组件规范](./fe-code-guidelines/03_VUE_COMPONENT_GUIDELINES.md)
   - 组件设计原则
   - Props和Emits定义
   - 组合式API (Composition API) 使用指南
   - 性能优化

4. [TypeScript使用规范](./fe-code-guidelines/04_TS_GUIDELINES.md)
   - 类型定义
   - 接口和类型使用
   - 泛型应用
   - 常见错误避免

5. [样式编写规范](./fe-code-guidelines/05_STYLE_GUIDELINES.md)
   - CSS/LESS规范
   - 样式组织方式
   - 变量和主题
   - 响应式设计

6. [API调用规范](./fe-code-guidelines/06_API_GUIDELINES.md)
   - 请求封装
   - 错误处理
   - 数据转换
   - Mock数据

7. [状态管理规范](./fe-code-guidelines/07_STATE_MANAGEMENT.md)
   - Pinia使用规范
   - 状态设计原则
   - 持久化策略
   - 状态共享范围

8. [AI代码生成提示词模板](./fe-code-guidelines/08_PROMPTS_TEMPLATES.md)
   - 通用模板
   - 组件生成模板
   - API集成模板

## 快速使用指南

1. **新手上路**：首先阅读[核心技术栈与架构](./fe-code-guidelines/01_TECH_STACK.md)和[项目结构与文件命名](./fe-code-guidelines/02_PROJECT_STRUCTURE.md)，了解项目整体框架

2. **组件开发**：参考[Vue组件规范](./fe-code-guidelines/03_VUE_COMPONENT_GUIDELINES.md)进行组件设计和实现

3. **样式开发**：遵循[样式编写规范](./fe-code-guidelines/05_STYLE_GUIDELINES.md)保持样式一致性

4. **API集成**：按照[API调用规范](./fe-code-guidelines/06_API_GUIDELINES.md)实现后端交互

5. **使用AI辅助开发**：参考[AI代码生成提示词模板](./fe-code-guidelines/08_PROMPTS_TEMPLATES.md)加速开发

## 代码质量工具

项目使用以下工具确保代码质量：

1. **ESLint** - 代码质量检查
2. **Prettier** - 代码格式化
3. **StyleLint** - 样式代码规范检查
4. **TypeScript** - 静态类型检查

## 文档维护

每个规范文档都是独立的，可以单独更新，不会影响其他文档。如果有新的开发规范或最佳实践，可以更新相应的文档或添加新的文档。

> 特别说明：本规范基于项目当前状态制定，随着项目演进可能需要调整。遇到规范与实际情况冲突时，请与团队讨论并更新规范。
