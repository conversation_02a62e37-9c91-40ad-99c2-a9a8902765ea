package com.xinghuo.common.database.plugins;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationInterceptor;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.processor.DsProcessor;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.xinghuo.common.database.util.ConnUtil;
import com.xinghuo.common.database.util.DataSourceUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.context.SpringContext;
import org.aopalliance.intercept.MethodInvocation;

/**
 * DynamicGeneratorInterceptor 动态数据源生成拦截器。
 *
 * 该拦截器继承自 DynamicDataSourceAnnotationInterceptor，用于处理动态数据源的切换。
 * 当接口继承了 DynamicSourceGeneratorInterface 接口，并且该接口上存在 @DS 注解时，
 * 根据注解中的数据源名称动态生成数据源并进行切换。
 *
 * 注意：该拦截器主要用于处理继承 DynamicSourceGeneratorInterface 接口的类，实现了根据接口注解生成动态数据源的功能。
 *
 * @see DynamicSourceGeneratorInterface
 * @see DS
 * @see DynamicDataSourceAnnotationInterceptor
 * @see DataSourceUtil
 * @see ConnUtil
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class DynamicGeneratorInterceptor extends DynamicDataSourceAnnotationInterceptor {

    private DynamicRoutingDataSource dynamicRoutingDataSource;
    private DefaultDataSourceCreator dataSourceCreator;

    /**
     * 构造函数。
     *
     * @param allowedPublicOnly 是否只允许公共方法
     * @param dsProcessor 数据源处理器
     */
    public DynamicGeneratorInterceptor(Boolean allowedPublicOnly, DsProcessor dsProcessor) {
        super(allowedPublicOnly, dsProcessor);
    }

    /**
     * 方法调用。
     *
     * @param methodInvocation 方法调用信息
     * @return 方法调用结果
     * @throws Throwable 可能抛出的异常
     */
    @Override
    public Object invoke(MethodInvocation methodInvocation) throws Throwable {
        try {
            // 是否继承动态生成源接口
            if (methodInvocation.getThis() instanceof DynamicSourceGeneratorInterface) {
                DS ds = methodInvocation.getThis().getClass().getAnnotation(DS.class);
                if (ds != null && StrXhUtil.isNotEmpty(ds.value())) {
                    String datasourceName = ds.value();
                    DynamicSourceGeneratorInterface m = (DynamicSourceGeneratorInterface) methodInvocation.getThis();
                    String now = null;
                    try {
                        boolean invalid = true;
                        if (Boolean.TRUE.equals(m.cachedConnection())) {
                            if (dynamicRoutingDataSource == null) {
                                dynamicRoutingDataSource = SpringContext.getBean(DynamicRoutingDataSource.class);
                                dataSourceCreator = SpringContext.getBean(DefaultDataSourceCreator.class);
                            }
                            if (dynamicRoutingDataSource.getDataSources().containsKey(datasourceName)) {
                                // if (dynamicRoutingDataSource.getCurrentDataSources().get(datasourceName).getConnection().isValid(5)) {
                                // 已存在当前动态数据源且数据源可用则不重新获取数据源配置
                                invalid = false;
                                // }
                            }
                        }
                        if (invalid) {
                            // 重新生成动态数据源
                            // 设置为默认数据源获取动态数据源信息
                            now = DynamicDataSourceContextHolder.push(null);
                            DataSourceUtil dataSource = m.getDataSource();
                            if (dataSource != null) {
                                DataSourceProperty dataSourceProperty = new DataSourceProperty();
                                dataSourceProperty.setUsername(dataSource.getUserName());
                                dataSourceProperty.setPassword(dataSource.getPassword());
                                dataSourceProperty.setUrl(ConnUtil.getUrl(dataSource));
                                dataSourceProperty.setDriverClassName(dataSource.getDriver());
                                dynamicRoutingDataSource.addDataSource(datasourceName,
                                        dataSourceCreator.createDataSource(dataSourceProperty));
                            }
                        }
                    } finally {
                        if (now != null) {
                            DynamicDataSourceContextHolder.poll();
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return methodInvocation.proceed();
    }
}
