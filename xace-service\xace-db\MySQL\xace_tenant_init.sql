/*
 Navicat Premium Data Transfer

 Source Server         : 0.212 - root（init）
 Source Server Type    : MySQL
 Source Server Version : 50739
 Source Host           : *************:3306
 Source Schema         : xh_tenant_init_344

 Target Server Type    : MySQL
 Target Server Version : 50739
 File Encoding         : 65001

 Date: 20/12/2022 17:05:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for base_account
-- ----------------------------
DROP TABLE IF EXISTS `base_account`;
CREATE TABLE `base_account`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_Account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账户',
  `F_Password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `F_RealName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `F_Gender` int(11) NULL DEFAULT NULL COMMENT '性别',
  `F_IsAdministrator` int(11) NULL DEFAULT NULL COMMENT '是否管理员',
  `F_Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteTime` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_account
-- ----------------------------
INSERT INTO `base_account` VALUES ('47ff0279-aa26a4fbeb59f630c8776f759', 'admin', '14e1b600b1fd579f47433b88e8d85291', 'admin', 1, 1, 'admin', 1, 1, '2021-09-18 18:01:08', '1', '2021-09-18 18:01:29', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for base_tenant
-- ----------------------------
DROP TABLE IF EXISTS `base_tenant`;
CREATE TABLE `base_tenant`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_EnCode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  `F_FullName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `F_CompanyName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司',
  `F_ExpiresTime` datetime(0) NULL DEFAULT NULL COMMENT '过期时间',
  `F_DbName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务名称',
  `F_IPAddress` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `F_IPAddressName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'IP所在城市',
  `F_SourceWebsite` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源网站',
  `F_Description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `F_SortCode` int(11) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_Dataschema` int(11) NULL DEFAULT NULL COMMENT '数据模式',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_tenant
-- ----------------------------
INSERT INTO `base_tenant` VALUES ('523954da-cc80e4c84bb7a5508bcd06fbe', '0929', '0929', '0929', '2022-10-29 11:02:04', 'xh_tenant_0Nu262r1421s', NULL, NULL, NULL, '', NULL, NULL, '2022-09-29 11:02:04', NULL, NULL, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for base_tenantlink
-- ----------------------------
DROP TABLE IF EXISTS `base_tenantlink`;
CREATE TABLE `base_tenantlink`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_FullName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '连接名称',
  `F_ConfigType` int(11) NULL DEFAULT NULL COMMENT '配置类型（0：主，1：从）',
  `F_Host` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主机地址',
  `F_Port` int(11) NULL DEFAULT NULL COMMENT '端口',
  `F_UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户',
  `F_Password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `F_ServiceName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务名称',
  `F_Description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `F_SortCode` bigint(20) NULL DEFAULT NULL COMMENT '排序',
  `F_EnabledMark` int(11) NULL DEFAULT NULL COMMENT '有效标志',
  `F_CreatorTime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改用户',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标志',
  `F_DeleteTime` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  `F_DbSchema` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表模式',
  `F_TableSpace` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表空间',
  `F_OracleParam` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'oracle参数',
  `F_ConnectionStr` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义连接语句',
  `F_TenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_DbType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库类型',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户数据连接' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_tenantlink
-- ----------------------------

-- ----------------------------
-- Table structure for base_tenantlog
-- ----------------------------
DROP TABLE IF EXISTS `base_tenantlog`;
CREATE TABLE `base_tenantlog`  (
  `F_Id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_TenantId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户主键',
  `F_LoginAccount` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录账户',
  `F_LoginIPAddress` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `F_LoginIPAddressName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'IP所在城市',
  `F_LoginSourceWebsite` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源网站',
  `F_LoginTime` datetime(0) NULL DEFAULT NULL COMMENT '登录时间',
  `F_Description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_tenantlog
-- ----------------------------
INSERT INTO `base_tenantlog` VALUES ('15f6c48f-bbdb94381853a15dc300b5d2a', '523954da-cc80e4c84bb7a5508bcd06fbe', '0929', '127.0.0.1', NULL, NULL, '2022-09-30 01:09:05', 'java:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36');
INSERT INTO `base_tenantlog` VALUES ('1caf046b-44ee74804962e88fd592a6cda', '410d0a14-4447542d6b5fe5979640ba915', '0412', '************', '本地局域网', NULL, '2022-04-28 11:01:33', 'java:Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36');
INSERT INTO `base_tenantlog` VALUES ('28149539-995d042eab174d41c9baec395', '410d0a14-4447542d6b5fe5979640ba915', '0412', '************', '本地局域网', NULL, '2022-04-16 16:29:13', 'java:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36');
INSERT INTO `base_tenantlog` VALUES ('485784c1-ff392417b9d3b206b90219c3f', '410d0a14-4447542d6b5fe5979640ba915', '0412', '************', '本地局域网', NULL, '2022-04-16 16:29:18', 'java:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36');
INSERT INTO `base_tenantlog` VALUES ('6341c5bc-4424e41ed860150ff1fb8e013', '410d0a14-4447542d6b5fe5979640ba915', '0412', '************', '本地局域网', NULL, '2022-04-28 11:01:25', 'java:Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36');
INSERT INTO `base_tenantlog` VALUES ('746f3de7-aae1f4af4868ef17bfd4b2389', '523954da-cc80e4c84bb7a5508bcd06fbe', '0929', '127.0.0.1', NULL, NULL, '2022-09-30 01:16:06', 'java:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36');
INSERT INTO `base_tenantlog` VALUES ('81f50b27-993f04aa09908fc973472d066', '523954da-cc80e4c84bb7a5508bcd06fbe', '0929', '127.0.0.1', NULL, NULL, '2022-09-29 11:23:40', 'java:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36');
INSERT INTO `base_tenantlog` VALUES ('c69b8210-22f234866bf5c6995d9828dd3', '410d0a14-4447542d6b5fe5979640ba915', '0412', '************', '本地局域网', NULL, '2022-04-16 16:29:12', 'java:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36');
INSERT INTO `base_tenantlog` VALUES ('d336bed5-55e1e4f60bf0edbc660337bbf', '523954da-cc80e4c84bb7a5508bcd06fbe', '0929', '127.0.0.1', NULL, NULL, '2022-09-29 11:23:42', 'java:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36');
INSERT INTO `base_tenantlog` VALUES ('e058e3da-00ee64f77bb5aac2b31c18e81', '410d0a14-4447542d6b5fe5979640ba915', '0412', '************', '本地局域网', NULL, '2022-04-28 11:01:28', 'java:Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36');

-- ----------------------------
-- Table structure for base_tenantsocials
-- ----------------------------
DROP TABLE IF EXISTS `base_tenantsocials`;
CREATE TABLE `base_tenantsocials`  (
  `F_Id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自然主键',
  `F_UserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `F_Account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户账号',
  `F_AccountName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户账号名称',
  `F_SocialType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方类型',
  `F_SocialId` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方账号id',
  `F_SocialName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方账号',
  `F_TenantId` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `F_CreatorUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `F_CreatorTime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `F_Description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `F_DeleteMark` int(11) NULL DEFAULT NULL COMMENT '删除标记',
  `F_DeleteTime` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `F_DeleteUserId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除用户',
  PRIMARY KEY (`F_Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '多租户第三方绑定表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_tenantsocials
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
