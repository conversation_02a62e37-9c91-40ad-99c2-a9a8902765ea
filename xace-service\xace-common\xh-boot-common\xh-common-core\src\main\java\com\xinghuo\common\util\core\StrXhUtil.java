package com.xinghuo.common.util.core;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 星火工具类 继承hutool的StrUtil
 *
 * <AUTHOR>
 * @since 2.0
 */
public class StrXhUtil  extends StrUtil {

    /**
     * 检查字符串是否非空且不等于 "null"。
     *
     * @param str 要检查的字符串
     * @return 如果字符串非空且不等于 "null"，返回 true；否则返回 false。
     */
    public static boolean isValidNotNull(CharSequence str) {
        return isNotBlank(str) && !StrUtil.equals("null", str);
    }

    public static boolean inStringIgnoreCase(String str, String... strs) {
        System.out.println("inStringIgnoreCase");
        if (str != null && strs != null) {
            return ArrayUtil.containsIgnoreCase(strs, str);
        }
        return false;
    }

    /**
     * 将字符串中连续出现的相同子字符串（即重复两次）替换为一次。
     *
     * @param value 要进行处理的原始字符串。
     * @param str   要替换成的目标子字符串。
     * @return 替换完成的字符串。
     */
    public static String replaceMoreStrToOneStr(String value, String str) {
        // 检查输入字符串是否为空
        if (isEmpty(value) || isEmpty(str)) {
            return value;
        }
        // 构造连续两次出现的子字符串
        String twoStr = str + str;
        // 使用循环替换连续两次出现的子字符串，直到不再存在为止
        while (value.contains(twoStr)) {
            value = value.replace(twoStr, str);
        }
        return value;
    }

    public static void prettyPrint(Object... objects){
         for(Object o:objects){
            System.err.println(o.getClass().getName()+":"+ toString(o).length());
        }
    }



}
