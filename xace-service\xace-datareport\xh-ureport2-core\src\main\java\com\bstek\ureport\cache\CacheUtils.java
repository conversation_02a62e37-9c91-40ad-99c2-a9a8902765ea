/*******************************************************************************
 * Copyright 2017 Bstek
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.cache;

import com.bstek.ureport.chart.ChartData;
import com.bstek.ureport.definition.ReportDefinition;
import com.bstek.ureport.model.Cell;
import com.bstek.ureport.utils.RedisUtil;
import com.bstek.ureport.utils.ServletUtil;
import com.bstek.ureport.utils.UnitUtils;
import com.xinghuo.common.util.json.JsonXhUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR> @since 3月8日
 */
public class CacheUtils implements ApplicationContextAware{
	private static ReportCache reportCache;
	private static ReportDefinitionCache reportDefinitionCache;
	private static String CHART_DATA_key="_chart_data_";
	private static RedisUtil redisUtil;

	@Autowired
	public void setRedisUtil(RedisUtil redisUtil) {
		CacheUtils.redisUtil = redisUtil;
	}

	@SuppressWarnings("unchecked")
	public static ChartData getChartData(String chartId){
		HttpServletRequest request = ServletUtil.getRequest();
		String id = request.getParameter("token") + CHART_DATA_key;
		String data = redisUtil.GetHashValues(id, chartId);
		if(StringUtils.isNotEmpty(data)){
			ChartData chartData = JsonXhUtil.toBean(data,ChartData.class);
			return chartData;
		}
		return null;
	}

	public static void storeChartDataMap(Map<String, ChartData> map){
		String key=CHART_DATA_key;
		if(reportCache!=null){
		    reportCache.storeObject(key, map);
		}
	}

	public static Object getObject(String file){
		if(reportCache!=null){
			return reportCache.getObject(file);
		}
		return null;
	}
	public static void storeObject(String file,Object obj){
		if(reportCache!=null){
			reportCache.storeObject(file, obj);
		}
	}

	public static void setChartData(String chartId) {
		HttpServletRequest request = ServletUtil.getRequest();
		String id = request.getParameter("token") + CHART_DATA_key;
		String base64Data = request.getParameter("_base64Data");
		String prefix = "data:image/png;base64,";
		if (base64Data != null) {
			if (base64Data.startsWith(prefix)) {
				base64Data = base64Data.substring(prefix.length(), base64Data.length());
			}
		}
		Cell cell = new Cell();
		cell.setName(chartId);
		ChartData chartData = new ChartData();
		String width = request.getParameter("_width");
		String height = request.getParameter("_height");
		chartData.setBase64Data(base64Data);
		chartData.setHeight(UnitUtils.pixelToPoint(Integer.valueOf(height)));
		chartData.setWidth(UnitUtils.pixelToPoint(Integer.valueOf(width)));
		redisUtil.InsertHash(id,chartId, JsonXhUtil.toJSONString(chartData));
		redisUtil.expire(id,300);
	}

	public static ReportDefinition getReportDefinition(String file){
		return reportDefinitionCache.getReportDefinition(file);
	}
	public static void cacheReportDefinition(String file,ReportDefinition reportDefinition){
		reportDefinitionCache.cacheReportDefinition(file, reportDefinition);
	}

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		Collection<ReportCache> coll=applicationContext.getBeansOfType(ReportCache.class).values();
		for(ReportCache cache:coll){
			if(cache.disabled()){
				continue;
			}
			reportCache=cache;
			break;
		}
		Collection<ReportDefinitionCache> reportCaches=applicationContext.getBeansOfType(ReportDefinitionCache.class).values();
		if(reportCaches.size()==0){
			reportDefinitionCache=new DefaultMemoryReportDefinitionCache();
		}else{
			reportDefinitionCache=reportCaches.iterator().next();
		}
	}
}
