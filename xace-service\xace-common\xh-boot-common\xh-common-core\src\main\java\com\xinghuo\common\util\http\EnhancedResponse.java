package com.xinghuo.common.util.http;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xinghuo.common.util.json.JsonXhUtil;
import org.apache.hc.client5.http.fluent.Response;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 封装了Response对象，提供额外的方法来处理响应。
 * 默认使用UTF-8字符集。
 */

public class EnhancedResponse {
    private final Response response;

    private String returnContent;

    public EnhancedResponse(Response response) {
        this.response = response;
        try {
            this.returnContent = response.returnContent().asString(StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 解析响应为ObjectNode对象。
     */
    public ObjectNode asJson()  {
        // 使用Jackson库解析响应体为JsonNode
        return JsonXhUtil.parseObject(returnContent);
    }

    /**
     * 获取响应体的字符串表示。默认使用UTF-8编码
     */
    public String asText()  {
        // 读取响应体为字符串
        return returnContent;
    }

}
