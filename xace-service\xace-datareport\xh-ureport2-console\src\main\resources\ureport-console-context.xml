<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">
	<import resource="classpath:ureport-core-context.xml"/>
	<import resource="classpath:ureport-font-context.xml"/>
	
	<bean id="ureport.datasourceServletAction" class="com.bstek.ureport.console.designer.DatasourceServletAction"></bean>
	<bean id="ureport.resourceLoaderServletAction" class="com.bstek.ureport.console.res.ResourceLoaderServletAction"></bean>
	<bean id="ureport.designerServletAction" class="com.bstek.ureport.console.designer.DesignerServletAction">
		<property name="reportRender" ref="ureport.reportRender"></property>
		<property name="reportParser" ref="ureport.reportParser"></property>
	</bean>
	
	<bean id="ureport.searchFormDesignerAction" class="com.bstek.ureport.console.designer.SearchFormDesignerAction"></bean>
	
	<bean id="ureport.htmlPreviewServletAction" class="com.bstek.ureport.console.html.HtmlPreviewServletAction">
		<property name="exportManager" ref="ureport.exportManager"></property>
		<property name="reportBuilder" ref="ureport.reportBuilder"></property>
		<property name="reportRender" ref="ureport.reportRender"></property>
	</bean>
	<bean id="ureport.exportWordServletAction" class="com.bstek.ureport.console.word.ExportWordServletAction">
		<property name="exportManager" ref="ureport.exportManager"></property>
		<property name="reportBuilder" ref="ureport.reportBuilder"></property>
	</bean>
	
	<bean id="ureport.exportPdfServletAction" class="com.bstek.ureport.console.pdf.ExportPdfServletAction">
		<property name="exportManager" ref="ureport.exportManager"></property>
		<property name="reportBuilder" ref="ureport.reportBuilder"></property>
		<property name="reportRender" ref="ureport.reportRender"></property>
	</bean>
	
	<bean id="ureport.exportExcelServletAction" class="com.bstek.ureport.console.excel.ExportExcelServletAction">
		<property name="exportManager" ref="ureport.exportManager"></property>
		<property name="reportBuilder" ref="ureport.reportBuilder"></property>
	</bean>
	
	<bean id="ureport.exportExcel97ServletAction" class="com.bstek.ureport.console.excel.ExportExcel97ServletAction">
		<property name="exportManager" ref="ureport.exportManager"></property>
		<property name="reportBuilder" ref="ureport.reportBuilder"></property>
	</bean>
	
	<bean id="ureport.imageServletAction" class="com.bstek.ureport.console.image.ImageServletAction"></bean>
	<bean id="ureport.importExcelServletAction" class="com.bstek.ureport.console.importexcel.ImportExcelServletAction"></bean>
	
	<bean id="ureport.chartServletAction" class="com.bstek.ureport.console.chart.ChartServletAction"></bean>
	
	<bean id="ureport.httpSessionReportCache" class="com.bstek.ureport.console.cache.HttpSessionReportCache">
		<property name="disabled" value="${ureport.disableHttpSessionReportCache}"></property>
	</bean>
</beans>