<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate">
          <Icon icon="ant-design:plus-outlined" />
          新增WBS模板
        </a-button>
        <a-button
          type="primary"
          ghost
          @click="handleBatchPublish"
          :disabled="!hasSelected"
        >
          <Icon icon="ant-design:check-outlined" />
          批量发布
        </a-button>
        <a-button 
          type="primary" 
          ghost 
          @click="handleBatchArchive" 
          :disabled="!hasSelected"
        >
          <Icon icon="ant-design:inbox-outlined" />
          批量归档
        </a-button>
        <a-button 
          color="error" 
          @click="handleBatchDelete" 
          :disabled="!hasSelected"
        >
          <Icon icon="ant-design:delete-outlined" />
          批量删除
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                label: '查看详情',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'clarity:note-edit-line',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:copy-outlined',
                label: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                label: '删除',
                onClick: handleDelete.bind(null, record),
              },
            ]"
            :dropDownActions="[
              {
                label: getStatusActionLabel(record.status),
                onClick: getStatusAction(record),
              },
              {
                label: 'WBS设计',
                onClick: handleWbsDesign.bind(null, record),
              },
              {
                label: '应用到项目',
                onClick: handleApplyToProjects.bind(null, record),
              },
              {
                label: '使用情况',
                onClick: handleUsageInfo.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <FormDrawer @register="registerFormDrawer" @reload="handleSuccess" />
    <DetailDrawer @register="registerDetailDrawer" />
    <WbsTemplateCopyModal @register="registerCopyModal" @success="handleSuccess" />
    <WbsDesignDrawer @register="registerWbsDesignDrawer" @reload="handleSuccess" />
    <ApplyToProjectsModal @register="registerApplyModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, computed } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useDrawer } from '/@/components/Drawer';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  
  import FormDrawer from './FormDrawer.vue';
  import DetailDrawer from './DetailDrawer.vue';
  import WbsTemplateCopyModal from './WbsTemplateCopyModal.vue';
  import WbsDesignDrawer from './WbsDesignDrawer.vue';
  import ApplyToProjectsModal from './ApplyToProjectsModal.vue';
  import { columns, searchFormSchema } from './wbsTemplate.data';
  import {
    getWbsTemplateList,
    deleteWbsTemplate,
    batchDeleteWbsTemplate,
    publishWbsTemplate,
    archiveWbsTemplate,
    batchPublishWbsTemplate,
    batchArchiveWbsTemplate,
    getWbsTemplateUsageInfo,
  } from '/@/api/project/wbsTemplate';

  defineOptions({ name: 'project-template-wbsTemplate' });

  const { createMessage } = useMessage();
  const { hasPermission } = usePermission();

  // 搜索信息
  const searchInfo = reactive<Recordable>({});

  // 表格配置
  const [registerTable, { getSelectRows, clearSelectedRowKeys, reload }] = useTable({
    title: 'WBS计划模板列表',
    api: getWbsTemplateList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    handleSearchInfoFn(info) {
      console.log('handleSearchInfoFn', info);
      return info;
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: undefined,
    },
    rowSelection: {
      type: 'checkbox',
    },
  });

  // 抽屉配置
  const [registerFormDrawer, { openDrawer: openFormDrawer }] = useDrawer();
  const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
  const [registerWbsDesignDrawer, { openDrawer: openWbsDesignDrawer }] = useDrawer();

  // 模态框配置
  const [registerCopyModal, { openModal: openCopyModal }] = useModal();
  const [registerApplyModal, { openModal: openApplyModal }] = useModal();

  // 计算属性
  const hasSelected = computed(() => getSelectRows().length > 0);

  // 获取状态操作标签
  function getStatusActionLabel(status: string) {
    switch (status) {
      case 'draft':
        return '发布';
      case 'published':
        return '归档';
      case 'archived':
        return '发布';
      default:
        return '发布';
    }
  }

  // 获取状态操作函数
  function getStatusAction(record: Recordable) {
    switch (record.status) {
      case 'draft':
        return () => handlePublish(record);
      case 'published':
        return () => handleArchive(record);
      case 'archived':
        return () => handlePublish(record);
      default:
        return () => handlePublish(record);
    }
  }

  // 新增
  function handleCreate() {
    openFormDrawer(true, {
      isUpdate: false,
    });
  }

  // 编辑
  function handleEdit(record: Recordable) {
    openFormDrawer(true, {
      record,
      isUpdate: true,
    });
  }

  // 查看详情
  function handleView(record: Recordable) {
    openDetailDrawer(true, { record });
  }

  // 复制
  function handleCopy(record: Recordable) {
    openCopyModal(true, { record });
  }

  // WBS设计
  function handleWbsDesign(record: Recordable) {
    openWbsDesignDrawer(true, { record });
  }

  // 应用到项目
  function handleApplyToProjects(record: Recordable) {
    openApplyModal(true, { record });
  }

  // 删除
  async function handleDelete(record: Recordable) {
    try {
      await deleteWbsTemplate(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      console.error('删除失败:', error);
    }
  }

  // 批量删除
  async function handleBatchDelete() {
    const rows = getSelectRows();
    if (rows.length === 0) {
      createMessage.warning('请选择要删除的数据');
      return;
    }

    try {
      const ids = rows.map((row) => row.id);
      await batchDeleteWbsTemplate(ids);
      createMessage.success('批量删除成功');
      clearSelectedRowKeys();
      reload();
    } catch (error) {
      console.error('批量删除失败:', error);
    }
  }

  // 发布
  async function handlePublish(record: Recordable) {
    try {
      await publishWbsTemplate(record.id);
      createMessage.success('发布成功');
      reload();
    } catch (error) {
      console.error('发布失败:', error);
    }
  }

  // 归档
  async function handleArchive(record: Recordable) {
    try {
      await archiveWbsTemplate(record.id);
      createMessage.success('归档成功');
      reload();
    } catch (error) {
      console.error('归档失败:', error);
    }
  }

  // 批量发布
  async function handleBatchPublish() {
    const rows = getSelectRows();
    if (rows.length === 0) {
      createMessage.warning('请选择要发布的数据');
      return;
    }

    try {
      const ids = rows.map((row) => row.id);
      await batchPublishWbsTemplate(ids);
      createMessage.success('批量发布成功');
      clearSelectedRowKeys();
      reload();
    } catch (error) {
      console.error('批量发布失败:', error);
    }
  }

  // 批量归档
  async function handleBatchArchive() {
    const rows = getSelectRows();
    if (rows.length === 0) {
      createMessage.warning('请选择要归档的数据');
      return;
    }

    try {
      const ids = rows.map((row) => row.id);
      await batchArchiveWbsTemplate(ids);
      createMessage.success('批量归档成功');
      clearSelectedRowKeys();
      reload();
    } catch (error) {
      console.error('批量归档失败:', error);
    }
  }

  // 使用情况
  async function handleUsageInfo(record: Recordable) {
    try {
      const result = await getWbsTemplateUsageInfo(record.id);
      // TODO: 显示使用情况信息
      console.log('使用情况:', result);
      createMessage.info('使用情况查询功能待完善');
    } catch (error) {
      console.error('获取使用情况失败:', error);
    }
  }

  // 操作成功回调
  function handleSuccess() {
    reload();
  }
</script>
