<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="FacetManager">
    <facet type="JRebel" name="JRebel">
      <configuration>
        <option name="ideModuleStorage">
          <map>
            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
            <entry key="jrebelEnabled" value="true" />
            <entry key="lastExternalPluginCheckTime" value="1744072076272" />
            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD54aC1jb21tb24tZGF0YWJhc2U8L2lkPjxjbGFzc3BhdGg+PGRpciBuYW1lPSJHOi92Mi9wZC14YWNlLXYyL3hhY2Utc2VydmljZS94YWNlLWNvbW1vbi94aC1ib290LWNvbW1vbi94aC1jb21tb24tZGF0YWJhc2UvdGFyZ2V0L2NsYXNzZXMiPjwvZGlyPjwvY2xhc3NwYXRoPjwvYXBwbGljYXRpb24+" />
          </map>
        </option>
        <option name="version" value="19" />
      </configuration>
    </facet>
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
  </component>
</module>