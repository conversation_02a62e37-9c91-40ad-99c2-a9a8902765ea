package com.xinghuo.common.util.tree;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 构建和表示树结构的数据模型
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
public class TreeViewModel {
    /**
     * 节点唯一标识符
     */
    private String id;

    /**
     * 节点代码
     */
    private String code;

    /**
     * 节点文本内容
     */
    private String text;

    /**
     * 节点标题
     */
    private String title;

    /**
     * 父节点ID
     */
    private String parentId;

    /**
     * 节点选中状态
     */
    private Integer checkstate;

    /**
     * 是否显示节点勾选框（默认：true）
     */
    private Boolean showcheck = true;

    /**
     * 是否展开节点（默认：true）
     */
    private Boolean isexpand = true;

    /**
     * 节点是否完整（默认：true）
     */
    private Boolean complete = true;

    /**
     * 节点关联的图片路径
     */
    private String img;

    /**
     * 节点CSS样式类名
     */
    private String cssClass;

    /**
     * 节点是否包含子节点
     */
    private Boolean hasChildren;

    /**
     * 存储节点额外属性的键值对集合
     */
    private Map<String, Object> ht;

    /**
     * 节点是否可被点击（默认：false）
     */
    private Boolean click;

    /**
     * 子节点列表
     */
    private List<TreeViewModel> childNodes;
}

