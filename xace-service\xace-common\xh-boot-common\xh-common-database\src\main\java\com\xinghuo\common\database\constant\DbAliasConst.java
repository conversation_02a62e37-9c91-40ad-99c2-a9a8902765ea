package com.xinghuo.common.database.constant;

import com.xinghuo.common.exception.DataException;
import lombok.AllArgsConstructor;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 字段别名特殊标识
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class DbAliasConst {

    /**
     * 允空
     */
    public static final String NULL = "NULL";

    /**
     * 非空
     */
    public static final String NOT_NULL = "NOT NULL";

    public static final NumFieldAttr<String> ALLOW_NULL;
    public static final NumFieldAttr<Boolean> PRIMARY_KEY;
    public static final NumFieldAttr<Boolean> AUTO_INCREMENT;

    static {
        /**
         * 允空
         * 0:空值 NULL、1:非空值 NOT NULL
         */
        Map<Integer, String> allowNullMap = new HashMap<>();
        allowNullMap.put(1, NULL);
        allowNullMap.put(0, NOT_NULL);
        allowNullMap.put(-1, NOT_NULL);
        ALLOW_NULL = new NumFieldAttr<>(Collections.unmodifiableMap(allowNullMap));

        /**
         * 主键
         *  0:非主键、1：主键
         */
        Map<Integer, Boolean> primaryKeyMap = new HashMap<>();
        primaryKeyMap.put(1, true);
        primaryKeyMap.put(0, false);
        primaryKeyMap.put(-1, false);
        PRIMARY_KEY = new NumFieldAttr<>(Collections.unmodifiableMap(primaryKeyMap));

        Map<Integer, Boolean> autoIncrementMap = new HashMap<>();
        autoIncrementMap.put(1, true);
        autoIncrementMap.put(0, false);
        autoIncrementMap.put(-1, false);
        AUTO_INCREMENT = new NumFieldAttr<>(Collections.unmodifiableMap(autoIncrementMap));
    }

    /**
     * 数值对应字段属性
     * @param <T>
     */
    @AllArgsConstructor
    public static class NumFieldAttr<T>{

        private Map<Integer, T> config;

        /**
         * 获取标识
         */
        public T getSign(Integer i) {
            return config.get(i == null ? -1 : i);
        }

        /**
         * 获取数值
         */
        public Integer getNum(T sign) throws DataException {
            if(sign == null){
                return 0;
            }
            Optional<Map.Entry<Integer, T>> first = config.entrySet().stream().filter(map -> map.getValue().equals(sign)).findFirst();
            if(first.isPresent()){
                return first.get().getKey();
            }else {
                throw new DataException("表示对应获取数值失败");
            }
        }
    }


}
