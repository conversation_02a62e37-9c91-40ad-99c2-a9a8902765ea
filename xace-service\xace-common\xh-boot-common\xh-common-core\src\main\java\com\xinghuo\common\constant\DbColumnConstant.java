package com.xinghuo.common.constant;
/**
 * 对数据库表中特殊字段
 * F_ 开头表示数据库中用到的内建字段。
 *
 * <AUTHOR>
 * @date 2023-12-23
 */
public class DbColumnConstant {

    // ID 相关
    public static final String F_ID = "F_ID";
    public static final String F_TENANT_ID = "F_TenantId";

    // 创建信息
    public static final String F_CREATOR_USER_ID = "F_CreatorUserId";
    public static final String F_CREATOR_TIME = "F_CreatorTime";
    // 修改信息
    public static final String F_LAST_MODIFY_USER_ID = "F_LastModifyUserId";
    public static final String F_LAST_MODIFY_TIME = "F_LastModifyTime";

    //20241026新
    public static final String F_CREATED_BY = "f_created_by";
    public static final String F_CREATED_AT = "f_created_at";

    public static final String F_LAST_UPDATED_BY = "f_last_updated_by";
    public static final String F_LAST_UPDATED_AT = "f_last_updated_at";

    public static final String F_DELETED_BY = "f_deleted_by";
    public static final String F_DELETED_AT = "f_deleted_at";

    // 删除信息
    public static final String F_DELETE_MARK = "F_DeleteMark";
    public static final String F_DELETE_USER_ID = "F_DeleteUserId";
    public static final String F_DELETE_TIME = "F_DeleteTime";

    //查询数据 是否全部数据 默认是Page
    /**
     * 分页
     */
    public static final int DATA_TYPE_PAGE = 0;

    /**
     * 列表
     */
    public static final int DATA_TYPE_LIST = 1;
}
