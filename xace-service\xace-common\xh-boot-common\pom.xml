<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xh-common</artifactId>
        <groupId>com.xinghuo.xace</groupId>
        <version>2.1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xh-boot-common</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>xh-common-core</module>
        <module>xh-common-database</module>
        <module>xh-common-cache</module>
        <!--<module>xh-common-security</module>
                <module>xh-common-auth</module>
        <module>xh-common-swagger</module>
                <module>xh-common-office</module>

        -->

        <module>xh-common-connector</module>
        <module>xh-common-sms</module>
    <module>xh-common-shardingsphere</module>
</modules>

<dependencies>
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
    </dependency>
</dependencies>

<!-- 手机个推 -->
    <repositories>
        <repository>
            <id>getui-nexus</id>
            <url>http://mvn.gt.igexin.com/nexus/content/repositories/releases/</url>
        </repository>
    </repositories>

</project>
