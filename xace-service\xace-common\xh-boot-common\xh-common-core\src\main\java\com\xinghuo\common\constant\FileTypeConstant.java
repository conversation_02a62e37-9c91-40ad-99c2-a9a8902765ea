package com.xinghuo.common.constant;

/**
 * 文件类型参数
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class FileTypeConstant {
    /**
     * 用户头像存储路径
     */
    public static final String USERAVATAR = "useravatar";
    /**
     * 邮件文件存储路径
     */
    public static final String MAIL = "mail";
    /**
     * IM聊天图片+语音存储路径
     */
    public static final String IM = "im";
    /**
     * 临时文件存储路径
     */
    public static final String WORKFLOW = "workflow";
    /**
     * 前端附件文件目录
     */
    public static final String ANNEX = "annex";
    public static final String ANNEXPIC = "annexpic";
    /**
     * 数据库备份文件路径
     */
    public static final String DATABACKUP = "dataBackup";
    /**
     * 文档管理存储路径
     */
    public static final String DOCUMENT = "document";
    /**
     * 临时文件存储路径
     */
    public static final String TEMPORARY = "temporary";
    /**
     * 允许上传文件类型
     */
    public static final String ALLOWUPLOADFILETYPE = "allowuploadfiletype";
    /**
     * 文件在线预览存储pdf
     */
    public static final String DOCUMENTPREVIEWPATH = "preview";
    /**
     * 文件模板存储路径
     */
    public static final String TEMPLATEFILE = "templatefile";
    /**
     * 前端文件目录
     */
    public static final String SERVICEDIRECTORY = "servicedirectory";
    /**
     * 后端文件目录
     */
    public static final String WEBDIRECTORY = "webdirectory";
    /**
     * 代码生成器生成位置
     */
    public static final String CODETEMP = "codetemp";
    /**
     * 导出
     */
    public static final String EXPORT = "export";
    /**
     * 文件预览
     */
    public static final String DOCUMENTPREVIEW = "documentpreview";
    /**
     * 大屏路径
     */
    public static final String BIVISUALPATH = "bivisualpath";

    /**
     * 初始化模板
     */
    public static final String TEMPLATECODEPATH = "templatecodepath";

    /**
     * 报表路径
     */
    public static final String REPORTPATH = "reportfile";

    /**
     * 文件zip打包下载临时文件路径
     */
    public static final String FILEZIPDOWNTEMPPATH = "filezipdownloadtemppath";

}
