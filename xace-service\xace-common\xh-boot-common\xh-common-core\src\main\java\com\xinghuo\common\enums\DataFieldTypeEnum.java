package com.xinghuo.common.enums;
/**
 * 数据权限字段类型
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public enum DataFieldTypeEnum {
	/**
	 * 浮点型
	 */
	Double("Double"),
	/**
	 * 字符型
	 */
	Varchar("String"),

	/**
	 * 数值型
	 */
	Number("Int32");

	private String message;

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	DataFieldTypeEnum(String message) {
		this.message = message;
	}
}
