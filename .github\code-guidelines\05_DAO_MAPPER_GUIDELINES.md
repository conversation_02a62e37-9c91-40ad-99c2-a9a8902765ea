# DAO/Mapper 层规范

## 基本结构

DAO/Mapper层负责定义数据库访问操作，在项目中主要基于MyBatis-Plus框架实现。

### 位置与命名

* **包路径:** `com.xinghuo.[模块名].dao` 和 `com.xinghuo.[模块名].dao.mapper`
* **命名规范:** 以 `Mapper` 结尾，如 `UserMapper`，`ProductCategoryMapper`

### 基础设置

* 继承自 `XHBaseMapper<T>` 接口，获取基本CRUD操作
* 使用 `@Mapper` 注解或在配置类中统一扫描
* 对应的XML文件应放在 `resources/mapper` 目录下

## 完整示例

```java

import com.xinghuo.common.base.dao.XHBaseMapper;

/**
 * 产品分类Mapper接口
 *
 * @author： frying52
 * date： 2023-05-15
 */
@Mapper
public interface ProductCategoryMapper extends XHBaseMapper<ProductCategoryEntity> {
    
    /**
     * 根据父分类ID查询子分类列表
     *
     * @param parentId 父分类ID
     * @return 分类列表
     */
    List<ProductCategoryEntity> selectByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询分类树结构
     *
     * @return 分类树列表
     */
    List<ProductCategoryTreeVO> selectCategoryTree();
    
    /**
     * 批量更新排序值
     *
     * @param sortList 排序列表
     * @return 影响行数
     */
    int batchUpdateSort(List<ProductCategorySortDTO> sortList);
}
```

## XML映射文件示例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.admin.dao.ProductCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinghuo.admin.entity.ProductCategoryEntity">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="PARENT_ID" property="parentId" />
        <result column="SORT" property="sort" />
        <result column="STATUS" property="status" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>
    
    <!-- 树结构结果映射 -->
    <resultMap id="TreeResultMap" type="com.xinghuo.admin.model.vo.ProductCategoryTreeVO">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="PARENT_ID" property="parentId" />
        <result column="SORT" property="sort" />
        <result column="STATUS" property="status" />
        <collection property="children" ofType="com.xinghuo.admin.model.vo.ProductCategoryTreeVO" 
                    column="ID" select="selectChildrenById"/>
    </resultMap>
    
    <!-- 自定义查询方法 -->
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT * FROM PRODUCT_CATEGORY WHERE PARENT_ID = #{parentId} ORDER BY SORT ASC
    </select>
    
    <!-- 查询分类树根节点 -->
    <select id="selectCategoryTree" resultMap="TreeResultMap">
        SELECT * FROM PRODUCT_CATEGORY WHERE PARENT_ID = 0 ORDER BY SORT ASC
    </select>
    
    <!-- 查询子节点 -->
    <select id="selectChildrenById" resultMap="TreeResultMap">
        SELECT * FROM PRODUCT_CATEGORY WHERE PARENT_ID = #{id} ORDER BY SORT ASC
    </select>
    
    <!-- 批量更新排序值 -->
    <update id="batchUpdateSort" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE PRODUCT_CATEGORY SET SORT = #{item.sort} WHERE ID = #{item.id}
        </foreach>
    </update>
</mapper>
```

## 查询构造器使用示例

```java
// 使用LambdaQueryWrapper构造查询条件
LambdaQueryWrapper<ProductCategoryEntity> wrapper = new LambdaQueryWrapper<>();
wrapper.like(StrUtil.isNotBlank(query.getName()), ProductCategoryEntity::getName, query.getName());
wrapper.eq(query.getParentId() != null, ProductCategoryEntity::getParentId, query.getParentId());
wrapper.eq(query.getStatus() != null, ProductCategoryEntity::getStatus, query.getStatus());
wrapper.orderByAsc(ProductCategoryEntity::getSort);

// 执行查询
List<ProductCategoryEntity> list = productCategoryMapper.selectList(wrapper);
```

## 设计原则

1. **接口设计**
   * 方法名应明确表达SQL操作意图，如select*, insert*, update*, delete*
   * 参数应明确，使用 `@Param` 注解标识XML中使用的参数名
   * 返回类型应明确，尽量不使用 `Map` 类型

2. **SQL编写**
   * 复杂SQL应写在XML文件中，而非注解中
   * 注意SQL注入问题，不要拼接SQL
   * 大量数据操作应考虑分页
   * 避免使用 `*` 查询所有字段，明确指定需要的字段

3. **性能考虑**
   * 合理设置索引
   * 避免子查询和多表连接，必要时拆分成多次简单查询
   * 大数据量操作考虑批处理
   * 合理使用缓存减少数据库压力

## 最佳实践

1. 优先使用MyBatis-Plus提供的内置方法，如 `selectById`, `insert`, `updateById`, `deleteById`
2. **【重要】优先使用QueryWrapper替代自定义SQL方法**
3. 避免在Mapper接口使用默认方法实现业务逻辑
4. 复杂条件查询使用 `QueryWrapper` 或 `LambdaQueryWrapper` 构建条件
5. 分页查询使用MyBatis-Plus的 `Page` 对象
6. 对于需要多表联查的复杂查询，可以定义VO对象映射结果
7. 批量操作注意性能，可以使用 `saveBatch`, `updateBatchById` 等批处理方法

---

## 🚨 重要规范：优先使用QueryWrapper替代自定义SQL

### 强制要求

**新开发的功能必须优先使用MyBatis Plus的QueryWrapper来实现数据查询和操作，避免编写自定义SQL方法。**

### 为什么使用QueryWrapper？

1. **避免XML映射维护**：无需创建和维护XML映射文件
2. **类型安全**：编译时检查，避免SQL语法错误
3. **代码简洁**：逻辑清晰，易于维护和理解
4. **数据库兼容性**：自动适配不同数据库方言
5. **动态查询**：更容易构建动态查询条件

### 实践对比

#### ❌ 错误做法（避免）

```java
// Mapper接口中定义自定义方法
@Mapper
public interface TemplateRelationMapper extends XHBaseMapper<TemplateRelationEntity> {
    List<String> selectTargetTemplateIds(@Param("sourceTemplateId") String sourceTemplateId,
                                        @Param("sourceTemplateType") String sourceTemplateType,
                                        @Param("targetTemplateType") String targetTemplateType);

    int deleteBySourceTemplate(@Param("sourceTemplateId") String sourceTemplateId,
                              @Param("sourceTemplateType") String sourceTemplateType);
}
```

#### ✅ 正确做法（推荐）

```java
// Mapper接口保持简洁
@Mapper
public interface TemplateRelationMapper extends XHBaseMapper<TemplateRelationEntity> {
    // 不定义自定义SQL方法，所有操作通过QueryWrapper实现
}

// Service层使用QueryWrapper + Lambda表达式（强烈推荐）
@Service
public class TemplateRelationServiceImpl extends BaseServiceImpl<TemplateRelationMapper, TemplateRelationEntity> {

    public List<String> getTargetTemplateIds(String sourceTemplateId, String sourceTemplateType, String targetTemplateType) {
        QueryWrapper<TemplateRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .select(TemplateRelationEntity::getTargetTemplateId)
                   .eq(TemplateRelationEntity::getSourceTemplateId, sourceTemplateId)
                   .eq(TemplateRelationEntity::getSourceTemplateType, sourceTemplateType)
                   .eq(TemplateRelationEntity::getTargetTemplateType, targetTemplateType);

        List<TemplateRelationEntity> relations = this.list(queryWrapper);
        return relations.stream()
                       .map(TemplateRelationEntity::getTargetTemplateId)
                       .collect(Collectors.toList());
    }

    public void deleteBySourceTemplate(String sourceTemplateId, String sourceTemplateType) {
        QueryWrapper<TemplateRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(TemplateRelationEntity::getSourceTemplateId, sourceTemplateId)
                   .eq(TemplateRelationEntity::getSourceTemplateType, sourceTemplateType);
        this.remove(queryWrapper);
    }
}
```

### 🔥 强制要求：使用Lambda表达式避免字段硬编码

**必须使用 `queryWrapper.lambda()` 方法，避免字段名硬编码！**

#### ❌ 错误写法（字段硬编码）
```java
QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
wrapper.eq("status", 1)  // ❌ 字段名硬编码，容易出错
       .like("name", "张")
       .between("age", 18, 65);
```

#### ✅ 正确写法（Lambda表达式）
```java
QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
wrapper.lambda()
       .eq(UserEntity::getStatus, 1)  // ✅ 类型安全，编译时检查
       .like(UserEntity::getName, "张")
       .between(UserEntity::getAge, 18, 65);
```

### 常用QueryWrapper + Lambda操作示例

```java
// 1. 基本查询
QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
wrapper.lambda()
       .eq(UserEntity::getStatus, 1)
       .like(UserEntity::getName, "张")
       .between(UserEntity::getAge, 18, 65)
       .orderByDesc(UserEntity::getCreateTime);

// 2. 选择特定字段
QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
wrapper.lambda()
       .select(UserEntity::getId, UserEntity::getName, UserEntity::getEmail)
       .eq(UserEntity::getStatus, 1);

// 3. 统计查询
QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
wrapper.lambda().eq(UserEntity::getStatus, 1);
long count = this.count(wrapper);

// 4. 批量删除
QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
wrapper.lambda().in(UserEntity::getId, Arrays.asList("1", "2", "3"));
this.remove(wrapper);

// 5. 动态条件
QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
wrapper.lambda()
       .eq(StringUtils.isNotBlank(name), UserEntity::getName, name)
       .eq(status != null, UserEntity::getStatus, status)
       .ge(startTime != null, UserEntity::getCreateTime, startTime);

// 6. 复杂查询
QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
wrapper.lambda()
       .eq(UserEntity::getStatus, 1)
       .and(w -> w.like(UserEntity::getName, "张").or().like(UserEntity::getEmail, "zhang"))
       .orderByDesc(UserEntity::getCreateTime);
```

### Lambda表达式的优势

1. **类型安全**：编译时检查字段名，避免拼写错误
2. **重构友好**：字段名变更时IDE会自动更新
3. **代码提示**：IDE提供完整的代码提示和自动补全
4. **可读性强**：代码更清晰，易于理解和维护

### 何时可以使用自定义SQL

仅在以下情况下才考虑使用自定义SQL：

1. **复杂的多表联查**：涉及3个以上表的复杂关联查询
2. **特殊的SQL函数**：需要使用数据库特有的函数或语法
3. **性能优化**：经过测试证明QueryWrapper性能不满足要求
4. **复杂的聚合查询**：涉及复杂的GROUP BY、HAVING等聚合操作

### 迁移指南

对于现有的自定义SQL方法，建议按以下步骤迁移：

1. **评估复杂度**：判断是否可以用QueryWrapper实现
2. **编写QueryWrapper版本**：在Service层实现相同功能
3. **测试验证**：确保功能和性能符合要求
4. **移除自定义方法**：删除Mapper接口中的自定义方法定义
5. **清理XML文件**：移除对应的XML映射（如果不再使用）


