-- 新增配置表备注字段
ALTER TABLE base_sysconfig ADD COLUMN `F_Remark` varchar(255) DEFAULT NULL COMMENT '备注';

-- 会否开启密码校验 水印等相关的系统配置，放到系统配置表中
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES
('443707792222588997', NULL, 'enableWatermark', '0', 'SysConfig', '是否开启水印  开启1 关闭0');
commit;



-- magicApi建表脚本

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for magic_api_backup
-- ----------------------------
DROP TABLE IF EXISTS `magic_api_backup`;
CREATE TABLE `magic_api_backup` (
  `id` varchar(32) NOT NULL COMMENT '原对象ID',
  `create_date` bigint NOT NULL COMMENT '备份时间',
  `tag` varchar(32) DEFAULT NULL COMMENT '标签',
  `type` varchar(32) DEFAULT NULL COMMENT '类型',
  `name` varchar(64) DEFAULT NULL COMMENT '原名称',
  `content` mediumtext COMMENT '备份内容',
  `create_by` varchar(64) DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`,`create_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ;

-- ----------------------------
-- Table structure for magic_api_file
-- ----------------------------
DROP TABLE IF EXISTS `magic_api_file`;
CREATE TABLE `magic_api_file` (
  `file_path` varchar(512) NOT NULL,
  `file_content` mediumtext,
  PRIMARY KEY (`file_path`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ;

-- 添加magic菜单
INSERT INTO `base_module` (`F_Id`, `F_ParentId`, `F_Type`, `F_FullName`, `F_EnCode`, `F_UrlAddress`, `F_IsButtonAuthorize`, `F_IsColumnAuthorize`, `F_IsDataAuthorize`, `F_PropertyJson`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_LinkTarget`, `F_Category`, `F_Icon`, `F_IsFormAuthorize`, `F_ModuleId`, `F_SystemId`, `F_TenantId`) VALUES ('483915078563792325', '9193163d20604861b13193d24bcb7b0c', '7', 'MagicApi接口开发', 'MagicApi', 'http://localhost:30000/magic/web/index.html?Authorization=${xhToken}', '0', '0', '0', '{\"iconBackgroundColor\":\"\",\"moduleId\":\"\"}', NULL, '34', '1', '2023-10-17 10:45:47', '349057407209541', NULL, NULL, NULL, NULL, NULL, '_blank', 'Web', 'icon-ym icon-ym-highchartsColumn', '0', NULL, '309228585019769285', NULL);



-- ----------------------------
-- Table structure for uni_jingxin_message
-- ----------------------------
DROP TABLE IF EXISTS `uni_jingxin_message`;
CREATE TABLE `uni_jingxin_message` (
  `ID` varchar(36) COLLATE utf8mb4_bin NOT NULL,
  `TYPE` varchar(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '推送类型 1:文件；2：图片；3附件；4视频 ',
  `CONTENT` text COLLATE utf8mb4_bin COMMENT '推送文字内容（type为1时传递的内容）',
  `FILE_NAMES` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件名集合',
  `UPLOAD_TIME` datetime DEFAULT NULL COMMENT '上传时间',
  `COUNT` int(2) DEFAULT NULL COMMENT '文件个数',
  `USER_ID` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL,
  `UPLOAD_MSG` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '上传环信返回的JSON内容',
  `PUSH_MSG` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '推送环信返回的JSON内容',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


-- ----------------------------
-- 统一认证配置信息、警信推送配置信息
-- ----------------------------
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('316967469136740997', NULL, 'uploadFilesUrl', '${ip}/${orgName}/${appName}/chatfiles', 'JINGXIN_CONFIG', '环信上传文件地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('316967469136740998', NULL, 'clientId', 'YXA6wywRpu_xRt6QW3BV1yPJdw', 'JINGXIN_CONFIG', '客户端id');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('316967469136740999', NULL, 'getTokenUrl', '${ip}/${orgName}/${appName}/token', 'JINGXIN_CONFIG', '环信获取token地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('316967469136741000', NULL, 'orgName', 'easemob-szga', 'JINGXIN_CONFIG', '组织名称');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('316967469136741001', NULL, 'pushUrl', '${ip}/${orgName}/${appName}/messages', 'JINGXIN_CONFIG', '环信推送地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('316967469136741002', NULL, 'fromUser', 'f920e7ed-b247-11e7-998c-d00d548b310b', 'JINGXIN_CONFIG', '收信息的服务号ID');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('316967469136741003', NULL, 'appName', 'chat', 'JINGXIN_CONFIG', '应用名称');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('316967469136741004', NULL, 'ip', 'http://***********:80', 'JINGXIN_CONFIG', '警信推送ip地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('316967469136741005', NULL, 'clientSecret', 'YXA6cHkhc9AtGenzEJjrpnYSFqVzzGw', 'JINGXIN_CONFIG', '客户端密钥');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('316967469136741006', NULL, 'grantType', 'client_credentials', 'JINGXIN_CONFIG', '授权类型');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936837', NULL, 'redirectUri', '${serverUrl}/api/oauth/ssoclient/loginback', 'UNI_USER_CONFIG', '当前服务获取code回调地址');
-- 默认密码：Xh12345@ --
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936838', NULL, 'defaultUserPassword', 'e48c44e2c722a2bef9070c25394ae025', 'UNI_USER_CONFIG', '默认密码（未使用密钥加密的密码）');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936839', NULL, 'clientId', 'frameworkservice', 'UNI_USER_CONFIG', '统一认证ClientId');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936840', NULL, 'getUserUrl', '${url}/sso/v1/api/user', 'UNI_USER_CONFIG', '获取用户信息地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936841', NULL, 'logoutUrl', '${url}/sso/revoke/token', 'UNI_USER_CONFIG', '登出地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936842', NULL, 'gettokenUrl', '${url}/sso/oauth/token', 'UNI_USER_CONFIG', '获取token地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936843', NULL, 'clientSecret', 'd2380065e19a63dd33507c5633363c9b', 'UNI_USER_CONFIG', '统一认证Secret');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936844', NULL, 'webLogin', '${url}/sso/oauth/authorize?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}', 'UNI_USER_CONFIG', '网页登录获取code地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936845', NULL, 'getAllDeptUrl', '${url}/users/v2/unifieduser/external/dept/list', 'UNI_USER_CONFIG', '获取全量部门地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936846', NULL, 'getAllUserUrl', '${url}/users/v2/unifieduser/external/user/list', 'UNI_USER_CONFIG', '获取全量用户地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936847', NULL, 'url', 'http://gw.szs.gd', 'UNI_USER_CONFIG', '統一认证地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936848', NULL, 'checkTokenUrl', '${url}/sso/oauth/check_token', 'UNI_USER_CONFIG', '校验token地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936849', NULL, 'serverUrl', 'http://68.64.159.34:30000', 'UNI_USER_CONFIG', '当前服务地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936850', NULL, 'secretKey', '1d6b5c839db64afb8d0a208e447624ed', 'UNI_USER_CONFIG', '密码加密密钥');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936851', NULL, 'roleConfig', 'false', 'UNI_USER_CONFIG', '获取全量用户是否配置默认角色');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936852', NULL, 'defaultRoleId', '', 'UNI_USER_CONFIG', '默认角色id（id集合，用逗号分隔）');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936836', NULL, 'getIncrementUserUrl', '${url}/users/v2/unifieduser/external/user/increment', 'UNI_USER_CONFIG', '获取增量用户地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936835', NULL, 'getIncrementDeptUrl', '${url}/users/v2/unifieduser/external/dept/increment', 'UNI_USER_CONFIG', '获取增量部门地址');
COMMIT;

-- ----------------------------
DROP TABLE IF EXISTS `base_sms_info`;
CREATE TABLE `base_sms_info` (
  `F_ID` varchar(50) NOT NULL,
  `F_PARAMS` varchar(2000) DEFAULT NULL COMMENT '传递参数',
  `F_CREATETIME` datetime DEFAULT NULL COMMENT '发送时间',
  `F_RESULT` varchar(255) DEFAULT NULL COMMENT '返回结果',
  PRIMARY KEY (`F_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='新短信平台发送短信记录表';


INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936853', '', 'spid', '99943', 'SMS_CONFIG', '短信平台申请的spid');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936854', '', 'spSecret', 'bzjfq9ovpn', 'SMS_CONFIG', '密钥');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936855', '', 'spname', '对接短信平台测试系统', 'SMS_CONFIG', '短信平台申请的spname');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936856', '', 'url', 'http://68.78.160.92:30774', 'SMS_CONFIG', '短信平台url');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936857', '', 'registUrl', '${url}/smsservice2/outer/api/session/v1/regist', 'SMS_CONFIG', '注册获取session地址');
INSERT INTO `base_sysconfig` (`F_Id`, `F_Name`, `F_Key`, `F_Value`, `F_Category`, `F_Remark`) VALUES ('317297469408936858', '', 'sendUrl', '${url}/smsservice2/outer/api/sms/v1/send', 'SMS_CONFIG', '短信发送地址');

-- 代码生成 配置信息保存
CREATE TABLE `base_visualdev_setting` (
  `F_Id` varchar(50) NOT NULL COMMENT '主键(和功能表中主键值一样)',
  `F_Module` varchar(50) COMMENT '模块id',
  `F_ClassName` varchar(50) COMMENT '功能类名',
  `F_SubClassName`  varchar(50) COMMENT '子功能类名(多个逗号拼接)',
  `F_Description`  varchar(50) COMMENT '功能描述',
  `F_ModulePackageName`  varchar(50) COMMENT '包名',
  `F_Author`  varchar(50) COMMENT '作者',
  `F_TENANTID`  varchar(100) COMMENT '租户id',
  `F_CreatorTime` datetime DEFAULT NULL COMMENT '创建时间',
  `F_CreatorUserId` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `F_LastModifyTime` datetime DEFAULT NULL COMMENT '修改时间',
  `F_LastModifyUserId` varchar(50) DEFAULT NULL COMMENT '修改用户',
  PRIMARY KEY (`F_Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='代码生成配置信息表';


ALTER TABLE `base_module`
ADD COLUMN `F_EnabledHide`  int NOT NULL DEFAULT 0 COMMENT '是否隐藏 1：隐藏；0：显示，默认显示' AFTER `F_ModuleId`;

update base_module set F_EnabledHide=0 where F_EnabledHide is null;

ALTER TABLE `base_dictionarydata` ADD COLUMN `F_CssClass`  varchar(255) NULL DEFAULT NULL COMMENT ' 样式属性（其他样式扩展）' ;
ALTER TABLE `base_dictionarydata` ADD COLUMN `F_ListClass`  varchar(255) NULL DEFAULT NULL COMMENT '表格回显样式' ;

-- 添加 样式 数据字典类型
INSERT INTO `base_dictionarytype` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_IsTree`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`) VALUES ('1045632246961152', 'afcc3a0952df4d1bad7d83cc8eb20fbd', '数据字典回显样式', 'listClass', '0', '', '20', NULL, '2022-10-20 11:49:02', 'admin', NULL, NULL, NULL, NULL, NULL);
-- 添加 样式 数据字典值
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) VALUES ('1045632460346368', '0', '主要', 'primary', 'ZY', NULL, '', '2', '1', '2022-10-20 11:49:43', 'admin', NULL, NULL, NULL, NULL, NULL, '1045632246961152', NULL, NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) VALUES ('1045632551572480', '0', '成功', 'success', 'CG', NULL, '', '3', '1', '2022-10-20 11:50:01', 'admin', NULL, NULL, NULL, NULL, NULL, '1045632246961152', NULL, NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) VALUES ('1045632624972800', '0', '信息', 'info', 'XX', NULL, '', '4', '1', '2022-10-20 11:50:15', 'admin', NULL, NULL, NULL, NULL, NULL, '1045632246961152', NULL, NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) VALUES ('1045632932729856', '0', '警告', 'warning', 'JG', NULL, '', '5', '1', '2022-10-20 11:51:13', 'admin', NULL, NULL, NULL, NULL, NULL, '1045632246961152', NULL, NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) VALUES ('1045633011373056', '0', '危险', 'danger', 'WX', NULL, '', '6', '1', '2022-10-20 11:51:28', 'admin', NULL, NULL, NULL, NULL, NULL, '1045632246961152', NULL, NULL);
