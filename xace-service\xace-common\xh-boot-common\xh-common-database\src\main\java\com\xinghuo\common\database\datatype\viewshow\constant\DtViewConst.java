package com.xinghuo.common.database.datatype.viewshow.constant;

/**
 * 前端数据类型标准
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class DtViewConst {

    /**
     * 字符串
     */
    public static final String VARCHAR = "varchar";

    /**
     * 日期时间
     */
    public static final String DATE_TIME = "datetime";

    /**
     * 整型
     */
    public static final String INT = "int";

    /**
     * 浮点
     */
    public static final String DECIMAL = "decimal";

    /**
     * 长整型
     */
    public static final String BIGINT = "bigint";

    /**
     * 文本
     */
    public static final String TEXT = "text";

    /**
     * 显示默认
     */
    public static final String DEFAULT = "默认";

    /**
     * Oracle 整型，长征
     */
    public static final String ORACLE_NUMBER = "数字类型";

}
