package com.xinghuo.common.database.model.superQuery;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 高级查询
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
@AllArgsConstructor
public class SuperQueryConditionModel<T> {
	private QueryWrapper<T> obj;
	private List<ConditionJsonModel> conditionList;
	private String matchLogic;
	private String tableName;
}
