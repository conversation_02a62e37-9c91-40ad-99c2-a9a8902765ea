package com.xinghuo.common.database.model.dto;//package com.xinghuo.database.model.dto;
//
//import com.xinghuo.database.sql.model.DbStruct;
//import com.xinghuo.database.util.DataSourceUtil;
//import com.xinghuo.util.StringUtil;
//import io.swagger.v3.oas.annotations.media.Schema;
//
///**
// * 数据源参数传输对象
// * -- 注意：这里的参数dataSourceUtil是spring托管的全局唯一变量，此数据传输对象防止数据源互串
// *
// * <AUTHOR>
// * @date 2023-10-05
// */
//@Data
//public class DataSourceDTO extends DataSourceUtil{
//
//    /**
//     * 数据来源
//     * 0：自身创建  1：配置  2：数据连接
//     */
//    private Integer dataSourceFrom;
//
//    /**
//     * 表名
//     */
//    private String tableName;
//
//
//
//}
