package com.xinghuo.common.database.model.dbtable;

import com.xinghuo.common.database.enums.DbAliasEnum;
import com.xinghuo.common.database.model.dbfield.DbFieldModel;
import com.xinghuo.common.database.model.dbtable.base.DbTableModelBase;
import com.xinghuo.common.database.model.dto.ModelDTO;
import com.xinghuo.common.database.model.interfaces.JdbcGetMod;
import com.xinghuo.common.database.source.AbstractDbBase;
import com.xinghuo.common.util.core.StrXhUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.sql.ResultSet;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
@NoArgsConstructor
@ToString(callSuper=true)
@Accessors(chain = true)
public class DbTableFieldModel extends DbTableModelBase implements JdbcGetMod {

    /**
    * 标识
    */
    @Schema(description = "标识")
    private String id;

    /**
     * 数据源主键
     */
    @Schema(description = "数据源主键")
    private String dbLinkId;

    /**
     * 数据库编码
     */
    @Schema(description = "数据库编码")
    private String dbEncode;

    /**
     * 更新时新表名
     */
    @Schema(description = "更新时新表名")
    private String updateNewTable;

    /**
     * 更新前的旧表名
     */
    @Schema(description = "更新前的旧表名")
    private String updateOldTable;

    /**
     * 字段信息集合
     */
    @Schema(description = "字段信息集合")
    private List<DbFieldModel> dbFieldModelList;

    /**
     * 表是否存在信息
     */
    @Schema(description = "表是否存在信息")
    private Boolean hasTableData;

    /**
     * 类型 0-表 1-视图
     */
    @Schema(description = "类型 0-表 1-视图")
    private Integer type;

    /**
     * 前端注释
     */
    public String getTableName(){
        return getComment();
    }

    public void setTableName(String comment) {
        super.setComment(comment);
    }

    public DbTableFieldModel(String table, String tableComment, List<DbFieldModel> dbFieldModelList){
        this.setTable(table);
        this.setComment(tableComment);
        this.dbFieldModelList = dbFieldModelList;
    }

    @Override
    public void setMod(ModelDTO modelDTO) {
        try {
            String dbEncode = modelDTO.getDbEncode();
            ResultSet resultSet = modelDTO.getResultSet();
            // ============== 表名 ==============
            try {
                String table = resultSet.getString(DbAliasEnum.TABLE_NAME.getAlias(dbEncode));
                this.setTable(table);
            } catch (Exception e) {

            }
            // ============== 表注释 ==============
            try {
                String tableComment = resultSet.getString(DbAliasEnum.TABLE_COMMENT.getAlias(dbEncode));
                this.setComment(tableComment);
            } catch (Exception e) {

            }

            // ============== 表总数 ==============
            this.setSum("0");
            try {
                String sum = resultSet.getString(DbAliasEnum.TABLE_SUM.getAlias(dbEncode));
                if(sum != null) {
                    this.setSum(sum);
                }
            } catch (Exception e) {

            }
            this.setType(0);
            try {
                String tableType = resultSet.getString(DbAliasEnum.TABLE_TYPE.getAlias(dbEncode));
                if (StrXhUtil.isNotEmpty(tableType)) {
                    if (dbEncode.equals(AbstractDbBase.SQL_SERVER) && tableType.equalsIgnoreCase("V ")) {
                        this.setType(1);
                    } else if ((dbEncode.equals(AbstractDbBase.ORACLE) || dbEncode.equals(AbstractDbBase.MYSQL)
                        || dbEncode.equals(AbstractDbBase.DM) || dbEncode.equals(AbstractDbBase.POSTGRE_SQL) || dbEncode.equals(AbstractDbBase.KINGBASE_ES)
                    )
                            && tableType.equalsIgnoreCase("VIEW")) {
                        this.setType(1);
                    }
                }
            } catch (Exception e) {

            }
            // ============== 表大小（由于部分数据库，版本取消了此功能）==============
            /*String size = resultSet.getString(DbAliasEnum.TABLE_SIZE.AS());*/
            this.setDbEncode(dbEncode);
//            this.setSize(size);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
