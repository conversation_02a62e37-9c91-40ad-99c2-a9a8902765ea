package com.xinghuo.common.database.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.DateXhUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * MyBatis Plus 元对象处理器，用于自动填充数据库表中的公共字段值。
 *
 * 在执行数据库操作时，该处理器会在插入和更新操作前自动填充指定的字段值。
 * 这样，我们就不需要在每次插入或更新记录时手动设置这些字段值，提高了代码的复用性和可维护性。
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Component
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler {

    private final String ENABLE_MARK = "enabledMark";
    private final String CREATOR_USER_ID = "creatorUserId";
    private final String CREATOR_TIME = "creatorTime";
    private final String CREATOR_USER = "creatorUser";
    private final String LAST_MODIFY_TIME = "lastModifyTime";
    private final String LAST_MODIFY_USER_ID = "lastModifyUserId";
    private final String LAST_MODIFY_USER = "lastModifyUser";

    //20241026
    private final String CREATED_BY = "createdBy";
    private final String CREATED_AT = "createdAt";
    private final String LAST_UPDATED_BY = "lastUpdatedBy";
    private final String LAST_UPDATED_AT = "lastUpdatedAt";
    private final String DELETED_BY = "deletedBy";
    private final String DELETED_AT = "deletedAt";




    /**
     * 插入操作时自动填充的字段值
     *
     * @param metaObject 元对象
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        // 获取当前登录用户的ID
        String userId = UserProvider.getLoginUserId();
        String userName = UserProvider.getUser().getUserName();

        // 获取各个字段的当前值
        Object enabledMark = this.getFieldValByName(ENABLE_MARK, metaObject);
        Object creatorUserId = this.getFieldValByName(CREATOR_USER_ID, metaObject);
        Object creatorTime = this.getFieldValByName(CREATOR_TIME, metaObject);
        Object creatorUser = this.getFieldValByName(CREATOR_USER, metaObject);

        // 如果字段值为null，则设置默认值
        if (enabledMark == null) {
            this.setFieldValByName(ENABLE_MARK, 1, metaObject);
        }
        if (creatorUserId == null) {
            this.setFieldValByName(CREATOR_USER_ID, userId, metaObject);
        }
        if (creatorTime == null) {
            this.setFieldValByName(CREATOR_TIME, DateXhUtil.date(), metaObject);
        }
        if (creatorUser == null) {
            this.setFieldValByName(CREATOR_USER, userId, metaObject);
        }


        //20241026
        if(metaObject.hasGetter(CREATED_BY)){
            this.setFieldValByName(CREATED_BY, userName, metaObject);
        }
        if (metaObject.hasGetter(CREATED_AT)) {
            this.setFieldValByName(CREATED_AT, new Date(), metaObject);
        }

        //20240120 为了同步程序 LAST_MODIFY_TIME 起作用，同步更新最后更新时间。
        updateFill(metaObject);
    }

    /**
     * 更新操作时自动填充的字段值
     *
     * @param metaObject 元对象
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        // 获取当前登录用户的ID
        String userId = UserProvider.getLoginUserId();
        String userName = UserProvider.getUser().getUserName();


        // 设置更新时间和更新用户字段的值
        this.setFieldValByName(LAST_MODIFY_TIME, new Date(), metaObject);
        this.setFieldValByName(LAST_MODIFY_USER_ID, userId, metaObject);
        this.setFieldValByName(LAST_MODIFY_USER, userId, metaObject);

        //20241026
        if(metaObject.hasGetter(LAST_UPDATED_BY)){
            this.setFieldValByName(LAST_UPDATED_BY, userName, metaObject);
        }
        if (metaObject.hasGetter(LAST_UPDATED_AT)) {
            this.setFieldValByName(LAST_UPDATED_AT, new Date(), metaObject);
        }
    }
}
