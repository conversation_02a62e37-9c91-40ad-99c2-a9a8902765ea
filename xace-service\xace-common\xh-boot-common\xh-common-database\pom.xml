<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xh-boot-common</artifactId>
        <groupId>com.xinghuo.xace</groupId>
        <version>2.1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xh-common-database</artifactId>

    <dependencies>
        <!-- mybatisplus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        </dependency>

        <!-- mysql -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!-- sqlserver -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- oracle -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.nls</groupId>
            <artifactId>orai18n</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- dm -->
        <dependency>
            <groupId>com.dm</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--人大金仓-->
        <dependency>
            <groupId>com.kingbase8</groupId>
            <artifactId>kingbase8-jdbc</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--PostGre-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- druid -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <scope>compile</scope>
        </dependency>
        <!-- Springboot processor-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- redis模块 -->
        <!--        &lt;!&ndash; SpringBoot Admin 客户端  &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>de.codecentric</groupId>-->
        <!--            <artifactId>spring-boot-admin-starter-client</artifactId>-->
        <!--            <version>${spring-boot-admin.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.cloud</groupId>-->
        <!--            <artifactId>spring-cloud-context</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-common-cache</artifactId>
        </dependency>
         <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>


    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>

</project>
