package com.xinghuo.common.hutool;

import com.xinghuo.common.util.core.RandomUtil;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class RandomUtilTest {

    @Test
    void snowIdShouldReturnNonNullValue() {
        String id = RandomUtil.snowId();
        assertNotNull(id);
    }

    @Test
    void enUuIdShouldReturnSixCharacterString() {
        String id = RandomUtil.enUuId();
        assertEquals(6, id.length());
    }

    @Test
    void enUuIdShouldReturnOnlyLowerCaseLetters() {
        String id = RandomUtil.enUuId();
        assertTrue(id.matches("[a-z]+"));
    }

    @Test
    void parsesShouldReturnZero() {
        Long result = RandomUtil.parses();
        assertEquals(0, result);
    }

    @Test
    void getRandomCodeShouldReturnSixDigitNumber() {
        String code = RandomUtil.getRandomCode();
        assertEquals(6, code.length());
        assertTrue(code.matches("\\d+"));
    }
}