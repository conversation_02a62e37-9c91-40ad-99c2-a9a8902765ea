package com.xinghuo.common.base.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 用于表示分页信息的数据模型类
 * 包含当前页码、每页记录数和总记录数
 * 通常用于与前端交互，传递分页信息
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
public class PaginationVO {
    /**
     * 当前页码
     */
    private Integer  currentPage;
    /**
     * 每页记录数
     */
    private Integer  pageSize;
    /**
     * 总记录数
     */
    private Long total;

    @JsonIgnore
    public void setTotalFromInt(Integer total) {
        this.total = total.longValue();
    }
}
