package com.xinghuo.common.database.source.impl;

import com.baomidou.mybatisplus.annotation.DbType;
import com.xinghuo.common.database.constant.DbAliasConst;
import com.xinghuo.common.database.constant.DbConst;
import com.xinghuo.common.database.enums.DbAliasEnum;
import com.xinghuo.common.database.model.dbfield.DbFieldModel;
import com.xinghuo.common.database.source.AbstractDbBase;
import com.xinghuo.common.database.sql.model.DbStruct;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.context.DataSourceContextHolder;

import java.sql.ResultSet;
import java.util.regex.Pattern;

/**
 * PostgreSQL模型
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class DbPostgre extends AbstractDbBase {

    public static String DEF_SCHEMA = "public";

    @Override
    protected void init() {
        setInstance(
                POSTGRE_SQL,
                DbType.POSTGRE_SQL,
                com.alibaba.druid.DbType.postgresql,
                "5432",
                "postgres",
                "postgresql",
                "org.postgresql.Driver",
                "jdbc:postgresql://{host}:{port}/{dbname}");
    }

    @Override
    public String getConnUrl(String prepareUrl, String host, Integer port, DbStruct struct) {
        prepareUrl = super.getConnUrl(prepareUrl, host, port, null);
        return prepareUrl.replace(DbConst.DB_NAME, struct.getPostGreDbName()).replace(DbConst.DB_SCHEMA, struct.getPostGreDbSchema());
    }

    @Override
    protected String getDynamicTableName(String tableName) {
        return DataSourceContextHolder.getDatasourceName().toLowerCase()+"."+tableName;
    }

    @Override
    public void setPartFieldModel(DbFieldModel model, ResultSet result) throws Exception {
        String nullSignStr = result.getString(DbAliasEnum.ALLOW_NULL.getAlias(this.getXhDbEncode()));
        model.setNullSign(DbAliasConst.ALLOW_NULL.getSign(nullSignStr.equals("YES") ? 1 : 0));
        super.setPartFieldModel(model, result);
    }

    private String getCheckSchema(String schema){
        if(StrXhUtil.isEmpty(schema)){
            // 默认public模式
            schema = DEF_SCHEMA;
        }
        return schema;
    }

    /**
     * 表存在大写与小写，导致大小写敏感，需要双引号
     * @param originTable 原始表名
     * @return 表名
     */
    public static String getTable(String originTable){
        if(Pattern.compile("[A-Z]").matcher(originTable).find()){
            return "\"" + originTable + "\"";
        }else {
            return originTable;
        }
    }

}
