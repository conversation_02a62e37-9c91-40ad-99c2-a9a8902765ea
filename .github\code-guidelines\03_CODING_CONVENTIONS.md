# erp-service 基本编码规范

## 命名约定

### 通用命名规则

* **类名:** 大驼峰命名法 (PascalCase)，如 `SysUserService`, `ProductOrderController`
* **接口名:** 大驼峰命名法 (PascalCase)，可考虑以 `I` 开头或 `Service`/`Mapper` 结尾，如 `ISysUserService` 或 `SysUserService` / `SysUserMapper`
* **方法名:** 小驼峰命名法 (camelCase)，如 `getUserById`, `saveProductOrder`
* **变量名:** 小驼峰命名法 (camelCase)，如 `userName`, `orderDetailList`
* **常量名:** 全大写，单词间用下划线分隔 (UPPER_SNAKE_CASE)，如 `MAX_RETRY_COUNT`
* **数据库表/列名:** 全小写，单词间用下划线分隔 (snake_case)，如 `sys_user`, `product_id`

### 严格命名规则

1. **强制:** 代码中任何命名均不能以下划线或美元符号开始以及结束
2. **强制:** 严禁使用拼音与英文混合的方式命名，更不允许直接使用中文命名
3. **强制:** 类名必须使用 UpperCamelCase 风格，严格遵守驼峰形式
4. **强制:** 方法名、参数名、成员变量、局部变量统一使用 lowerCamelCase 风格，必须遵守驼峰形式
5. **强制:** 常量命名全部大写，单词间用下划线隔开，如 `MAX_STOCK_COUNT`
6. **强制:** Model 中布尔类型的变量，不要以 `is` 开头，数据库字段也是如此
7. **强制:** 包名统一使用小写，点分隔符之间有且仅有一个英语单词

### 命名含义规范

* 方法名应该明确表示其功能和意图
* 获取单个对象的方法应使用 `get` 前缀，如 `getUser`
* 获取集合或数组的方法应使用复数名词，如 `getUsers`
* 布尔类型的字段或方法应使用 `is`, `has`, `can` 等前缀，如 `isEnabled`, `hasPermission`

## 常量定义

* **强制:** 不允许任何魔法值(未经定义的常量)直接出现在代码中
* 代码中应避免硬编码，将可能变化的值定义为静态常量
* 相同领域的常量应归类到同一个常量类中

```java
// 正确示例：常量定义
public class OrderConstants {
    // 订单状态
    public static final int STATUS_PENDING = 1;
    public static final int STATUS_PAID = 2;
    public static final int STATUS_SHIPPED = 3;
    public static final int STATUS_COMPLETED = 4;

    // 支付方式
    public static final String PAYMENT_ALIPAY = "alipay";
    public static final String PAYMENT_WECHAT = "wechat";
}

// 使用常量
if (order.getStatus() == OrderConstants.STATUS_PAID) {
    // 处理已支付订单
}
```

## 导入规范

### ⚠️ 重要：Jakarta EE 规范（JDK 17+）

由于项目使用 **JDK 17+ 和 Spring Boot 3.x**，必须使用 **Jakarta EE** 规范的包名：

**✅ 正确的导入：**
```java
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.persistence.Entity;
```

**❌ 错误的导入（不要使用）：**
```java
import javax.validation.constraints.NotNull;  // 错误！
import javax.servlet.http.HttpServletRequest;  // 错误！
import javax.persistence.Entity;               // 错误！
```

### 导入顺序

1. Java 标准库
2. Jakarta EE 库
3. Spring 框架库
4. 第三方库
5. 项目内部包

### 导入规则

* 不使用通配符导入（除非导入超过5个同包类）
* 删除未使用的导入
* 按字母顺序排列
* **强制：** 优先使用 `jakarta.*` 包，避免使用 `javax.*` 包

## 代码格式化

* 使用统一的代码格式化配置，建议使用 IntelliJ IDEA 默认格式
* **强制:** 缩进使用4个空格，禁止使用制表符(Tab)
* **强制:** 一行代码不应过长，单行字符数限制不超过120个
* **强制:** 大括号使用约定：
  * 左大括号前不换行，后换行
  * 右大括号前换行，后根据情况换行
* **强制:** 左右小括号和字符之间不允许有空格
* **强制:** 任何二元、三元运算符的左右两边都需要加空格
* **强制:** 方法参数在定义和传入时，多个参数的逗号后必须加空格
* 方法间应有一个空行分隔
* 相关代码应分组放置在一起
* 避免无意义的空行

```java
// 正确示例：格式化规范
public void someMethod(String param1, int param2, boolean flag) {
    // 二元运算符两侧加空格
    int result = param2 * 2 + 10;

    // 条件判断，括号内侧无空格，操作符两侧有空格
    if (param1.length() > 10 && flag) {
        // 做一些处理
        doSomething();
    } else {
        // 其他处理
        doOthers();
    }

    // 调用方法，逗号后加空格
    callAnotherMethod("value", 42, true);
}
```

## 注释规范

### 类注释

使用标准的 Javadoc 类注释，至少包含以下信息：

```java
/**
 * [类功能简要描述]
 *
 * @author： [你的名字或标识]
 * date： [创建或修改日期 YYYY-MM-DD]
 */
```

### 方法注释

重要的公共方法应添加 Javadoc 注释，说明方法用途：

```java
/**
 * [方法功能简述]
 *
 * @param paramName 参数描述
 * @return 返回值描述
 * @throws ExceptionType 异常情况描述
 */
```

### 行内注释

对复杂逻辑添加必要的行内注释：

```java
// 这是一个行内注释示例，说明下面代码的功能
```

## 常用库使用规范

### Lombok

推荐使用的注解：

* `@Data` - 生成所有字段的getter、setter、toString、equals、hashCode
* `@Getter`/`@Setter` - 只需getter或setter时
* `@Builder` - 构建者模式
* `@RequiredArgsConstructor` - 使用final字段的构造器
* `@Slf4j` - 日志字段

### MybatisPlus

* 实体类注解：
  * `@TableName` - 指定表名
  * `@TableId` - 指定主键及生成规则
  * `@TableField` - 指定字段映射

* 查询构建：
  * 优先使用 `LambdaQueryWrapper` 保证类型安全
  * 复杂查询使用 XML 映射文件

### Spring Boot

* 组件注解：
  * `@RestController` - REST控制器
  * `@Service` - 服务组件
  * `@Mapper` - 数据库访问组件
  * `@Component` - 通用组件

* 配置相关：
  * `@Configuration` - 配置类
  * `@Value` - 注入配置值
  * `@ConfigurationProperties` - 配置属性绑定

## 异常处理

* 使用自定义异常类封装业务异常
* 明确区分系统异常和业务异常
* 不要捕获异常后不处理或仅记录日志
* 避免捕获顶层异常 `Exception`，应捕获具体异常类型

## 其他规范

* 避免编写过长的方法，单个方法建议不超过50行
* 避免过度设计或提前优化
* 保持代码的可测试性
* 遵循单一职责原则
