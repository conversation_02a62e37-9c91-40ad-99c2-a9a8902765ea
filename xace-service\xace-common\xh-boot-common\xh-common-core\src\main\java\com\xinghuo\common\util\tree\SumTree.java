package com.xinghuo.common.util.tree;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 通用树节点类
 *
 * @param <T> 节点数据类型
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SumTree<T> {
    /**
     * 节点 ID
     */
    private String id;

    /**
     * 父节点 ID
     */
    private String parentId;

    /**
     * 是否有子节点
     */
    private Boolean hasChildren;

    /**
     * 子节点列表
     */
    private List<SumTree<T>> children;
}
