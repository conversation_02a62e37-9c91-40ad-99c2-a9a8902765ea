package com.xinghuo.common.util.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.cfg.CoercionAction;
import com.fasterxml.jackson.databind.cfg.CoercionInputShape;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.core.StrXhUtil;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Json工具类   使用Jackson作为JSON实现
 *
 * <AUTHOR>
 */
public class JsonXhUtil {


    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        // 配置 ObjectMapper 忽略未知的属性  如果不配置，登录就会报错。redirectUrl 没有在baseInfo展示。
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.registerModule(new JavaTimeModule());
//        // 创建一个SimpleModule实例
//        SimpleModule module = new SimpleModule();
//        // 将自定义反序列化器添加到module
//        module.addDeserializer(String.class, new JsonObjectToStringDeserializer());
//        // 将module注册到objectMapper
//        objectMapper.registerModule(module);
        //你可以将空字符串强制转换为null2。
        objectMapper.coercionConfigDefaults()
                .setCoercion(CoercionInputShape.EmptyString, CoercionAction.AsNull);
    }


    public static String toJSONString(Object object) {
//        return JSON.toJSONString(object, SerializerFeature.WriteMapNullValue);
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
    }

    public static ObjectNode parseObject(String json) {
        try {
            return (ObjectNode) objectMapper.readTree(json);
        } catch (IOException e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
    }


    /**
     * list转成JSONField
     *
     * @param lists
     * @return
     */
    public static List listToJsonField(List lists) {
//        //空的也显示
//        String jsonStr = JSONArray.toJSONString(lists, SerializerFeature.WriteMapNullValue);
//        //空的不显示
//        List list = JSONArray.parseObject(jsonStr, List.class);
//        return list;
        try {
            // 空的也显示
            String jsonStr = objectMapper.writeValueAsString(lists);
            // 空的不显示
            List list = objectMapper.readValue(jsonStr, List.class);
            return list;
        } catch (IOException e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
    }

    /**
     * 对象转成Map
     *
     * @param object
     * @return
     */
    public static Map<String, Object> entityToMap(Object object) {
//        String jsonStr = JSONObject.toJSONString(object);
//        Map<String, Object> map = JSONObject.parseObject(jsonStr, new TypeReference<Map<String, Object>>() {
//        });
//        return map;
        return objectMapper.convertValue(object, new TypeReference<Map<String, Object>>() {
        });

    }

    public static Map<String, String> entityToMaps(Object object) {
//        String jsonStr = JSONObject.toJSONString(object);
//        Map<String, String> map = JSONObject.parseObject(jsonStr, new TypeReference<Map<String, String>>() {
//        });
//        return map;
        return objectMapper.convertValue(object, new TypeReference<Map<String, String>>() {
        });
    }

    /**
     * String转成Map
     *
     * @param object
     * @return
     */
    public static Map<String, Object> stringToMap(String object) {
        try {
            if(StrXhUtil.isBlank(object)) {
                return null;
            }

            return objectMapper.readValue(object, new TypeReference<Map<String, Object>>() {
            });
        } catch (IOException e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
    }

    public static ObjectNode createObjectNode() {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode node = mapper.createObjectNode();
        return node;
    }

    public static ArrayNode createArrayNode() {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.createArrayNode();
    }


    public static JsonNode parseObject(Object object) {
        try {
            return   objectMapper.valueToTree(object);
        } catch (Exception e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
    }

    /**
     * 功能描述：把JSON数据转换成指定的java对象
     *
     * @param jsonData JSON数据
     * @param clazz    指定的java对象
     * @return 指定的java对象
     */
    public static <T> T toBean(String jsonData, Class<T> clazz) {
        try {
            if(StrXhUtil.startWith(jsonData, "\"")){
                jsonData = objectMapper.readValue(jsonData, String.class);
            }

            return objectMapper.readValue(jsonData, clazz);
            //           return objectMapper.readValue(jsonData, new TypeReference<T>() {});
        } catch (Exception e) {
           e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
//        return JSON.parseObject(jsonData, clazz);
    }

    public static <T> T toBeanEx(String jsonData, Class<T> clazz)  throws Exception{
            return objectMapper.readValue(jsonData, clazz);
    }


    public static ArrayNode getJsonArray(JsonNode jsonNode, String fieldName) {
        JsonNode arrayNode = jsonNode.get(fieldName);
        if (arrayNode != null && arrayNode.isArray()) {
            return (ArrayNode) arrayNode;
        }
        return null;
    }

    public static ObjectNode getObjectNode(JsonNode jsonNode) {
        return objectMapper.convertValue(jsonNode, ObjectNode.class);
    }

    public static <T> T getJsonObjectToBean(ObjectNode objectNode, Class<T> clazz) {
        try {
            return objectMapper.treeToValue(objectNode, clazz);
        } catch (Exception e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
//        return JSON.parseObject(jsonData, clazz);
    }

    /**
     * 功能描述：把JSON数据转换成JSONArray数据
     *
     * @param json
     * @return
     */
//    public static JSONArray getJsonToJsonArray(String json) {
//        return JSONArray.parseArray(json);
//    }
    public static ArrayNode getJsonToJsonArray(String json) {
        try {
            JsonNode jsonNode = objectMapper.readTree(json);
            if (jsonNode.isArray()) {
                return (ArrayNode) jsonNode;
            }
        } catch (IOException e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
        }
        return null;
    }

    /**
     * 功能描述：把List数据转换成JSONArray数据
     *
     * @param list
     * @param <T>
     * @return
     */
//    public static <T> JSONArray getListToJsonArray(List<T> list) {
//        return JSONArray.parseArray(JsonXhUtil.toJSONString(list));
//    }
    public static <T> ArrayNode getListToJsonArray(List<T> list) {
        try {
            String jsonStr = objectMapper.writeValueAsString(list);
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            if (jsonNode.isArray()) {
                return (ArrayNode) jsonNode;
            }
        } catch (IOException e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
        }
        return null;
    }

    /**
     * 功能描述：把java对象转换成JSON数据
     *
     * @param object java对象
     * @return JSON数据
     */
    public static String getObjectToString(Object object) {
//        return JSON.toJSONString(object, SerializerFeature.WriteMapNullValue);
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
    }

    /**
     * 功能描述：把java对象转换成JSON数据
     *
     * @param object java对象
     * @return JSON数据
     */
    public static String getObjectToStringAsDate(Object object) {
//        return JSON.toJSONStringWithDateFormat(object, "yyy-MM-dd HH:mm:ss");
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(SerializationFeature.WRITE_DATE_KEYS_AS_TIMESTAMPS, false);
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
    }

    /**
     * 功能描述：把java对象转换成JSON数据,时间格式化
     *
     * @param object java对象
     * @return JSON数据
     */
    public static String getObjectToStringDateFormat(Object object, String dateFormat) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setDateFormat(new SimpleDateFormat(dateFormat));
            objectMapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, true);
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
    }


    /**
     * 功能描述：把JSON数据转换成指定的java对象
     *
     * @param dto   dto对象
     * @param clazz 指定的java对象
     * @return 指定的java对象
     */
    public static <T> T getJsonToBeanEx(Object dto, Class<T> clazz) {
        if (dto == null) {
            throw new DataException("此条数据不存在");
        }
        String str = JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue);
        return JSON.parseObject(str, clazz);
//        try {
//            String jsonString = objectMapper.writeValueAsString(dto);
//            return objectMapper.readValue(jsonString, clazz);
//        } catch (Exception e) {
//            e.printStackTrace();
//            // 处理异常或者抛出异常
//            return null;
//        }
    }

    /**
     * 功能描述：把JSON数据转换成指定的java对象列表
     * 不能使用 new TypeReference<List<T>>(){}，  TypeReference<List<T>>在运行时无法获取其泛型参数的具体类型，因为Java的类型擦除。
     *
     * @param jsonData JSON数据
     * @param clazz    指定的java对象
     * @return List<T>
     */
    public static <T> List<T> jsonToList(String jsonData, Class<T> clazz) {
//        return JSON.parseArray(jsonData, clazz);
        try {
            if (jsonData.startsWith("\"")) {
                jsonData = objectMapper.readValue(jsonData, String.class);
            }
            // 首先，将带有双引号的JSON字符串解析为一个JSON字符串
            return objectMapper.readValue(jsonData, TypeFactory.defaultInstance().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
    }

    /**
     * 功能描述：把JSON数据转换成较为复杂的List<Map<String, Object>>
     *
     * @param jsonData JSON数据
     * @return List<Map < String, Object>>
     */
    public static List<Map<String, Object>> getJsonToListMap(String jsonData)  throws Exception{
//        return JSON.parseObject(jsonData, new TypeReference<List<Map<String, Object>>>() {
//        });

            return objectMapper.readValue(jsonData, new TypeReference<List<Map<String, Object>>>() {
            });

    }

    /**
     * 功能描述：把JSONArray数据转换成较为复杂的List<Map<String, Object>>
     *
     * @param jsonArray JSONArray数据
     * @return List<Map < String, Object>>
     */
//    public static List<Map<String, Object>> getJsonToList(JSONArray jsonArray) {
//        return JSON.parseObject(JSON.toJSONString(jsonArray), new TypeReference<List<Map<String, Object>>>() {
//        });
//    }
    public static List<Map<String, Object>> jsonToList(ArrayNode jsonArray) {
//        return JSON.parseObject(JSON.toJSONString(jsonArray), new TypeReference<List<Map<String, Object>>>() {
//        });
        return objectMapper.convertValue(jsonArray, new TypeReference<List<Map<String, Object>>>() {
        });
    }

    /**
     * 通过 JSON 的方式进行复制可以处理深层次的复制，
     * 但相对来说会比较耗费性能，
     * 因为需要进行序列化和反序列化操作。
     * 如果性能要求不高且代码简洁性更重要，这种方法是可行的。
     * <p>
     * 功能描述：把JSON数据转换成指定的java对象
     * TODO 是否需要放在静态类中。
     *
     * @param dto   dto对象
     * @param clazz 指定的java对象
     * @return 指定的java对象
     */
    public static <T> T jsonDeepCopy(Object dto, Class<T> clazz) {
        try {
//            ObjectMapper objectMapper = new ObjectMapper();
//            // 设置序列化时包含 null 值的特性
//            objectMapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, true);
//            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

            String jsonStr = objectMapper.writeValueAsString(dto);
            return objectMapper.readValue(jsonStr, clazz);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }


    public static <T> List<T> convertArrayNodeToList(ArrayNode arrayNode, Class<T> valueType) {
        List<T> resultList = new ArrayList<>();
        for (JsonNode node : arrayNode) {
            T object = objectMapper.convertValue(node, valueType);
            resultList.add(object);
        }
        return resultList;
    }

    /**
     * 功能描述：把JSON数据转换成指定的java对象列表
     *
     * @param dto   dto对象
     * @param clazz 指定的java对象
     * @return List<T>
     */
    public static <T> List<T> jsonToList(Object dto, Class<T> clazz) {
        try {
            return objectMapper.readValue(getObjectToString(dto), objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (Exception e) {
            e.printStackTrace();
            // 处理异常或者抛出异常
            return null;
        }
    }
}
