package com.bstek.ureport.console.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bstek.ureport.console.config.DataSourceConfig;
import com.bstek.ureport.console.config.datasource.DataSourceContextHolder;
import com.bstek.ureport.console.ureport.util.JwtUtil;
import com.bstek.ureport.exception.ReportException;
import com.bstek.ureport.utils.RedisUtil;
import com.nimbusds.jwt.JWTClaimsSet;
import com.xinghuo.common.util.json.JsonXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class UpdateData {
    @Autowired
    private DataSourceConfig dataSourceConfig;
    @Autowired
    private RedisUtil redisUtil;

    public boolean getDbName(String token) {
        if (dataSourceConfig.isMultiTenancy()) {
            try {
                String realToken = JwtUtil.getRealToken(token);
                String dbName = null, tenantId = null;
                try {
                    if(StringUtils.hasLength(realToken) && !realToken.equals("null")) {
                        Map<String, Object> uinfoMap = getUserInfo(token);
                        dbName = String.valueOf(uinfoMap.get("tenantDbConnectionString"));
                        tenantId = String.valueOf(uinfoMap.get("tenantId"));
                    }else{
                        JWTClaimsSet userInfo = JwtUtil.getUserInfo(token);
                        Map<String,Object> claims = userInfo.getClaims();
                        dbName = null;
                        if(claims.get("ConnectionConfig") == null && claims.get("TenantDbName") == null){
                            //Net3.4.7最新版
                            String userId = claims.get("UserId").toString();
                            tenantId = claims.get("TenantId").toString();
                            Map<String, Object> uinfoMap = getUserInfoNet(userId, tenantId);
                            if(uinfoMap != null && !uinfoMap.isEmpty()){
                                dbName = getDbInfoNet(tenantId);
                            }
                        }else if(claims.get("TenantDbName") != null){
                            dbName = String.valueOf(claims.get("TenantDbName"));
                        }else{
                            JSONObject connectionConfig = JSONObject.parseObject(claims.get("ConnectionConfig").toString());
                            boolean isCustome = connectionConfig.getBooleanValue("IsCustom");
                            if(isCustome){
                                throw new ReportException("暂不支持指定数据源模式多租户");
                            }
                            JSONArray configList = connectionConfig.getJSONArray("ConfigList");
                            for (int i = 0; i < configList.size(); i++) {
                                JSONObject config = configList.getJSONObject(i);
                                if(config.getBooleanValue("IsMaster")){
                                    dbName = config.getString("ServiceName");
                                }

                            }
                        }
                        tenantId = String.valueOf(claims.get("TenantId"));
                    }
                }catch (Exception e){
                    //e.printStackTrace();
                }
                Assert.notNull(tenantId, "租户ID为空");
                Assert.notNull(dbName, "租户库名为空");
                DataSourceContextHolder.setDatasource(tenantId,dbName);
                return true;
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new RuntimeException("获取多租户数据库失败", e);
            }
        }
        return false;
    }

    //判断是否开启多租户
    public boolean getMultiTenancy(String multiTenancy){
        if (Boolean.valueOf(multiTenancy)) {
            return true;
        }
        return false;
    }

    //获取用户名
    public String getUserName(String token){
        try {
            Map<String, Object> map = getUserInfo(token);
            return String.valueOf(map.get("userName"));
        }catch (Exception e){
            JWTClaimsSet userInfo = JwtUtil.getUserInfo(token);
            return String.valueOf(userInfo.getClaims().get("UserName"));
        }
    }

    //获取Account
    public String getUserId(String token){
        try {
            Map<String, Object> map = getUserInfo(token);
            return String.valueOf(map.get("userId"));
        }catch (Exception e){
            JWTClaimsSet userInfo = JwtUtil.getUserInfo(token);
            return String.valueOf(userInfo.getClaims().get("UserId"));
        }
    }

    public String getRedisToken(String token){
        try {
            JWTClaimsSet map = JwtUtil.getUserInfo(token);
            if(map!=null&&map.getClaim("token") != null){
                Map<String, Object> uinfoMap = getUserInfo(token);
                return String.valueOf(uinfoMap.get("userId"));
            }else if(map!=null&&map.getClaim("UserId") != null){
                String uid = map.getStringClaim("UserId");
                Long exp = map.getDateClaim("exp").getTime();
                if(System.currentTimeMillis() < exp){
                    return uid;
                }
            }
            //统一认证token
            Object userObj = redisUtil.GetString(token);
            if(userObj!=null){
                return String.valueOf(userObj);
            }
        } catch (Exception e) {
            log.error("连接Redis失败", e);
            return null;
        }
        return null;
    }

    public Map<String, Object> getUserInfo(String token){
        String realToken = JwtUtil.getRealToken(token);
        Object uinfoObj = redisUtil.GetString(realToken);
        Map<String, Object> uinfoMap;
        if (uinfoObj == null) {
            uinfoObj = redisUtil.GetString(String.format("Authorization:login:token-session:%s", token.contains(" ")?token.split(" ")[1]:token));
            JSONObject uinfoJs = new JSONObject((Map) uinfoObj);
            uinfoMap = uinfoJs.getJSONObject("dataMap").getJSONObject("userInfo").getInnerMap();
        } else {
            uinfoMap = JsonXhUtil.stringToMap(uinfoObj.toString());
        }
        return uinfoMap;
    }


    public Map<String, Object> getUserInfoNet(String userId, String tenantId){
        String userToken = String.format("%s:xh:permission:user:%s", tenantId, userId);
        Object uinfoObj = redisUtil.GetString(userToken);
        if(uinfoObj == null){
            return Collections.EMPTY_MAP;
        }
        Map<String, Object> uinfoMap = JsonXhUtil.stringToMap(JSONObject.toJSONString(uinfoObj));
        return uinfoMap;
    }

    public String getDbInfoNet(String tenantId){
        String tenantToken = "xh:globaltenant";
        Object uinfoObj = redisUtil.GetString(tenantToken);
        String dbName = null;
        if(uinfoObj == null){
            return dbName;
        }
        JSONArray tenantInfos = JSONArray.parseArray(JSONArray.toJSONString(uinfoObj));
        for (Object tenantInfo : tenantInfos) {
            JSONObject tenantJson = (JSONObject) tenantInfo;
            if(Objects.equals(tenantId, tenantJson.get("TenantId"))){
                JSONObject connectionConfig = tenantJson.getJSONObject("connectionConfig");
                boolean isCustome = connectionConfig.getBooleanValue("IsCustom");
                if(isCustome){
                    throw new ReportException("暂不支持指定数据源模式多租户");
                }
                JSONArray configList = connectionConfig.getJSONArray("ConfigList");
                for (int i = 0; i < configList.size(); i++) {
                    JSONObject config = configList.getJSONObject(i);
                    if(config.getBooleanValue("IsMaster")){
                        return config.getString("ServiceName");
                    }

                }
            }
        }
        return dbName;
    }



}
