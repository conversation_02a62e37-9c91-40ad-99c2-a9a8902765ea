package com.xinghuo.common.base.controller;

import com.xinghuo.common.base.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.ParameterizedType;

/**
 * 控制器基类，提供基础的服务和实体类型支持
 *
 * @param <S> 服务类类型，继承自BaseService
 * @param <Entity> 实体类类型
 * <AUTHOR>
 * @date 2023-10-05
 */
public abstract class BaseController<S extends BaseService<Entity>, Entity> {

    @Autowired
    protected S baseService;

    /**
     * 实体类的Class对象
     */
    Class<Entity> entityClass = null;

    /**
     * 获取实体类的Class对象
     *
     * @return 实体类的Class对象
     */
    public Class<Entity> getEntityClass() {
        // 如果entityClass为null，则通过反射获取泛型参数的实际类型
        if (entityClass == null) {
            this.entityClass = (Class<Entity>) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[1];
        }
        return this.entityClass;
    }

    /**
     * 获取基础服务类
     *
     * @return 基础服务类对象
     */
    public S getBaseService() {
        return baseService;
    }

}
