# 前端代码规范指南

本目录包含了项目前端开发的完整代码规范和最佳实践指南。

## 📚 规范文档目录

### [01. 技术栈规范](./01_TECH_STACK.md)
- Vue 3 + TypeScript 开发规范
- 依赖管理和版本控制
- 开发工具配置

### [02. 项目结构规范](./02_PROJECT_STRUCTURE.md)
- 目录结构组织
- 文件命名规范
- 模块划分原则

### [03. Vue 组件开发规范](./03_VUE_COMPONENT_GUIDELINES.md)
- 组件设计原则
- Props 和 Events 规范
- 生命周期使用指南
- Composition API 最佳实践

### [04. API 响应处理规范](./04_API_RESPONSE_GUIDELINES.md) ⭐
- 后台 ActionResult 结构说明
- 前端响应处理标准
- 分页数据处理规范
- 错误处理最佳实践
- 常见错误及解决方案

### [05. 样式开发规范](./05_STYLE_GUIDELINES.md)
- CSS/LESS 编写规范
- 响应式设计指南
- 主题和变量管理

### [08. 提示词模板](./08_PROMPTS_TEMPLATES.md)
- AI 辅助开发提示词
- 代码生成模板
- 问题排查指南

## 🚨 重要提醒

### API 响应处理注意事项

**后台统一返回格式：**
```typescript
interface ActionResult<T> {
  code: number;     // 状态码，200表示成功
  msg: string;      // 返回信息
  data: T;          // 业务数据
}
```

**常见错误：**
```typescript
// ❌ 错误写法
const businessDetail = await getBusinessInfo(id);
businessInfo.value = businessDetail; // 错误！businessDetail 是 ActionResult 结构

// ✅ 正确写法
const response = await getBusinessInfo(id);
if (response.code === 200) {
  businessInfo.value = response.data; // 正确！使用 response.data
}
```

**分页数据处理：**
```typescript
// ❌ 错误写法
dataSource.value = response.data;

// ✅ 正确写法
if (response.code === 200) {
  dataSource.value = response.data.list;
  pagination.value = response.data.pagination;
}
```

## 📋 开发检查清单

在提交代码前，请确保：

### API 调用
- [ ] 检查 `response.code` 是否为 200
- [ ] 正确访问 `response.data` 获取业务数据
- [ ] 处理错误情况并显示 `response.msg`
- [ ] 分页数据使用 `response.data.list`

### 组件开发
- [ ] 组件名称使用 PascalCase
- [ ] Props 定义了正确的类型
- [ ] 事件使用 kebab-case 命名
- [ ] 使用 Composition API

### 样式规范
- [ ] 使用 scoped 样式
- [ ] 遵循 BEM 命名规范
- [ ] 响应式设计适配

### TypeScript
- [ ] 定义了接口类型
- [ ] 避免使用 any 类型
- [ ] 正确的泛型使用

## 🔧 工具和配置

### 推荐的 VSCode 插件
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- Auto Rename Tag

### 代码格式化配置
项目已配置 ESLint 和 Prettier，请确保在保存时自动格式化代码。

## 📖 学习资源

- [Vue 3 官方文档](https://vuejs.org/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)
- [Ant Design Vue 组件库](https://antdv.com/)
- [Vite 构建工具](https://vitejs.dev/)

## 🤝 贡献指南

1. 遵循现有的代码规范
2. 提交前运行代码检查
3. 编写清晰的提交信息
4. 更新相关文档

## 📞 联系方式

如有疑问或建议，请联系前端团队或在项目中提出 Issue。

---

**记住：规范的代码是团队协作的基础，请严格遵循本规范进行开发。**
