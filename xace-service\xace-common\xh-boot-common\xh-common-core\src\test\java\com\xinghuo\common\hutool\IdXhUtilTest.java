package com.xinghuo.common.hutool;


import com.xinghuo.common.util.core.IdXhUtil;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;


class IdXhUtilTest {

    @Test
    void nextIdStr_returnsValidId() {
        String id = IdXhUtil.nextIdStr();
        assertNotNull(id);
        assertTrue(id.matches("\\d+"));
    }

    @Test
    void nextIdStr_returnsUniqueIds() {
        String id1 = IdXhUtil.nextIdStr();
        String id2 = IdXhUtil.nextIdStr();
        assertNotEquals(id1, id2);
    }

    @Test
    void nextId_returnsValidId() {
        Long id = IdXhUtil.nextId();
        assertNotNull(id);
    }

    @Test
    void nextId_returnsUniqueIds() {
        Long id1 = IdXhUtil.nextId();
        Long id2 = IdXhUtil.nextId();
        assertNotEquals(id1, id2);
    }
}
