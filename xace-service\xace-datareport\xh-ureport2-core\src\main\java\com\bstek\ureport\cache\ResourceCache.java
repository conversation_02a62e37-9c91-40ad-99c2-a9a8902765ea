/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.cache;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> @since 3月17日
 */
public class ResourceCache {
	private static Map<String,Object> map=new HashMap<String,Object>();
	public static void putObject(String key,Object obj){
		if(map.containsKey(key)){
			map.remove(key);
		}
		map.put(key, obj);
	}
	public static Object getObject(String key){
		return map.get(key);
	}
}
