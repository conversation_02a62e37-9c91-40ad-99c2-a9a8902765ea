package com.xinghuo.common.util;

import cn.dev33.satoken.same.SaSameUtil;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.session.TokenSign;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.text.StrPool;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.constant.Constants;
import com.xinghuo.common.constant.DeviceTypeEnum;
import com.xinghuo.common.redis.util.CacheKeyUtil;
import com.xinghuo.common.util.extra.ServletUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.redis.util.RedisUtil;
import com.xinghuo.common.util.context.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.stream.Collectors;

import static com.xinghuo.common.constant.AuthConstant.TOKEN_PREFIX_SP;


/**
 * 用户信息提供者工具类，用于处理用户登录、登出、用户信息缓存等操作。
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Slf4j
@Component
public class UserProvider {

    /**
     * Redis工具类
     */
    private static RedisUtil redisUtil;
    /**
     * 缓存键生成工具类
     */
    private static CacheKeyUtil cacheKeyUtil;

    public static final String USER_INFO_KEY = "userInfo";
    /**
     * 本地线程变量，存储当前线程的用户信息
     */
    private static final ThreadLocal<UserInfo> USER_CACHE = new ThreadLocal<>();

    /**
     * 构造函数，注入依赖的 RedisUtil 和 CacheKeyUtil
     */
    public UserProvider(RedisUtil redisUtil, CacheKeyUtil cacheKeyUtil) {
        UserProvider.redisUtil = redisUtil;
        UserProvider.cacheKeyUtil = cacheKeyUtil;
    }


    // =================== 登录相关操作 ===================

    /**
     * 登录系统
     *
     * @param userInfo 登录用户信息
     */
    public static void login(UserInfo userInfo) {
        setLocalLoginUser(userInfo);
        StpUtil.login(splicingLoginId(userInfo.getUserId()));
        userInfo.setToken(StpUtil.getTokenValueNotCut());
        setLoginUser(userInfo);
    }

    /**
     * 登录系统
     *
     * @param userInfo   用户信息
     * @param loginModel 登录参数
     */
    public static void login(UserInfo userInfo, SaLoginModel loginModel) {
        setLocalLoginUser(userInfo);
        StpUtil.login(splicingLoginId(userInfo.getUserId()), loginModel);
        userInfo.setToken(StpUtil.getTokenValueNotCut());
        setLoginUser(userInfo);
    }


    // =================== 登录用户ID相关操作 ===================

    /**
     * 获取指定TOKEN用户ID
     */
    public static String getLoginUserId(String token) {
        String loginId = (String) StpUtil.getLoginIdByToken(token);
        return parseLoginId(loginId);
    }

    /**
     * 获取当前用户ID, 包含临时切换用户ID
     */
    public static String getLoginUserId() {
        String loginId = getUser().getUserId();
        return parseLoginId(loginId);
    }


    // =================== 用户ID拼接相关操作 ===================


    /**
     * 拼接租户下的用户ID
     */
    public static String splicingLoginId(String userId) {
        return splicingLoginId(userId, null);
    }

    /**
     * 拼接租户下的用户ID
     */
    private static String splicingLoginId(String userId, String tenantId) {
        if (StrXhUtil.isEmpty(tenantId)) {
            tenantId = DataSourceContextHolder.getDatasourceId();
        }
        if (StrXhUtil.isNotEmpty(tenantId)) {
            return tenantId + StrPool.COLON + userId;
        }
        return userId;
    }

    /**
     * 解析租户下的登录ID
     */
    private static String parseLoginId(String loginId) {
        if (loginId != null && loginId.contains(StrPool.COLON)) {
            loginId = loginId.substring(loginId.indexOf(StrPool.COLON) + 1);
        }
        return loginId;
    }

    /**
     * Token是否有效
     */
    public static Boolean isValidToken(String token) {
        UserInfo userInfo = getUser(token);
        return userInfo.getUserId() != null;
    }


    // =================== UserInfo缓存相关操作 ===================


    /**
     * 设置Redis用户数据
     */
    public static void setLoginUser(UserInfo userInfo) {
        StpUtil.getTokenSession().set(USER_INFO_KEY, userInfo);
    }

    /**
     * 设置本地用户数据
     */
    public static void setLocalLoginUser(UserInfo userInfo) {
        USER_CACHE.set(userInfo);
    }

    /**
     * 获取本地用户数据
     */
    public static UserInfo getLocalLoginUser() {
        return USER_CACHE.get();
    }

    /**
     * 清空本地用户数据
     */
    public static void clearLocalUser() {
        USER_CACHE.remove();
    }


    /**
     * 获取用户缓存
     * 保留旧方法
     */
    public UserInfo get(String token) {
        return UserProvider.getUser(token);
    }

    /**
     * 获取用户缓存
     */
    public UserInfo get() {
        return UserProvider.getUser();
    }


    /**
     * 根据用户ID, 租户ID获取随机获取一个UserInfo
     */
    public static UserInfo getUser(String userId, String tenantId) {
        return getUser(userId, tenantId, null, null);
    }

    /**
     * 根据用户ID, 租户ID, 设备类型获取随机获取一个UserInfo
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @param includeDevice 在指定的设备类型中查找。如果提供此参数，只返回这些设备类型中的用户信息。
     * @param excludeDevice 排除指定设备类型。如果提供此参数，不会返回这些设备类型中的用户信息。
     * @return UserInfo 用户信息对象。如果找不到符合条件的用户信息，则返回一个空的UserInfo对象。
     */
    public static UserInfo getUser(String userId, String tenantId, List<String> includeDevice, List<String> excludeDevice) {
        // 通过用户ID和租户ID获取用户的会话信息
        SaSession session = StpUtil.getSessionByLoginId(splicingLoginId(userId, tenantId), false);
        if (session != null) {
            // 获取用户的token签名列表
            List<TokenSign> tokenSignList = session.tokenSignListCopy();
            // 过滤token签名列表，根据includeDevice和excludeDevice参数调整
            if (!tokenSignList.isEmpty()) {
                tokenSignList = tokenSignList.stream().filter(tokenSign -> {
                    // 排除指定设备类型
                    if (!ObjectUtils.isEmpty(excludeDevice)) {
                        if (excludeDevice.contains(tokenSign.getDevice())) {
                            return false;
                        }
                    }
                    // 仅包含指定设备类型
                    if (!ObjectUtils.isEmpty(includeDevice)) {
                        return includeDevice.contains(tokenSign.getDevice());
                    }
                    return true;
                }).toList();
                // 从过滤后的token签名列表中获取第一个签名值，并据此获取UserInfo
                if (!tokenSignList.isEmpty()) {
                    return getUser(tokenSignList.get(0).getValue());
                }
            }
        }
        // 如果找不到符合条件的用户信息，返回一个空的UserInfo对象
        return new UserInfo();
    }


    /**
     * 获取用户缓存信息。该方法用于根据提供的token获取相应的用户信息缓存。如果token有效且存在对应的用户登录信息，
     * 则从缓存中获取用户信息；如果不存在或token无效，则返回一个空的用户信息对象。
     *
     * @param token 用户的token，用于识别和验证用户。可以是任何字符串，null值表示非Web环境下的认证。
     * @return UserInfo 用户信息对象，包含用户的缓存信息。如果找不到相关用户信息，则返回一个空的UserInfo对象。
     */
    public static UserInfo getUser(String token) {
        UserInfo userInfo = null;
        String tokens = null;
        if (token != null) {
            // 如果提供了token，则尝试通过切割token获取真正的认证标识
            tokens = cutToken(token);
        } else {
            try {
                // 在没有提供token的情况下，尝试从当前环境获取token值
                tokens = StpUtil.getTokenValue();
            } catch (Exception e) {
                // 异常处理：获取token失败，此处不做任何操作，即忽略异常
            }
        }
        if (tokens != null) {
            // 如果成功获取了token，尝试根据token获取登录ID和用户信息
            if (StpUtil.getLoginIdByToken(tokens) != null) {
                // 从session中根据token获取用户信息并返回
                userInfo = (UserInfo) StpUtil.getTokenSessionByToken(tokens).get(USER_INFO_KEY);
            }
        }
        // 如果始终没有获取到用户信息，则返回一个新的空的UserInfo对象
        if (userInfo == null) {
            userInfo = new UserInfo();
        }
        return userInfo;
    }


    /**
     * 获取用户缓存
     */
    public static UserInfo getUser() {
//        if(StpUtil.getTokenValue() == null){
//            return  new UserInfo();
//        }
        UserInfo userInfo = USER_CACHE.get();
        if (userInfo != null) {
            return userInfo;
        }
        userInfo = UserProvider.getUser(null);
        if (userInfo.getUserId() != null) {
            USER_CACHE.set(userInfo);
        }
        return userInfo;
    }

    // =================== Token相关操作 ===================

    /**
     * 去除Token前缀
     */
    public static String cutToken(String token) {
        if (token != null && token.startsWith(TOKEN_PREFIX_SP)) {
            token = token.substring(TOKEN_PREFIX_SP.length());
        }
        return token;
    }

    /**
     * 获取token
     */
    public static String getToken() {
        return getAuthorize();
    }


    /**
     * 获取Authorize
     */
    public static String getAuthorize() {
        return ServletUtil.getHeader(Constants.AUTHORIZATION);
    }


    /**
     * TOKEN续期
     */
    public static void renewTimeout() {
        if (StpUtil.getTokenValue() != null) {
            UserInfo userInfo = UserProvider.getUser();
            if (userInfo.getUserId() == null || userInfo.getTokenTimeout() == null) {
                //避免请求过网关之后TOKEN失效(携带TOKEN调用登录接口之后账号被顶替)
                return;
            }
            StpUtil.renewTimeout(userInfo.getTokenTimeout() * 60L);
            SaSession saSession = StpUtil.getSessionByLoginId(splicingLoginId(userInfo.getUserId()), false);
            if (saSession != null) {
                saSession.updateTimeout(userInfo.getTokenTimeout() * 60L);
            }
        }
    }

    /**
     * 获取所有Token记录
     * 包含无效状态的用户、临时用户
     */
    public static List<String> getLoginUserListToken() {
        return StpUtil.searchTokenValue("", 0, -1, true).stream().map(token -> token.replace(StpUtil.stpLogic.splicingKeyTokenValue(""), "")).collect(Collectors.toList());
    }


    // =================== 临时Token相关操作 ===================


    /**
     * 获取内部服务传递验证TOKEN
     */
    public static String getInnerAuthToken() {
        return SaSameUtil.getToken();
    }

    /**
     * 验证内部传递Token是否有效 抛出异常
     */
    public static void checkInnerToken(String token) {
        SaSameUtil.checkToken(token);
    }

    /**
     * 验证内部传递Token是否有效
     */
    public static boolean isValidInnerToken(String token) {
        return SaSameUtil.isValid(token);
    }


    // =================== 退出相关操作 ===================


    /**
     * 根据用户ID踢出全部用户
     */
    public static void kickoutByUserId(String userId, String tenantId) {
        StpUtil.kickout(splicingLoginId(userId, tenantId));
    }

    /**
     * 根据Token踢出指定会话
     */
    public static void kickoutByToken(String... tokens) {
        for (String token : tokens) {
            StpUtil.kickoutByTokenValue(token);
        }
    }

    /**
     * 退出当前Token, 不清除用户其他系统缓存
     */
    public static void logout() {
        StpUtil.logout();

    }

    /**
     * 退出指定Token, 不清除用户其他系统缓存
     */
    public static void logoutByToken(String token) {
        if (token == null) {
            logout();
        } else {
            StpUtil.logoutByTokenValue(cutToken(token));
        }
    }

    /**
     * 退出指定设备类型的用户的全部登录信息, 不清除用户其他系统缓存
     */
    public static void logoutByUserId(String userId, DeviceTypeEnum deviceTypeEnum) {
        StpUtil.logout(splicingLoginId(userId), deviceTypeEnum.getDevice());
    }

    /**
     * 退出指定用户的全部登录信息, 清除相关缓存
     */
    public static void logoutByUserId(String userId) {
        StpUtil.logout(splicingLoginId(userId));
        removeOtherCache(userId);

    }

    // =================== 用户权限 ===================

    /**
     * 获取当前用户拥有的权限列表(菜单编码列表、功能ID列表)
     */
    public static List<String> getPermissionList() {
        return StpUtil.getPermissionList();
    }

    /**
     * 获取当前用户拥有的角色列表
     */
    public static List<String> getRoleList() {
        return StpUtil.getRoleList();
    }


    // =================== 其他缓存相关操作 ===================

    /**
     * 移除
     */
    public static void removeOtherCache(String userId) {
        redisUtil.remove(cacheKeyUtil.getUserAuthorize() + userId);
        redisUtil.remove(cacheKeyUtil.getSystemInfo());
    }

    /**
     * 是否在线
     */
    public boolean isOnLine(String userId) {
        return StpUtil.getTokenValueByLoginId(splicingLoginId(userId), getDeviceForAgent().getDevice()) != null;
    }


    /**
     * 是否登陆
     */
    public static boolean isLogined() {
        return StpUtil.isLogin();
    }

    /**
     * 指定Token是否有效
     */
    public static boolean isValid(String token) {
        return StpUtil.getLoginIdByToken(token) != null;
    }


    public static DeviceTypeEnum getDeviceForAgent() {
        if (ServletUtil.getIsMobileDevice()) {
            return DeviceTypeEnum.APP;
        } else {
            return DeviceTypeEnum.PC;
        }
    }

    /**
     * 判断用户是否是临时用户
     *
     * @param userInfo 用户信息
     * @return 是否是临时用户
     */
    public static boolean isTempUser(UserInfo userInfo) {
        if (userInfo == null) {
            userInfo = getUser();
        }
        return DeviceTypeEnum.TEMPUSER.getDevice().equals(userInfo.getLoginDevice())
                || DeviceTypeEnum.TEMPUSERLIMITED.getDevice().equals(userInfo.getLoginDevice());
    }


}
