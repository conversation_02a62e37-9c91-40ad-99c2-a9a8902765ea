package com.xinghuo.common.redis.util;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 缓存工具类，提供对缓存的增删改查操作
 */
@Component
@Slf4j
public class CacheXhUtil {

    @Autowired
    private CacheManager cacheManager; // 自动注入缓存管理器

    private Cache<String, Object> xaceCache; // 定义一个缓存对象

    /**
     * 初始化方法，创建一个二级缓存
     */
    @PostConstruct
    public void init() {
        // 配置缓存参数，包括过期时间和缓存类型等
        QuickConfig qc = QuickConfig.newBuilder("xaceCache")
                .expire(Duration.ofSeconds(100))
                .cacheType(CacheType.BOTH)
                .syncLocal(true)
                .build();
        // 创建缓存
        xaceCache = cacheManager.getOrCreateCache(qc);
    }

    /**
     * 从缓存中获取对象
     *
     * @param key 缓存键
     * @return 缓存键对应的价值对象，如果不存在则返回null
     */
    public Object get(String key) {
        log.info("get cache key: {}", key);
        return xaceCache.get(key);
    }

    public boolean exists(String key) {
        boolean exist = xaceCache.exists(key);
        log.info("exists cache key: {}", exist);
        return exist;
    }



    /**
     * 将键值对存入缓存
     *
     * @param key 缓存键
     * @param value 缓存值
     */
    public void put(String key, Object value) {
        log.info("put cache key: {}, value: {}", key, value);
        xaceCache.put(key, value);
    }

    /**
     * 将键值对存入缓存，并设置过期时间
     *
     * @param key 缓存键
     * @param value 缓存值
     * @param seconds 缓存的过期时间（秒）
     */
    public void put(String key, Object value,long seconds) {
        log.info("put cache key: {}, value: {}", key, value);
        xaceCache.put(key, value,seconds, TimeUnit.SECONDS);
    }

    /**
     * 从缓存中移除指定键的对象
     *
     * @param key 缓存键
     */
    public void remove(String key) {
        log.info("remove cache key: {}", key);
        xaceCache.remove(key);
    }

}
