# Controller 层规范

## 基本结构

Controller层负责处理HTTP请求，将请求委托给服务层处理，并将结果返回给客户端。

### 位置与命名

* **包路径:** `com.xinghuo.[模块名].controller`
* **命名规范:** 以 `Controller` 结尾，如 `UserController`，`AllegroOfferController`

### 基础注解

* `@Slf4j` - Lombok提供的日志注解
* `@RestController` - 标识为REST控制器
* `@RequestMapping("/api/xxx")` - 指定路由前缀
* `@Tag(name = "xxx", description = "xxx")` - Swagger文档标签

### 资源注入

* 使用 `@Resource` 注入依赖服务，每个依赖一行
* 按照功能逻辑排列依赖服务

## 完整示例

```java
/**
 * 在线产品列表
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "在线产品列表", description = "在线产品列表")
@RequestMapping("/api/allegro/offer")
public class AllegroOfferController {

    @Resource
    private AllegroOfferService allegroOfferService;

    @Resource
    private AllegroStoreService allegroStoreService;

    @Operation(summary = "在线产品查询统计数字")
    @PostMapping("/sumList")
    public ActionResult<List<AllegroOfferSumModel>> sumList(@RequestBody AllegroOfferPagination pagination) {
        List<AllegroOfferSumModel> list = allegroOfferService.getSumList(pagination);
        return ActionResult.success(list);
    }

    @Operation(summary = "在线产品查询列表")
    @PostMapping("/getList")
    public ActionResult<PageListVO<AllegroOfferModel>> list(@RequestBody AllegroOfferPagination pagination) {
        List<AllegroOfferEntity> list = allegroOfferService.getList(pagination);
        List<AllegroOfferModel> listVO = BeanCopierUtils.copyList(list, AllegroOfferModel.class);
        // 对结果进行数据转换和补充
        for(AllegroOfferModel vo : listVO){
            if(vo.getStatus().equals("ACTIVE")){
                vo.setStatusName("在售");
            }
            // ...其他状态转换...
            vo.setSellerName(allegroStoreService.getInfoBySellerId(vo.getSellerId()).getStoreName());
        }
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    @Operation(summary = "批量上架--被批量下架的店铺，触发批量上架指令")
    @PostMapping("/storeActivate")
    public ActionResult<String> storeActivate(@RequestBody @Valid OfferActivateModel model) {
        int size = allegroOfferService.insertActivateBatch(model.getSellerId(), model.getEndDate());
        return ActionResult.success("批量上架发送请求成功，共" + size + "条，等待后台推送。");
    }
}
```

## 设计原则

### RESTful API设计

* 使用HTTP方法表示动作
  * `GET` - 查询资源
  * `POST` - 创建资源或复杂查询
  * `PUT` - 更新资源
  * `DELETE` - 删除资源

* 使用URL表示资源
  * API前缀统一使用 `/api/[模块]/[资源]`
  * 资源命名使用名词，如 `/api/allegro/offer`
  * 通过URL参数或路径传递资源标识，如 `/{id}`

### 参数传递与校验

* 查询类接口
  * 简单查询使用 `@GetMapping` 和请求参数方式传递
  * 复杂查询使用 `@PostMapping` 和 `@RequestBody`，如 `@PostMapping("/getList")`

* 分页查询参数
  * 使用专门的分页对象，如 `AllegroOfferPagination`
  * 包含排序、过滤和分页信息

* 创建/更新操作
  * 使用 `@RequestBody @Valid` 注解接收并校验参数
  * 参数对象命名规范：创建用`xxxForm`，更新用`xxxForm`，查询用`xxxPagination`

* 集合类参数校验
  * 使用 `@NotEmpty` 校验集合非空
  * 批量操作使用`@RequestBody List<String> idList`形式

### 返回值封装

* 统一使用 `ActionResult<T>` 封装返回结果
* 成功返回方式：
  * 普通数据：`ActionResult.success(data)`
  * 分页数据：`ActionResult.page(listVO, page)`
  * 带消息的成功：`ActionResult.success("操作成功信息")`
* 失败返回：`ActionResult.fail("错误信息")`
* 使用预定义的消息码：`ActionResult.success(MsgCode.SU004.get())`

### 接口文档

* 使用 `@Operation(summary = "xxx")` 描述接口功能，简洁明了
* 类上使用 `@Tag(name = "xxx", description = "xxx")` 对API分组
* 复杂参数使用 `@Schema` 注解详细说明

### 数据转换和业务处理

* 使用 `BeanCopierUtils.copyList()` 或 `BeanCopierUtils.copy()` 进行对象转换
* 在Controller中进行必要的数据转换和补充：
  * 状态码转换为状态名称
  * 关联其他实体的名称
  * 计算派生属性

## 请求处理流程

1. 接收并验证请求参数
2. 调用Service层处理业务逻辑
3. 转换Service返回的实体为前端所需的VO/DTO对象
4. 补充关联数据和派生属性
5. 封装为统一的ActionResult并返回

## 批量操作设计

* 批量操作通常使用POST或PUT方法
* 批量ID操作使用List接收：
  ```java
  @PutMapping("/buckPrepareLink")
  public ActionResult buckPrepareLink(@RequestBody @NotEmpty List<String> idList) {
      allegroOfferService.buckPrepareLink(idList);
      return ActionResult.success("处理成功！");
  }
  ```

* 复杂批量操作使用专门的Model对象：
  ```java
  @PutMapping("/buckFeeUpdate")
  @Operation(summary = "店铺-批量费用设置")
  public ActionResult buckFeeUpdate(@RequestBody @Valid BuckFeeForm form)
  ```

## 最佳实践

1. Controller保持轻量，主要负责：
   - 参数接收与校验
   - 调用Service层方法
   - 数据转换与组装
   - 结果封装与返回

2. 方法命名遵循动词+名词模式，体现业务含义：
   - `list` / `getList` - 获取列表
   - `sumList` - 获取汇总数据
   - `storeActivate` - 店铺激活
   - `buckPrepareLink` - 批量准备链接

3. 对于复杂数据处理，使用清晰的循环或流式处理，避免过度嵌套

4. 返回友好的错误信息和操作结果提示，如"批量上架发送请求成功，共X条"

5. 在参数验证失败时，返回具体的错误信息，帮助前端定位问题

6. 同一资源的不同视图可以在同一Controller中提供多个接口，如管理视图、用户视图等

## 分页查询接口规范

### ⚠️ 重要：分页查询接口统一规范

**所有分页查询接口必须遵循以下规范：**

**✅ 正确的分页查询接口：**

```java
/**
 * 获取XXX列表
 *
 * @param pagination 分页查询参数
 * @return XXX列表
 */
@PostMapping("/getList")
@Operation(summary = "获取XXX列表")
public ActionResult list(@RequestBody XxxPagination pagination) {
    List<XxxEntity> list = xxxService.getList(pagination);
    List<XxxVO> listVOs = BeanCopierUtils.copyList(list, XxxVO.class);
    PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
    return ActionResult.page(listVOs, page);
}
```

**❌ 错误的分页查询接口：**

```java
// 错误1：使用GET方法
@GetMapping
@Operation(summary = "获取XXX列表")
public ActionResult list(XxxPagination pagination) { ... }

// 错误2：缺少@RequestBody注解
@PostMapping("/getList")
@Operation(summary = "获取XXX列表")
public ActionResult list(XxxPagination pagination) { ... }

// 错误3：路径不规范
@PostMapping("/query")
@Operation(summary = "获取XXX列表")
public ActionResult list(@RequestBody XxxPagination pagination) { ... }
```

### 规范要点

1. **HTTP方法**：统一使用 `@PostMapping("/getList")`
2. **参数注解**：必须使用 `@RequestBody` 注解
3. **方法名称**：建议使用 `list` 作为方法名
4. **返回格式**：使用 `ActionResult.page(listVOs, page)` 返回分页结果
5. **文档注解**：使用 `@Operation` 提供接口说明

### 原因说明

- **POST方法**：便于传递复杂查询条件，避免URL长度限制
- **@RequestBody**：支持复杂对象传递，便于前端统一处理
- **统一路径**：便于前端API调用的一致性
- **统一返回**：保证分页数据格式的一致性

