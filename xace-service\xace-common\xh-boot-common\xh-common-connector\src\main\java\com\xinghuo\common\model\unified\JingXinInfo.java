package com.xinghuo.common.model.unified;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 警信推送配置Model
 *
 * <AUTHOR>
 * @date 2022-07-13
 */
@Data
public class JingXinInfo {

    @NotBlank(message = "必填")
    @Schema(description = "警信推送ip地址")
    private String ip;
    @NotBlank(message = "必填")
    @Schema(description = "组织名称")
    private String orgName;
    @NotBlank(message = "必填")
    @Schema(description = "应用名称")
    private String appName;
    @NotBlank(message = "必填")
    @Schema(description = "客户端id")
    private String clientId;
    @NotBlank(message = "必填")
    @Schema(description = "客户端密钥")
    private String clientSecret;
    @NotBlank(message = "必填")
    @Schema(description = "授权类型")
    private String grantType;
    @NotBlank(message = "必填")
    @Schema(description = "收信息的服务号ID")
    private String fromUser;
    @NotBlank(message = "必填")
    @Schema(description = "环信获取token地址")
    private String getTokenUrl;
    @NotBlank(message = "必填")
    @Schema(description = "环信上传文件地址")
    private String uploadFilesUrl;
    @NotBlank(message = "必填")
    @Schema(description = "环信推送地址")
    private String pushUrl;
}
