package com.xinghuo.common.hutool;

class JsonXhUtilTest {
//
//    @Test
//    void toJSONStringShouldReturnNonNullValue() {
//        String jsonString = JsonXhUtil.toJSONString(new Object());
//        assertNotNull(jsonString);
//    }
//
//    @Test
//    void parseObjectShouldReturnNonNullValue() {
//        ObjectNode objectNode = JsonXhUtil.parseObject("{}");
//        assertNotNull(objectNode);
//    }
//
//    @Test
//    void listToJsonFieldShouldReturnNonNullValue() {
//        List<Object> list = JsonXhUtil.listToJsonField(Arrays.asList(new Object()));
//        assertNotNull(list);
//    }
//
//    @Test
//    void entityToMapShouldReturnNonNullValue() {
//        Map<String, Object> map = JsonXhUtil.entityToMap(new Object());
//        assertNotNull(map);
//    }
//
//    @Test
//    void entityToMapsShouldReturnNonNullValue() {
//        Map<String, String> map = JsonXhUtil.entityToMaps(new Object());
//        assertNotNull(map);
//    }
//
//    @Test
//    void stringToMapShouldReturnNonNullValue() {
//        Map<String, Object> map = JsonXhUtil.stringToMap("{}");
//        assertNotNull(map);
//    }
//
//    @Test
//    void createObjectNodeShouldReturnNonNullValue() {
//        ObjectNode objectNode = JsonXhUtil.createObjectNode();
//        assertNotNull(objectNode);
//    }
//
//    @Test
//    void parseObjectFromObjectShouldReturnNonNullValue() {
//        ObjectNode objectNode = JsonXhUtil.parseObject(new Object());
//        assertNotNull(objectNode);
//    }
//
//    @Test
//    void toBeanShouldReturnNonNullValue() {
//        Object object = JsonXhUtil.toBean("{}", Object.class);
//        assertNotNull(object);
//    }
//
//    @Test
//    void getJsonToJsonArrayShouldReturnNonNullValue() {
//        ArrayNode arrayNode = JsonXhUtil.getJsonToJsonArray("[]");
//        assertNotNull(arrayNode);
//    }
//
//    @Test
//    void getListToJsonArrayShouldReturnNonNullValue() {
//        ArrayNode arrayNode = JsonXhUtil.getListToJsonArray(Arrays.asList(new Object()));
//        assertNotNull(arrayNode);
//    }
//
//    @Test
//    void getObjectToStringShouldReturnNonNullValue() {
//        String jsonString = JsonXhUtil.getObjectToString(new Object());
//        assertNotNull(jsonString);
//    }
//
//    @Test
//    void getObjectToStringAsDateShouldReturnNonNullValue() {
//        String jsonString = JsonXhUtil.getObjectToStringAsDate(new Object());
//        assertNotNull(jsonString);
//    }
//
//    @Test
//    void getObjectToStringDateFormatShouldReturnNonNullValue() {
//        String jsonString = JsonXhUtil.getObjectToStringDateFormat(new Object(), "yyyy-MM-dd");
//        assertNotNull(jsonString);
//    }
//
//    @Test
//    void getJsonToBeanExShouldReturnNonNullValue() {
//        Object object = JsonXhUtil.getJsonToBeanEx("{}", Object.class);
//        assertNotNull(object);
//    }
//
//    @Test
//    void jsonToListShouldReturnNonNullValue() {
//        List<Object> list = JsonXhUtil.jsonToList("[]", Object.class);
//        assertNotNull(list);
//    }
//
//    @Test
//    void getJsonToListMapShouldReturnNonNullValue() {
//        List<Map<String, Object>> list = JsonXhUtil.getJsonToListMap("[]");
//        assertNotNull(list);
//    }
//
//    @Test
//    void jsonToListFromJsonArrayShouldReturnNonNullValue() {
//        List<Map<String, Object>> list = JsonXhUtil.jsonToList(JsonXhUtil.getJsonToJsonArray("[]"));
//        assertNotNull(list);
//    }
//
//    @Test
//    void jsonDeepCopyShouldReturnNonNullValue() {
//        Object object = JsonXhUtil.jsonDeepCopy(new Object(), Object.class);
//        assertNotNull(object);
//    }
//
//    @Test
//    void convertArrayNodeToListShouldReturnNonNullValue() {
//        List<Object> list = JsonXhUtil.convertArrayNodeToList(JsonXhUtil.getJsonToJsonArray("[]"), Object.class);
//        assertNotNull(list);
//    }
//
//    @Test
//    void jsonToListFromObjectShouldReturnNonNullValue() {
//        List<Object> list = JsonXhUtil.jsonToList(new Object(), Object.class);
//        assertNotNull(list);
//    }
}