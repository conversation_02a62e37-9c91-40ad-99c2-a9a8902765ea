package com.xinghuo.checkscore.model.config;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;

/**
 * zz_jx_check_user_config
 *
 * @版本： V2.0
 * @版权：
 * @作者： XACE开发平台组
 * @日期： 2024-01-28
 */
@Data
public class CheckUserConfigPagination extends Pagination {
    /**
     * 查询key
     */
    private String[] selectKey;
    /**
     * json
     */
    private String json;
    /**
     * 数据类型 0-当前页，1-全部数据
     */
    private String dataType;
    /**
     * 高级查询
     */
    private String superQueryJson;
    /**
     * 功能id
     */
    private String moduleId;
    /**
     * 菜单id
     */
    private String menuId;
    /**
     * 员工姓名
     */
    private String userName;
    /**
     * 分部
     */
    private String fbId;
    /**
     * 员工id
     */
    private String userId;
}