package com.xinghuo.common.constant;

/**
 * 常用提示信息
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public interface MsgCode {

    /**
     * 执行成功：SU（success）
     */
    com.xinghuo.common.constant.model.MsgCode SU000 = MSG("Success");
    com.xinghuo.common.constant.model.MsgCode SU001 = MSG("新建成功");
    com.xinghuo.common.constant.model.MsgCode SU002 = MSG("保存成功");
    com.xinghuo.common.constant.model.MsgCode SU003 = MSG("删除成功");
    com.xinghuo.common.constant.model.MsgCode SU004 = MSG("更新成功");
    com.xinghuo.common.constant.model.MsgCode SU005 = MSG("操作成功");
    com.xinghuo.common.constant.model.MsgCode SU006 = MSG("提交成功，请耐心等待");
    com.xinghuo.common.constant.model.MsgCode SU007 = MSG("复制成功");
    com.xinghuo.common.constant.model.MsgCode SU008 = MSG("停止成功");
    com.xinghuo.common.constant.model.MsgCode SU009 = MSG("终止成功");
    com.xinghuo.common.constant.model.MsgCode SU010 = MSG("还原成功");
    com.xinghuo.common.constant.model.MsgCode SU011 = MSG("发布成功");
    com.xinghuo.common.constant.model.MsgCode SU012 = MSG("发送成功");
    com.xinghuo.common.constant.model.MsgCode SU013 = MSG("接口修改成功");
    com.xinghuo.common.constant.model.MsgCode SU014 = MSG("更新接口状态成功");
    com.xinghuo.common.constant.model.MsgCode SU015 = MSG("上传成功");
    com.xinghuo.common.constant.model.MsgCode SU016 = MSG("设置成功");
    com.xinghuo.common.constant.model.MsgCode SU017 = MSG("验证成功");
    com.xinghuo.common.constant.model.MsgCode SU018 = MSG("添加成功");

    /**
     * 执行失败：FA（fail）
     */
    com.xinghuo.common.constant.model.MsgCode FA001 = MSG("此条数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA002 = MSG("更新失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA003 = MSG("删除失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA004 = MSG("复制失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA005 = MSG("发送失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA006 = MSG("下载失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA007 = MSG("操作失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA008 = MSG("停止失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA009 = MSG("终止失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA010 = MSG("还原失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA011 = MSG("发布失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA012 = MSG("获取失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA013 = MSG("接口修改失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA014 = MSG("更新接口状态失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA015 = MSG("预览失败，数据不存在");
    com.xinghuo.common.constant.model.MsgCode FA016 = MSG("删除失败，该文件夹存在数据");
    com.xinghuo.common.constant.model.MsgCode FA017 = MSG("上传失败，文件格式不允许上传");
    com.xinghuo.common.constant.model.MsgCode FA018 = MSG("文件不存在");
    com.xinghuo.common.constant.model.MsgCode FA019 = MSG("已失效");
    com.xinghuo.common.constant.model.MsgCode FA020 = MSG("未查到信息");
    com.xinghuo.common.constant.model.MsgCode FA021 = MSG("操作失败！您没有权限操作");
    com.xinghuo.common.constant.model.MsgCode FA022 = MSG("更新失败！您没有权限操作 (角色只有超级管理员才能够操作)");
    com.xinghuo.common.constant.model.MsgCode FA023 = MSG("更新失败！已绑定用户，无法切换组织");
    com.xinghuo.common.constant.model.MsgCode FA024 = MSG("删除失败！已绑定用户");
    com.xinghuo.common.constant.model.MsgCode FA025 = MSG("该组织内无角色或角色权限为空，组织切换失败");
    com.xinghuo.common.constant.model.MsgCode FA026 = MSG("更新失败，关联组织不存在，请重新登录，或者刷新页面");
    com.xinghuo.common.constant.model.MsgCode FA027 = MSG("该系统下菜单为空，系统切换失败");
    com.xinghuo.common.constant.model.MsgCode FA035 = MSG("未获取到租户指定数据源信息");

    /*======1 短语======*/
    com.xinghuo.common.constant.model.MsgCode FA101 = MSG("保存失败");
    com.xinghuo.common.constant.model.MsgCode FA102 = MSG("更新失败");


    /**
     * 重名判断
     */
    com.xinghuo.common.constant.model.MsgCode EXIST001 = MSG("名称不能重复");
    com.xinghuo.common.constant.model.MsgCode EXIST002 = MSG("编码不能重复");
    com.xinghuo.common.constant.model.MsgCode EXIST003 = MSG("模板名已存在");
    com.xinghuo.common.constant.model.MsgCode EXIST004 = MSG("文件夹名称不能重复");
    com.xinghuo.common.constant.model.MsgCode EXIST005 = MSG("模板名称超过了限制长度");
    com.xinghuo.common.constant.model.MsgCode EXIST101 = MSG("名称重复，请重新输入");
    com.xinghuo.common.constant.model.MsgCode EXIST102 = MSG("编码重复，请重新输入");


    /**
     * 导入导出：IMP（import/export）
     */
    com.xinghuo.common.constant.model.MsgCode IMP001 = MSG("导入成功");
    com.xinghuo.common.constant.model.MsgCode IMP002 = MSG("导入失败，文件格式错误");
    com.xinghuo.common.constant.model.MsgCode IMP003 = MSG("导入失败，数据已存在");
    com.xinghuo.common.constant.model.MsgCode IMP004 = MSG("导入失败，数据有误");

    /**
     * 其他
     */
    // 打印模板 (print)
    com.xinghuo.common.constant.model.MsgCode PRI001 = MSG("打印模板不存在");
    com.xinghuo.common.constant.model.MsgCode PRI002 = MSG("数字字典不存在printDev的字典分类");
    com.xinghuo.common.constant.model.MsgCode PRI003 = MSG("第1条SQL语句：查询出多条表头信息");
    com.xinghuo.common.constant.model.MsgCode PRI004 = MSG("第1条SQL语句：未查出表头信息");
    com.xinghuo.common.constant.model.MsgCode PRI005 = MSG("第{index}条SQL语句：");
    com.xinghuo.common.constant.model.MsgCode PRI006 = MSG("已到达该模板复制上限，请复制源模板");
    com.xinghuo.common.constant.model.MsgCode COD001 = MSG("集合条件过滤获得目标为空");

    /**
     * 复制
     */
    com.xinghuo.common.constant.model.MsgCode COPY001 = MSG("复制名称长度超过了限制长度");


    /**
     * 登录相关
     */
    /*=====0-账号相关====*/
    com.xinghuo.common.constant.model.MsgCode LOG001 = MSG("账户异常");
    com.xinghuo.common.constant.model.MsgCode LOG002 = MSG("注销成功");
    com.xinghuo.common.constant.model.MsgCode LOG003 = MSG("无效的账号");
    com.xinghuo.common.constant.model.MsgCode LOG004 = MSG("账号异常，请联系管理员修改所属组织信息");
    com.xinghuo.common.constant.model.MsgCode LOG005 = MSG("账号未被激活");
    com.xinghuo.common.constant.model.MsgCode LOG006 = MSG("账号被禁用");
    com.xinghuo.common.constant.model.MsgCode LOG007 = MSG("账号已被删除");
    com.xinghuo.common.constant.model.MsgCode LOG010 = MSG("此IP未在白名单中，请联系管理员");
    com.xinghuo.common.constant.model.MsgCode LOG011 = MSG("登录失败，用户暂未绑定角色");
    com.xinghuo.common.constant.model.MsgCode LOG012 = MSG("请联系管理员解除账号锁定！");
    com.xinghuo.common.constant.model.MsgCode LOG013 = MSG("请等待{time}分钟后再进行登录，或联系管理员解除账号锁定！");

    /*======1-登录相关======*/
    com.xinghuo.common.constant.model.MsgCode LOG101 = LOG("账户或密码错误，请重新输入。");
    com.xinghuo.common.constant.model.MsgCode LOG102 = LOG("账号有误，请重新输入。");
    com.xinghuo.common.constant.model.MsgCode LOG103 = LOG("请输入验证码");
    com.xinghuo.common.constant.model.MsgCode LOG104 = LOG("验证码错误");
    com.xinghuo.common.constant.model.MsgCode LOG105 = LOG("连接租户服务失败，请稍后再试");
    com.xinghuo.common.constant.model.MsgCode LOG106 = LOG("短信验证码错误");
    com.xinghuo.common.constant.model.MsgCode LOG108 = LOG("请等待{0}分钟后再进行登录，或联系管理员解除锁定");
    com.xinghuo.common.constant.model.MsgCode LOG109 = LOG("租户登录失败，请用手机验证码登录");
    com.xinghuo.common.constant.model.MsgCode LOG110 = LOG("数据库异常，请联系管理员处理");
    com.xinghuo.common.constant.model.MsgCode LOG111 = LOG("已开启单点登录, 不支持此登录方式");
    com.xinghuo.common.constant.model.MsgCode LOG112 = LOG("不支持此登录方式");
    com.xinghuo.common.constant.model.MsgCode LOG113 = LOG("未设置租户信息");
    com.xinghuo.common.constant.model.MsgCode LOG114 = LOG("租户编码不允许为空");
    com.xinghuo.common.constant.model.MsgCode LOG115 = LOG("租户信息获取失败");


    com.xinghuo.common.constant.model.MsgCode LOG116 = LOG("不支持此验证");
    com.xinghuo.common.constant.model.MsgCode LOG117 = LOG("连短信验证码验证失败：{0}");


    /*======2-密码修改========*/
    com.xinghuo.common.constant.model.MsgCode LOG201 = LOG("旧密码错误");
    com.xinghuo.common.constant.model.MsgCode LOG202 = LOG("修改成功，请牢记新密码。");
    com.xinghuo.common.constant.model.MsgCode LOG203 = LOG("修改失败，账号不存在。");
    com.xinghuo.common.constant.model.MsgCode LOG204 = LOG("修改失败，新建密码不能与旧密码一样。");

    /**
     * 数据库
     */
    com.xinghuo.common.constant.model.MsgCode DB001 = DB("数据类型编码不符合标准（请注意大小写）。MySQL , SQLServer , Oracle , DM , KingbaseES , PostgreSQL");
    com.xinghuo.common.constant.model.MsgCode DB002 = DB("请检查 1、连接信息 2、网络通信 3、数据库服务启动状态。 详情：");
    com.xinghuo.common.constant.model.MsgCode DB003 = DB("通过url找不到对应数据库");
    com.xinghuo.common.constant.model.MsgCode DB004 = DB("查询结果集为空。");
    com.xinghuo.common.constant.model.MsgCode DB005 = DB("未找到对应数据库类型");
    com.xinghuo.common.constant.model.MsgCode DB006 = DB("未找到对应数据类型转换");
    com.xinghuo.common.constant.model.MsgCode DB007 = DB("导入表名存在重复");
    com.xinghuo.common.constant.model.MsgCode DB008 = DB("建表数据与当前操作数据库不匹配");
    com.xinghuo.common.constant.model.MsgCode DB009 = DB("未找到表信息");
    com.xinghuo.common.constant.model.MsgCode DB010 = DB("数据库?，未找到此表:?");
    /*== 系统自带表 DB1 ==*/
    com.xinghuo.common.constant.model.MsgCode DB101 = DB("系统自带表,不允许被删除");
    com.xinghuo.common.constant.model.MsgCode DB102 = DB("系统自带表,不允许被编辑");
    /*== 表已经被使用 DB2 ==*/
    com.xinghuo.common.constant.model.MsgCode DB201 = DB("表已经被使用,不允许被删除");
    com.xinghuo.common.constant.model.MsgCode DB202 = DB("表已经被使用,不允许被编辑");
    com.xinghuo.common.constant.model.MsgCode DB301 = DB("数据库连接成功");
    com.xinghuo.common.constant.model.MsgCode DB302 = DB("数据库连接失败");


    /**
     * 工作流相关错误码
     */
    /*========0-状态、短提示==========*/
    com.xinghuo.common.constant.model.MsgCode WF001 = WF("必填值");
    com.xinghuo.common.constant.model.MsgCode WF002 = WF("【审核通过】");
    com.xinghuo.common.constant.model.MsgCode WF003 = WF("【审核同意】");
    com.xinghuo.common.constant.model.MsgCode WF004 = WF("【审核拒绝】");
    com.xinghuo.common.constant.model.MsgCode WF005 = WF("审批已完成");
    com.xinghuo.common.constant.model.MsgCode WF006 = WF("开始");
    com.xinghuo.common.constant.model.MsgCode WF007 = WF("结束");
    com.xinghuo.common.constant.model.MsgCode WF008 = WF("必须有表");

    /*=========1-提示语句=========*/
    com.xinghuo.common.constant.model.MsgCode WF101 = WF("新增异常，需自主排查。");
    com.xinghuo.common.constant.model.MsgCode WF102 = WF("修改异常，需自主排查。");
    com.xinghuo.common.constant.model.MsgCode WF103 = WF("已到达该模板复制上限，请复制源模板");
    com.xinghuo.common.constant.model.MsgCode WF104 = WF("当前流程被处理，无法撤回流程");
    com.xinghuo.common.constant.model.MsgCode WF105 = WF("任务待审状态才能撤回");
    com.xinghuo.common.constant.model.MsgCode WF106 = WF("下一节点已审批，不能撤回！");
    com.xinghuo.common.constant.model.MsgCode WF107 = WF("包含子流程不能撤回");
    com.xinghuo.common.constant.model.MsgCode WF108 = WF("当前流程正在运行不能重复提交");
    com.xinghuo.common.constant.model.MsgCode WF109 = WF("单据规则不存在");
    com.xinghuo.common.constant.model.MsgCode WF110 = WF("包含子流程不能操作");
    com.xinghuo.common.constant.model.MsgCode WF111 = WF("当前流程未完成,不能修改工作流引擎");
    com.xinghuo.common.constant.model.MsgCode WF112 = WF("已审核完成");
    com.xinghuo.common.constant.model.MsgCode WF113 = WF("未找到流程引擎");
    com.xinghuo.common.constant.model.MsgCode WF114 = WF("驳回节点不能是子流程");
    com.xinghuo.common.constant.model.MsgCode WF115 = WF("该流程工单已删除");
    com.xinghuo.common.constant.model.MsgCode WF116 = WF("当前流程正在运行不能删除");
    com.xinghuo.common.constant.model.MsgCode WF117 = WF("功能流程不能删除");
    com.xinghuo.common.constant.model.MsgCode WF118 = WF("不能删除");
    com.xinghuo.common.constant.model.MsgCode WF119 = WF("系统表单反射失败");
    com.xinghuo.common.constant.model.MsgCode WF120 = WF("该流程工单已撤回");
    com.xinghuo.common.constant.model.MsgCode WF121 = WF("该流程工单已终止");
    com.xinghuo.common.constant.model.MsgCode WF122 = WF("没有权限操作");
    com.xinghuo.common.constant.model.MsgCode WF123 = WF("该流程待办已删除");
    com.xinghuo.common.constant.model.MsgCode WF124 = WF("下一节点为选择分支无法批量审批!");
    com.xinghuo.common.constant.model.MsgCode WF125 = WF("包含子流程不能终止");
    com.xinghuo.common.constant.model.MsgCode WF126 = WF("该版本内有工单任务流转,无法删除!");
    com.xinghuo.common.constant.model.MsgCode WF127 = WF("该功能已被流程引用，请重新选择关联功能！");
    com.xinghuo.common.constant.model.MsgCode WF128 = WF("条件流程包含候选人无法批量通过！");
    com.xinghuo.common.constant.model.MsgCode WF129 = WF("冻结不能操作！");
    com.xinghuo.common.constant.model.MsgCode WF130 = WF("禁用成功");
    com.xinghuo.common.constant.model.MsgCode WF131 = WF("启用成功");

    /**
     * 在线开发相关错误码
     *
     * @param desc
     * @return
     */
    /*=========1-错误提示=========*/
    com.xinghuo.common.constant.model.MsgCode VS401 = VS("该模板内表单内容为空，无法");
    com.xinghuo.common.constant.model.MsgCode VS402 = VS("该模板内列表内容为空，无法");
    com.xinghuo.common.constant.model.MsgCode VS403 = VS("该功能未配置流程不可用");
    com.xinghuo.common.constant.model.MsgCode VS404 = VS("单行输入不能重复");
    com.xinghuo.common.constant.model.MsgCode VS405 = VS("当前表单原数据已被调整，请重新进入该页面编辑并提交数据");
    com.xinghuo.common.constant.model.MsgCode VS406 = VS("该功能配置的流程处于停用");
    com.xinghuo.common.constant.model.MsgCode VS407 = VS("表头名称不可更改,表头行不能删除");
    com.xinghuo.common.constant.model.MsgCode VS408 = VS("请至少选择一个数据表");

    static com.xinghuo.common.constant.model.MsgCode MSG(String desc) {
        return new com.xinghuo.common.constant.model.MsgCode("message", desc);
    }

    static com.xinghuo.common.constant.model.MsgCode LOG(String desc) {
        return new com.xinghuo.common.constant.model.MsgCode("login", desc);
    }

    static com.xinghuo.common.constant.model.MsgCode DB(String desc) {
        return new com.xinghuo.common.constant.model.MsgCode("database", desc);
    }

    static com.xinghuo.common.constant.model.MsgCode WF(String desc) {
        return new com.xinghuo.common.constant.model.MsgCode("workflow", desc);
    }

    static com.xinghuo.common.constant.model.MsgCode VS(String desc) {
        return new com.xinghuo.common.constant.model.MsgCode("visual", desc);
    }


}
