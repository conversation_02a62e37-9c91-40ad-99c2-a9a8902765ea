package com.xinghuo.common.hutool;


import com.xinghuo.common.util.core.FileXhUtil;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;

import static org.junit.jupiter.api.Assertions.*;

class FileXhUtilTest {


    @Test
    void existsSuffixShouldReturnTrueForNonJsonFile() {
        MockMultipartFile multipartFile = new MockMultipartFile("test", "test.txt", "text/plain", "test".getBytes());
        assertTrue(FileXhUtil.existsSuffix(multipartFile, "json"));
    }

    @Test
    void existsSuffixShouldReturnFalseForJsonFile() {
        MockMultipartFile multipartFile = new MockMultipartFile("test", "test.json", "application/json", "{\"test\":\"test\"}".getBytes());
        assertFalse(FileXhUtil.existsSuffix(multipartFile, "json"));
    }

    @Test
    void getFileContentShouldReturnFileContent() {
        MockMultipartFile multipartFile = new MockMultipartFile("test", "test.txt", "text/plain", "test".getBytes());
        String content = FileXhUtil.getFileContent(multipartFile);
        assertEquals("test", content);
    }

    @Test
    void getFileContentShouldReturnEmptyForEmptyFile() {
        MockMultipartFile multipartFile = new MockMultipartFile("test", "test.txt", "text/plain", "".getBytes());
        String content = FileXhUtil.getFileContent(multipartFile);
        assertTrue(content.isEmpty());
    }
}
