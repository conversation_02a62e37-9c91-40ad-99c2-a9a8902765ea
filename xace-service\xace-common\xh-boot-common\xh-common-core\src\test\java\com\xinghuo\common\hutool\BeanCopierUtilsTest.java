package com.xinghuo.common.hutool;


class BeanCopierUtilsTest {
//
//    @Test
//    void copyShouldReturnNonNullValue() {
//        List<String> source = Arrays.asList("test1", "test2");
//        List<String> target = BeanCopierUtils.copy(source, List.class);
//        assertNotNull(target);
//    }
//
//    @Test
//    void copyShouldReturnEqualValue() {
//        List<String> source = Arrays.asList("test1", "test2");
//        List<String> target = BeanCopierUtils.copy(source, List.class);
//        assertEquals(source, target);
//    }
//
//    @Test
//    void copyListShouldReturnNonNullValue() {
//        List<List> sources = Arrays.asList(Arrays.asList("test1", "test2"), Arrays.asList("test3", "test4"));
//        List<List> targets = BeanCopierUtils.copyList(sources, List.class);
//        assertNotNull(targets);
//    }
//
//    @Test
//    void copyListShouldReturnEqualValue() {
//        List<List> sources = Arrays.asList(Arrays.asList("test1", "test2"), Arrays.asList("test3", "test4"));
//        List<List> targets = BeanCopierUtils.copyList(sources, List.class);
//        assertEquals(sources, targets);
//    }
//
//    @Test
//    void copyUsingConvertShouldReturnNonNullValue() {
//        List<String> source = Arrays.asList("test1", "test2");
//        String target = BeanCopierUtils.copyUsingConvert(source, String.class);
//        assertNotNull(target);
//    }
//
//    @Test
//    void copyUsingConvertShouldReturnEqualValue() {
//        List<String> source = Arrays.asList("test1", "test2");
//        String target = BeanCopierUtils.copyUsingConvert(source, String.class);
//        assertEquals("test1,test2", target);
//    }
//
//    @Test
//    void copyListUsingConvertShouldReturnNonNullValue() {
//        List<List<String>> sources = Arrays.asList(Arrays.asList("test1", "test2"), Arrays.asList("test3", "test4"));
//        List<String> targets = BeanCopierUtils.copyListUsingConvert(sources, String.class);
//        assertNotNull(targets);
//    }
//
//    @Test
//    void copyListUsingConvertShouldReturnEqualValue() {
//        List<List<String>> sources = Arrays.asList(Arrays.asList("test1", "test2"), Arrays.asList("test3", "test4"));
//        List<String> targets = BeanCopierUtils.copyListUsingConvert(sources, String.class);
//        assertEquals(Arrays.asList("test1,test2", "test3,test4"), targets);
//    }
}

