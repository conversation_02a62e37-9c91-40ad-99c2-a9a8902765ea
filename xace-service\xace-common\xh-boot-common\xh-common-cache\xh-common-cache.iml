<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="FacetManager">
    <facet type="JRebel" name="JRebel">
      <configuration>
        <option name="ideModuleStorage">
          <map>
            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
            <entry key="jrebelEnabled" value="true" />
            <entry key="lastExternalPluginCheckTime" value="1707269421882" />
            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD54aC1jb21tb24tcmVkaXM8L2lkPjxjbGFzc3BhdGg+PGRpciBuYW1lPSJHOi92Mi9wZC14YWNlLXYyL3hhY2Utc2VydmljZS94YWNlLWNvbW1vbi94aC1ib290LWNvbW1vbi94aC1jb21tb24tcmVkaXMvdGFyZ2V0L2NsYXNzZXMiPjwvZGlyPjwvY2xhc3NwYXRoPjwvYXBwbGljYXRpb24+" />
          </map>
        </option>
        <option name="version" value="8" />
      </configuration>
    </facet>
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
  </component>
</module>