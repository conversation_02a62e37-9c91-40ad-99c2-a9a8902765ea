package com.xinghuo.common.util.context;

/**
 * 用于在多数据源环境中进行上下文切换。使用ThreadLocal来存储和获取当前线程的数据库上下文信息
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class DataSourceContextHolder {

    //用于存储当前线程的数据库名称
    private static final ThreadLocal<String> CONTEXT_DB_NAME_HOLDER = new ThreadLocal<>();

    //存储当前线程的数据库ID
    private static final ThreadLocal<String> CONTEXT_DB_ID_HOLDER = new ThreadLocal<>();

   //用于存储一个布尔值，表示是否已分配数据源
   private static final ThreadLocal<Boolean> CONTEXT_ASSIGN_HOLDER = new ThreadLocal<>();

    /**
     * 设置当前数据库
     */
    public static void setDatasource(String dbId,String dbName, boolean assign) {
        CONTEXT_DB_NAME_HOLDER.set(dbName);
        CONTEXT_DB_ID_HOLDER.set(dbId);
        CONTEXT_ASSIGN_HOLDER.set(assign);
    }

    /**
     * 取得当前数据源Id
     */
    public static String getDatasourceId() {
        return CONTEXT_DB_ID_HOLDER.get();
    }
    /**
     * 取得当前数据源名称
     */
    public static String getDatasourceName() {
        return CONTEXT_DB_NAME_HOLDER.get();
    }

    public static Boolean isAssignDataSource(){
        return Boolean.TRUE.equals(CONTEXT_ASSIGN_HOLDER.get());
    }

    /**
     * 清除上下文数据
     */
    public static void clearDatasourceType() {
        CONTEXT_DB_NAME_HOLDER.remove();
        CONTEXT_DB_ID_HOLDER.remove();
        CONTEXT_ASSIGN_HOLDER.remove();
    }
}
