package com.xinghuo.common.util.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;

import java.io.IOException;

/**
 * 自定义反序列化器，将JSON数组或字符串反序列化为字符串
 * <AUTHOR>
 */
public class ListToStringDeserializer extends StdDeserializer<String> {

    public ListToStringDeserializer() {
        this(null); // 默认构造函数，调用带Class<?>参数的构造函数
    }

    public ListToStringDeserializer(Class<?> vc) {
        super(vc); // 继承自StdDeserializer的构造函数
    }

    /**
     * 反序列化方法，将JSON解析为字符串
     * @param jp JsonParser，用于解析JSON
     * @param ctxt DeserializationContext，提供上下文信息
     * @return String，解析后的字符串
     * @throws IOException 如果解析过程中发生IO异常
     */
    @Override
    public String deserialize(JsonParser jp, DeserializationContext ctxt)
            throws IOException {
        JsonNode node = jp.readValueAsTree(); // 将JSON解析为JsonNode

        if (node.isArray()) {
            // 如果节点是数组，将其转换为字符串并返回
            return node.toString();
        } else if (node.isTextual()) {
            // 如果节点是文本，直接返回该文本
            return node.asText();
        }

        // 如果节点既不是数组也不是文本，返回空字符串
        return "";
    }
}
