SET FOREIGN_KEY_CHECKS=0;
ALTER TABLE `base_appdata` ADD COLUMN `F_SystemId`  varchar(50) NULL DEFAULT NULL COMMENT '系统id' AFTER `F_DeleteMark`;
ALTER TABLE `base_appdata` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_SystemId`;
ALTER TABLE `base_appdata` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_appdata` MODIFY COLUMN `F_ObjectType`  varchar(50) NULL DEFAULT NULL COMMENT '对象类型' AFTER `F_Id`;
ALTER TABLE `base_appdata` MODIFY COLUMN `F_ObjectId`  varchar(50) NULL DEFAULT NULL COMMENT '对象主键' AFTER `F_ObjectType`;
ALTER TABLE `base_appdata` MODIFY COLUMN `F_ObjectData`  longtext NULL COMMENT '对象json' AFTER `F_ObjectId`;
ALTER TABLE `base_appdata` MODIFY COLUMN `F_Description`  longtext NULL COMMENT '描述' AFTER `F_ObjectData`;
ALTER TABLE `base_appdata` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_appdata` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
update base_appdata set F_SystemId='309228585019769285' where F_SystemId is null;
commit;

ALTER TABLE `base_authorize` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_CreatorUserId`;
ALTER TABLE `base_authorize` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_authorize` MODIFY COLUMN `F_ItemType`  varchar(50) NULL DEFAULT NULL COMMENT '项目类型' AFTER `F_Id`;
ALTER TABLE `base_authorize` MODIFY COLUMN `F_ItemId`  varchar(50) NULL DEFAULT NULL COMMENT '项目主键' AFTER `F_ItemType`;
ALTER TABLE `base_authorize` MODIFY COLUMN `F_ObjectType`  varchar(50) NULL DEFAULT NULL COMMENT '对象类型' AFTER `F_ItemId`;
ALTER TABLE `base_authorize` MODIFY COLUMN `F_ObjectId`  varchar(50) NULL DEFAULT NULL COMMENT '对象主键' AFTER `F_ObjectType`;
ALTER TABLE `base_authorize` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;


ALTER TABLE `base_billrule` ADD COLUMN `F_Category`  varchar(50) NULL DEFAULT NULL COMMENT '业务分类' AFTER `F_DeleteMark`;
ALTER TABLE `base_billrule` ADD COLUMN `F_CategoryId`  varchar(50) NULL DEFAULT NULL COMMENT '业务分类id' AFTER `F_Category`;
ALTER TABLE `base_billrule` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_CategoryId`;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '单据名称' AFTER `F_Id`;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '单据编号' AFTER `F_FullName`;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_Prefix`  varchar(50) NULL DEFAULT NULL COMMENT '单据前缀' AFTER `F_EnCode`;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_DateFormat`  varchar(50) NULL DEFAULT NULL COMMENT '日期格式' AFTER `F_Prefix`;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_StartNumber`  varchar(50) NULL DEFAULT NULL COMMENT '流水起始' AFTER `F_Digit`;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_Example`  varchar(100) NULL DEFAULT NULL COMMENT '流水范例' AFTER `F_StartNumber`;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_OutputNumber`  varchar(100) NULL DEFAULT NULL COMMENT '输出流水号' AFTER `F_ThisNumber`;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_Description`  longtext NULL COMMENT '描述' AFTER `F_OutputNumber`;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_billrule` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
update base_billrule set F_Category = '306427554749773829' where F_Category is null;
commit;


-- 模块列表权限表
ALTER TABLE `base_columnspurview` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_DeleteMark`;
ALTER TABLE `base_columnspurview` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '主键Id' FIRST ;
ALTER TABLE `base_columnspurview` MODIFY COLUMN `F_FieldList`  longtext NULL COMMENT '列表字段数组' AFTER `F_Id`;
ALTER TABLE `base_columnspurview` MODIFY COLUMN `F_ModuleId`  varchar(50) NULL DEFAULT NULL COMMENT '模块ID' AFTER `F_FieldList`;
ALTER TABLE `base_columnspurview` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_columnspurview` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_columnspurview` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;


ALTER TABLE `base_comfields` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_Field`;
ALTER TABLE `base_comfields` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '主键' FIRST ;
ALTER TABLE `base_comfields` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述说明' AFTER `F_Id`;
ALTER TABLE `base_comfields` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_comfields` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_comfields` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_comfields` MODIFY COLUMN `F_FieldName`  varchar(50) NULL DEFAULT NULL COMMENT '列说名' AFTER `F_DeleteUserId`;
ALTER TABLE `base_comfields` MODIFY COLUMN `F_DataType`  varchar(50) NULL DEFAULT NULL COMMENT '类型' AFTER `F_FieldName`;
ALTER TABLE `base_comfields` MODIFY COLUMN `F_DataLength`  varchar(50) NULL DEFAULT NULL COMMENT '长度' AFTER `F_DataType`;
ALTER TABLE `base_comfields` MODIFY COLUMN `F_Field`  varchar(50) NULL DEFAULT NULL COMMENT '列名' AFTER `F_AllowNull`;


CREATE TABLE `base_commonwords` (
`F_Id`  varchar(50) NOT NULL COMMENT '自然主键' ,
`F_SystemIds`  text NULL COMMENT '应用id' ,
`F_SystemNames`  text NULL COMMENT '应用名称' ,
`F_CommonWordsText`  longtext NULL COMMENT '常用语' ,
`F_CommonWordsType`  int(11) NULL DEFAULT NULL COMMENT '常用语类型(0:系统,1:个人)' ,
`F_SortCode`  bigint(20) NULL DEFAULT NULL COMMENT '排序' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '有效标志' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;


ALTER TABLE `base_datainterface` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_DataProcessing`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '主键ID' FIRST ;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_CategoryId`  varchar(50) NOT NULL COMMENT '分组ID' AFTER `F_Id`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_FullName`  varchar(50) NOT NULL COMMENT '接口名称' AFTER `F_CategoryId`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_RequestMethod`  varchar(50) NOT NULL COMMENT '请求方式' AFTER `F_Path`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_ResponseType`  varchar(50) NOT NULL COMMENT '返回类型' AFTER `F_RequestMethod`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_Query`  longtext NOT NULL COMMENT '查询语句' AFTER `F_ResponseType`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_RequestParameters`  longtext NULL COMMENT '请求参数JSON' AFTER `F_Query`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_IpAddress`  longtext NULL AFTER `F_RequestParameters`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '接口编码' AFTER `F_IpAddress`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述或说明' AFTER `F_EnabledMark`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NOT NULL COMMENT '创建用户id' AFTER `F_CreatorTime`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户id' AFTER `F_LastModifyTime`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户id' AFTER `F_DeleteTime`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_DbLinkId`  varchar(100) NOT NULL COMMENT '数据源id' AFTER `F_DeleteUserId`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_RequestHeaders`  longtext NULL AFTER `F_CheckType`;
ALTER TABLE `base_datainterface` MODIFY COLUMN `F_DataProcessing`  longtext NULL COMMENT '数据处理' AFTER `F_RequestHeaders`;


ALTER TABLE `base_datainterfacelog` ADD COLUMN `F_OauthAppId`  varchar(50) NULL DEFAULT NULL AFTER `F_InvokWasteTime`;
ALTER TABLE `base_datainterfacelog` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_OauthAppId`;
ALTER TABLE `base_datainterfacelog` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '主键id' FIRST ;
ALTER TABLE `base_datainterfacelog` MODIFY COLUMN `F_InvokId`  varchar(50) NOT NULL COMMENT '调用接口id' AFTER `F_Id`;
ALTER TABLE `base_datainterfacelog` MODIFY COLUMN `F_UserId`  varchar(50) NULL DEFAULT NULL COMMENT '调用者id' AFTER `F_InvokTime`;
ALTER TABLE `base_datainterfacelog` MODIFY COLUMN `F_InvokIp`  varchar(50) NULL DEFAULT NULL COMMENT '请求ip' AFTER `F_UserId`;
-- ALTER TABLE `base_datainterfacelog` MODIFY COLUMN `F_InvokDevice`  varchar(255) NULL DEFAULT NULL COMMENT '请求设备' AFTER `F_InvokIp`;
ALTER TABLE `base_datainterfacelog` MODIFY COLUMN `F_InvokType`  varchar(50) NULL DEFAULT NULL COMMENT '请求类型' AFTER `F_InvokDevice`;
-- ALTER TABLE `base_datainterfacelog` DROP COLUMN `F_requestJson`;
-- ALTER TABLE `base_datainterfacelog` DROP COLUMN `F_responseJson`;


ALTER TABLE `base_dbbackup` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_DeleteUserId`;
ALTER TABLE `base_dbbackup` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_dbbackup` MODIFY COLUMN `F_BackupDbName`  varchar(50) NULL DEFAULT NULL COMMENT '备份库名' AFTER `F_Id`;
ALTER TABLE `base_dbbackup` MODIFY COLUMN `F_FileName`  varchar(50) NULL DEFAULT NULL COMMENT '文件名称' AFTER `F_BackupTime`;
ALTER TABLE `base_dbbackup` MODIFY COLUMN `F_FileSize`  varchar(50) NULL DEFAULT NULL COMMENT '文件大小' AFTER `F_FileName`;
ALTER TABLE `base_dbbackup` MODIFY COLUMN `F_FilePath`  longtext NULL COMMENT '文件路径' AFTER `F_FileSize`;
ALTER TABLE `base_dbbackup` MODIFY COLUMN `F_Description`  longtext NULL COMMENT '描述' AFTER `F_FilePath`;
ALTER TABLE `base_dbbackup` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_dbbackup` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_dbbackup` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;

ALTER TABLE `base_dblink` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_OracleParam`;
ALTER TABLE `base_dblink` ADD COLUMN `F_Oracle_Extend`  tinyint(4) NULL DEFAULT NULL COMMENT 'Oracle扩展开关 1:开启 0:关闭' AFTER `F_TenantId`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '连接名称' AFTER `F_Id`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_DbType`  varchar(50) NULL DEFAULT NULL COMMENT '连接驱动' AFTER `F_FullName`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_Host`  varchar(50) NULL DEFAULT NULL COMMENT '主机地址' AFTER `F_DbType`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_UserName`  varchar(50) NULL DEFAULT NULL COMMENT '用户' AFTER `F_Port`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_Password`  varchar(50) NULL DEFAULT NULL COMMENT '密码' AFTER `F_UserName`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_ServiceName`  varchar(50) NULL DEFAULT NULL COMMENT '服务名称' AFTER `F_Password`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_Description`  longtext NULL COMMENT '描述' AFTER `F_ServiceName`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_DbSchema`  varchar(50) NULL DEFAULT NULL COMMENT '模式' AFTER `F_DeleteUserId`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_TableSpace`  varchar(50) NULL DEFAULT NULL COMMENT '表空间' AFTER `F_DbSchema`;
ALTER TABLE `base_dblink` MODIFY COLUMN `F_OracleParam`  varchar(100) NULL DEFAULT NULL COMMENT 'oracle连接参数' AFTER `F_TableSpace`;


ALTER TABLE `base_dictionarydata` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_DictionaryTypeId`;
ALTER TABLE `base_dictionarydata` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_dictionarydata` MODIFY COLUMN `F_ParentId`  varchar(50) NULL DEFAULT NULL COMMENT '上级' AFTER `F_Id`;
ALTER TABLE `base_dictionarydata` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '名称' AFTER `F_ParentId`;
ALTER TABLE `base_dictionarydata` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '编号' AFTER `F_FullName`;
ALTER TABLE `base_dictionarydata` MODIFY COLUMN `F_SimpleSpelling`  longtext NULL COMMENT '拼音' AFTER `F_EnCode`;
ALTER TABLE `base_dictionarydata` MODIFY COLUMN `F_Description`  longtext NULL COMMENT '描述' AFTER `F_IsDefault`;
ALTER TABLE `base_dictionarydata` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_dictionarydata` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_dictionarydata` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_dictionarydata` MODIFY COLUMN `F_DictionaryTypeId`  varchar(50) NULL DEFAULT NULL COMMENT '类别主键' AFTER `F_DeleteUserId`;
-- ALTER TABLE `base_dictionarydata` DROP COLUMN `F_CssClass`;
-- ALTER TABLE `base_dictionarydata` DROP COLUMN `F_ListClass`;

ALTER TABLE `base_dictionarytype` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_DeleteUserId`;
ALTER TABLE `base_dictionarytype` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_dictionarytype` MODIFY COLUMN `F_ParentId`  varchar(50) NULL DEFAULT NULL COMMENT '上级' AFTER `F_Id`;
ALTER TABLE `base_dictionarytype` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '名称' AFTER `F_ParentId`;
ALTER TABLE `base_dictionarytype` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '编号' AFTER `F_FullName`;
ALTER TABLE `base_dictionarytype` MODIFY COLUMN `F_Description`  longtext NULL COMMENT '描述' AFTER `F_IsTree`;
ALTER TABLE `base_dictionarytype` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_dictionarytype` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_dictionarytype` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;

-- 添加 单据 业务分类字典项
INSERT INTO `base_dictionarytype` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_IsTree`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_TenantId`) VALUES ('306427078100678661', 'afcc3a0952df4d1bad7d83cc8eb20fbd', '业务分类', 'businessType', '0', '业务分类', '0', NULL, '2022-06-14 16:11:51', '***************', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_TenantId`) VALUES ('306427554749773829', '0', '项目管理', 'projectManage', 'XMGL', NULL, '项目管理', '0', '1', '2022-06-14 16:13:44', '***************', NULL, NULL, NULL, NULL, NULL, '306427078100678661', NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_TenantId`) VALUES ('306427745951315973', '0', '销售管理', 'salesManage', 'XSGL', NULL, '销售管理', '0', '1', '2022-06-14 16:14:30', '***************', NULL, NULL, NULL, NULL, NULL, '306427078100678661', NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_TenantId`) VALUES ('306428013560494085', '0', '公文管理', 'documentManage', 'GWGL', NULL, '公文管理', '0', '1', '2022-06-14 16:15:34', '***************', NULL, NULL, NULL, NULL, NULL, '306427078100678661', NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_TenantId`) VALUES ('306428174336555013', '0', '合同管理', 'contractManage', 'HTGL', NULL, '合同管理', '0', '1', '2022-06-14 16:16:12', '***************', NULL, NULL, NULL, NULL, NULL, '306427078100678661', NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_TenantId`) VALUES ('306428289763801093', '0', '资产管理', 'assetManage', 'ZCGL', NULL, '资产管理', '0', '1', '2022-06-14 16:16:39', '***************', NULL, NULL, NULL, NULL, NULL, '306427078100678661', NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_TenantId`) VALUES ('306428714160257029', '0', '日常工作', 'routine', 'RCGZ', NULL, '日常工作', '0', '1', '2022-06-14 16:18:21', '***************', NULL, NULL, NULL, NULL, NULL, '306427078100678661', NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_TenantId`) VALUES ('306428806950844421', '0', '人事管理', 'personnelManage', 'RSGL', NULL, '人事管理', '0', '1', '2022-06-14 16:18:43', '***************', NULL, NULL, NULL, NULL, NULL, '306427078100678661', NULL);
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_TenantId`) VALUES ('306428899179395077', '0', '行政管理', 'administrativeManage', 'XZGL', NULL, '行政管理', '0', '1', '2022-06-14 16:19:05', '***************', NULL, NULL, NULL, NULL, NULL, '306427078100678661', NULL);


ALTER TABLE `base_file` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_OldFileVersionId`;
ALTER TABLE `base_file` MODIFY COLUMN `F_Id`  varchar(255) NOT NULL COMMENT 'id' FIRST ;
ALTER TABLE `base_file` MODIFY COLUMN `F_FileVersion`  varchar(255) NOT NULL COMMENT '文件版本' AFTER `F_Id`;
ALTER TABLE `base_file` MODIFY COLUMN `F_FileName`  varchar(255) NOT NULL COMMENT '文件名' AFTER `F_FileVersion`;
ALTER TABLE `base_file` MODIFY COLUMN `F_Type`  varchar(255) NOT NULL COMMENT '上传方式: 1.local 本地上传  2.http http上传  3.create 新建' AFTER `F_FileName`;
ALTER TABLE `base_file` MODIFY COLUMN `F_Url`  varchar(255) NULL DEFAULT NULL COMMENT 'http上传的url' AFTER `F_Type`;
ALTER TABLE `base_file` MODIFY COLUMN `F_OldFileVersionId`  varchar(255) NULL DEFAULT NULL COMMENT '历史版本id' AFTER `F_Url`;
ALTER TABLE `base_filter` MODIFY COLUMN `F_Config`  text NULL COMMENT '过滤配置' AFTER `F_ModuleId`;
ALTER TABLE `base_filter` MODIFY COLUMN `F_ConfigApp`  text NULL COMMENT '过滤配置app' AFTER `F_TenantId`;

ALTER TABLE `base_group` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_DeleteUserId`;
ALTER TABLE `base_group` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_group` MODIFY COLUMN `F_FullName`  varchar(200) NULL DEFAULT NULL COMMENT '名称' AFTER `F_Id`;
ALTER TABLE `base_group` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '编码' AFTER `F_FullName`;
ALTER TABLE `base_group` MODIFY COLUMN `F_Type`  varchar(50) NULL DEFAULT NULL COMMENT '类型' AFTER `F_EnCode`;
ALTER TABLE `base_group` MODIFY COLUMN `F_Description`  varchar(500) NULL DEFAULT NULL COMMENT '说明' AFTER `F_Type`;
ALTER TABLE `base_group` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_group` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_group` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;

-- ？？
ALTER TABLE `base_imcontent` ADD COLUMN `F_SENDDELETEMARK`  varchar(50) NULL DEFAULT NULL COMMENT '接收者删除标记' AFTER `F_State`;
ALTER TABLE `base_imcontent` ADD COLUMN `F_DELETEMARK`  int(20) NULL DEFAULT NULL COMMENT '删除标记' AFTER `F_SENDDELETEMARK`;
ALTER TABLE `base_imcontent` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_DELETEMARK`;
ALTER TABLE `base_imcontent` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_imcontent` MODIFY COLUMN `F_SendUserId`  varchar(50) NULL DEFAULT NULL COMMENT '发送者' AFTER `F_Id`;
ALTER TABLE `base_imcontent` MODIFY COLUMN `F_ReceiveUserId`  varchar(50) NULL DEFAULT NULL COMMENT '接收者' AFTER `F_SendTime`;
ALTER TABLE `base_imcontent` MODIFY COLUMN `F_Content`  longtext NULL COMMENT '内容' AFTER `F_ReceiveTime`;
ALTER TABLE `base_imcontent` MODIFY COLUMN `F_ContentType`  varchar(50) NULL DEFAULT NULL COMMENT '内容' AFTER `F_Content`;
ALTER TABLE `base_imreply` ADD COLUMN `F_ImreplySendDeleteMark`  varchar(50) NULL DEFAULT NULL COMMENT '接收者删除标记' AFTER `F_ReceiveTime`;
ALTER TABLE `base_imreply` ADD COLUMN `F_ImreplyDeleteMark`  int(20) NULL DEFAULT NULL COMMENT '删除标记' AFTER `F_ImreplySendDeleteMark`;
ALTER TABLE `base_imreply` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_ImreplyDeleteMark`;
ALTER TABLE `base_imreply` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '主键' FIRST ;
ALTER TABLE `base_imreply` MODIFY COLUMN `F_UserId`  varchar(50) NULL DEFAULT NULL COMMENT '用户主键' AFTER `F_Id`;
ALTER TABLE `base_imreply` MODIFY COLUMN `F_ReceiveUserId`  varchar(50) NULL DEFAULT NULL COMMENT '接收用户' AFTER `F_UserId`;

CREATE TABLE `base_interfaceoauth` (
`F_Id`  varchar(64) NOT NULL COMMENT '主键' ,
`F_AppId`  varchar(128) NOT NULL COMMENT '应用id' ,
`F_AppName`  varchar(50) NOT NULL COMMENT '应用名称' ,
`F_AppSecret`  varchar(128) NOT NULL COMMENT '应用秘钥' ,
`F_VerifySignature`  int(16) NULL DEFAULT NULL COMMENT '验证签名' ,
`F_UsefulLife`  datetime NULL DEFAULT NULL COMMENT '使用期限' ,
`F_WhiteList`  text NULL COMMENT '白名单' ,
`F_BlackList`  text NULL COMMENT '黑名单' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建人' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '最后修改人' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '最后修改时间' ,
`F_SortCode`  bigint(20) NULL DEFAULT NULL COMMENT '排序' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '状态' ,
`F_Description`  text NULL COMMENT '说明' ,
`F_DataInterfaceIds`  text NULL COMMENT '接口列表' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标记' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
-- ??
ALTER TABLE `base_message` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_Files`;
ALTER TABLE `base_message` ADD COLUMN `F_FlowType`  int(11) NULL DEFAULT NULL COMMENT '流程类型' AFTER `F_TenantId`;
ALTER TABLE `base_message` ADD COLUMN `F_CoverImage`  varchar(200) NULL DEFAULT NULL COMMENT '封面图片' AFTER `F_FlowType`;
ALTER TABLE `base_message` ADD COLUMN `F_ExpirationTime`  datetime NULL DEFAULT NULL COMMENT '过期时间' AFTER `F_CoverImage`;
ALTER TABLE `base_message` ADD COLUMN `F_Category`  varchar(50) NULL DEFAULT NULL COMMENT '分类 1-公告 2-通知' AFTER `F_ExpirationTime`;
ALTER TABLE `base_message` ADD COLUMN `F_RemindCategory`  int(11) NULL DEFAULT NULL COMMENT '提醒方式 1-站内信 2-自定义 3-不通知' AFTER `F_Category`;
ALTER TABLE `base_message` ADD COLUMN `F_SendConfigId`  varchar(50) NULL DEFAULT NULL COMMENT '发送配置' AFTER `F_RemindCategory`;
ALTER TABLE `base_message` ADD COLUMN `F_Excerpt`  text NULL COMMENT '摘要' AFTER `F_SendConfigId`;
ALTER TABLE `base_message` ADD COLUMN `F_DefaultTitle`  varchar(200) NULL DEFAULT NULL COMMENT '消息模板标题' AFTER `F_Excerpt`;
ALTER TABLE `base_message` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_message` MODIFY COLUMN `F_Title`  varchar(200) NULL DEFAULT NULL COMMENT '标题' AFTER `F_Type`;
ALTER TABLE `base_message` MODIFY COLUMN `F_BodyText`  longtext NULL COMMENT '正文' AFTER `F_Title`;
ALTER TABLE `base_message` MODIFY COLUMN `F_ToUserIds`  longtext NULL COMMENT '收件用户' AFTER `F_PriorityLevel`;
ALTER TABLE `base_message` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_IsRead`;
ALTER TABLE `base_message` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_message` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_message` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_message` MODIFY COLUMN `F_Files`  longtext NULL COMMENT '附件' AFTER `F_DeleteUserId`;

ALTER TABLE `base_messagereceive` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_BodyText`;
ALTER TABLE `base_messagereceive` ADD COLUMN `F_Excerpt`  text NULL COMMENT '摘要' AFTER `F_TenantId`;
ALTER TABLE `base_messagereceive` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_messagereceive` MODIFY COLUMN `F_MessageId`  varchar(50) NULL DEFAULT NULL COMMENT '消息主键' AFTER `F_Id`;
ALTER TABLE `base_messagereceive` MODIFY COLUMN `F_UserId`  varchar(50) NULL DEFAULT NULL COMMENT '用户主键' AFTER `F_MessageId`;
ALTER TABLE `base_messagereceive` MODIFY COLUMN `F_BodyText`  longtext NULL COMMENT '内容' AFTER `F_ReadCount`;

CREATE TABLE `base_message_account_config` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键id' ,
`F_Type`  varchar(50) NULL DEFAULT NULL COMMENT '配置类型' ,
`F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '名称' ,
`F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '编码' ,
`F_AddressorName`  varchar(50) NULL DEFAULT NULL COMMENT '发件人昵称' ,
`F_SmtpServer`  varchar(50) NULL DEFAULT NULL COMMENT 'SMTP服务器' ,
`F_SmtpPort`  int(11) NULL DEFAULT NULL COMMENT 'SMTP端口' ,
`F_SslLink`  varchar(50) NULL DEFAULT NULL COMMENT 'SSL安全链接' ,
`F_SmtpUser`  varchar(50) NULL DEFAULT NULL COMMENT 'SMTP用户' ,
`F_SmtpPassword`  varchar(50) NULL DEFAULT NULL COMMENT 'SMTP密码' ,
`F_Channel`  varchar(50) NULL DEFAULT NULL COMMENT '渠道' ,
`F_SmsSignature`  varchar(50) NULL DEFAULT NULL COMMENT '短信签名' ,
`F_AppId`  varchar(50) NULL DEFAULT NULL COMMENT '应用ID' ,
`F_AppSecret`  varchar(500) NULL DEFAULT NULL COMMENT '应用Secret' ,
`F_EndPoint`  varchar(50) NULL DEFAULT NULL COMMENT 'EndPoint（阿里云）' ,
`F_SdkAppId`  varchar(50) NULL DEFAULT NULL COMMENT 'SDK AppID（腾讯云）' ,
`F_AppKey`  varchar(50) NULL DEFAULT NULL COMMENT 'AppKey（腾讯云）' ,
`F_ZoneName`  varchar(50) NULL DEFAULT NULL COMMENT '地域域名（腾讯云）' ,
`F_ZoneParam`  varchar(50) NULL DEFAULT NULL COMMENT '地域参数（腾讯云）' ,
`F_EnterpriseId`  varchar(50) NULL DEFAULT NULL COMMENT '企业id' ,
`F_AgentId`  varchar(50) NULL DEFAULT NULL COMMENT 'AgentID' ,
`F_WebhookType`  varchar(50) NULL DEFAULT NULL COMMENT 'WebHook类型' ,
`F_WebhookAddress`  varchar(500) NULL DEFAULT NULL COMMENT 'WebHook地址' ,
`F_ApproveType`  varchar(50) NULL DEFAULT NULL COMMENT '认证类型' ,
`F_Bearer`  varchar(500) NULL DEFAULT NULL COMMENT 'Bearer令牌' ,
`F_UserName`  varchar(50) NULL DEFAULT NULL COMMENT '用户名（基本认证）' ,
`F_Password`  varchar(50) NULL DEFAULT NULL COMMENT '密码（基本认证）' ,
`F_SortCode`  int(11) NULL DEFAULT NULL COMMENT '排序' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '状态' ,
`F_Description`  text NULL COMMENT '说明' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_DeleteMark`  int(2) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_message_data_type` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键id' ,
`F_Type`  varchar(50) NULL DEFAULT NULL COMMENT '数据类型（1：消息类型，2：渠道，3：webhook类型，4：消息来源）' ,
`F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '数据名称' ,
`F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '数据编码（为防止与系统后续更新的功能的数据编码冲突，客户自定义添加的功能的数据编码请以ZDY开头。例如：ZDY1）' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建人员' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改人员' ,
`F_DeleteMark`  int(2) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_EnabledMark`  int(2) NULL DEFAULT NULL COMMENT '状态' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_message_monitor` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键id' ,
`F_AccountId`  varchar(50) NULL DEFAULT NULL COMMENT '账号id' ,
`F_AccountName`  varchar(50) NULL DEFAULT NULL COMMENT '账号名称' ,
`F_AccountCode`  varchar(50) NULL DEFAULT NULL COMMENT '账号编码' ,
`F_MessageType`  varchar(50) NULL DEFAULT NULL COMMENT '消息类型' ,
`F_MessageSource`  varchar(50) NULL DEFAULT NULL COMMENT '消息来源' ,
`F_SendTime`  datetime NULL DEFAULT NULL COMMENT '发送时间' ,
`F_MessageTemplateId`  varchar(50) NULL DEFAULT NULL COMMENT '消息模板id' ,
`F_Title`  varchar(255) NULL DEFAULT NULL COMMENT '标题' ,
`F_ReceiveUser`  text NULL COMMENT '接收人' ,
`F_Content`  longtext NULL COMMENT '内容' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建人员' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改人员' ,
`F_DeleteMark`  int(2) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_EnabledMark`  int(2) NULL DEFAULT NULL COMMENT '状态' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_message_send_config` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键id' ,
`F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '名称' ,
`F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '编码' ,
`F_TemplateType`  varchar(50) NULL DEFAULT NULL COMMENT '模板类型' ,
`F_MessageSource`  varchar(50) NULL DEFAULT NULL COMMENT '消息来源' ,
`F_SortCode`  int(11) NULL DEFAULT NULL COMMENT '排序' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '状态' ,
`F_Description`  text NULL COMMENT '说明' ,
`F_UsedId`  longtext NULL COMMENT '被引用id' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建人员' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改人员' ,
`F_DeleteMark`  int(2) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_message_send_record` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键id' ,
`F_SendConfigId`  varchar(50) NULL DEFAULT NULL COMMENT '发送配置id' ,
`F_MessageSource`  varchar(50) NULL DEFAULT NULL COMMENT '消息来源' ,
`F_UsedId`  varchar(50) NULL DEFAULT NULL COMMENT '被引用id' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建人员' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改人员' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除人员' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
`F_EnabledMark`  int(2) NULL DEFAULT NULL COMMENT '状态' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_message_send_template` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键id' ,
`F_SendConfigId`  varchar(50) NULL DEFAULT NULL COMMENT '消息发送配置id' ,
`F_MessageType`  varchar(50) NULL DEFAULT NULL COMMENT '消息类型' ,
`F_TemplateId`  varchar(50) NULL DEFAULT NULL COMMENT '消息模板id' ,
`F_AccountConfigId`  varchar(50) NULL DEFAULT NULL COMMENT '账号配置id' ,
`F_SortCode`  int(11) NULL DEFAULT NULL COMMENT '排序' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '状态' ,
`F_Description`  text NULL COMMENT '说明' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建人员' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改人员' ,
`F_DeleteMark`  int(2) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_message_short_link` (
`F_Id`  varchar(50) NOT NULL COMMENT '自然主键' ,
`F_ShortLink`  varchar(255) NULL DEFAULT NULL COMMENT '短链接' ,
`F_RealPcLink`  longtext NULL COMMENT 'PC端链接' ,
`F_RealAppLink`  longtext NULL COMMENT 'App端链接' ,
`F_BodyText`  longtext NULL COMMENT '流程内容' ,
`F_IsUsed`  int(11) NULL DEFAULT NULL COMMENT '是否点击后失效' ,
`F_ClickNum`  int(11) NULL DEFAULT NULL COMMENT '点击次数' ,
`F_UnableNum`  int(11) NULL DEFAULT NULL COMMENT '失效次数' ,
`F_UnableTime`  datetime NULL DEFAULT NULL COMMENT '失效时间' ,
`F_UserId`  varchar(50) NULL DEFAULT NULL COMMENT '用户id' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_EnabledMark`  int(2) NULL DEFAULT NULL COMMENT '状态' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_message_sms_field` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键id' ,
`F_TemplateId`  varchar(50) NULL DEFAULT NULL COMMENT '消息模板id' ,
`F_FieldId`  varchar(50) NULL DEFAULT NULL COMMENT '参数id' ,
`F_SmsField`  varchar(50) NULL DEFAULT NULL COMMENT '短信变量' ,
`F_Field`  varchar(50) NULL DEFAULT NULL COMMENT '参数' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建人员' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改人员' ,
`F_DeleteMark`  int(2) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_EnabledMark`  int(2) NULL DEFAULT NULL COMMENT '状态' ,
`F_IsTitle`  int(2) NULL DEFAULT NULL COMMENT '是否标题' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_message_template_config` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键id' ,
`F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '名称' ,
`F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '编码' ,
`F_TemplateType`  varchar(50) NULL DEFAULT NULL COMMENT '模板类型' ,
`F_MessageSource`  varchar(50) NULL DEFAULT NULL COMMENT '消息来源' ,
`F_MessageType`  varchar(50) NULL DEFAULT NULL COMMENT '消息类型' ,
`F_EnabledMark`  int(2) NULL DEFAULT NULL COMMENT '状态' ,
`F_SortCode`  int(10) NULL DEFAULT NULL COMMENT '排序' ,
`F_Description`  text NULL COMMENT '说明' ,
`F_Title`  varchar(50) NULL DEFAULT NULL COMMENT '消息标题' ,
`F_Content`  longtext NULL COMMENT '消息内容' ,
`F_TemplateCode`  varchar(50) NULL DEFAULT NULL COMMENT '模板编号' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建人员' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改人员' ,
`F_DeleteMark`  int(2) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_WxSkip`  varchar(50) NULL DEFAULT NULL COMMENT '跳转方式' ,
`F_XcxAppId`  varchar(50) NULL DEFAULT NULL COMMENT '小程序id' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_message_template_param` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键id' ,
`F_TemplateId`  varchar(50) NULL DEFAULT NULL COMMENT '消息模板id' ,
`F_Field`  varchar(50) NULL DEFAULT NULL COMMENT '参数名称' ,
`F_FieldName`  varchar(50) NULL DEFAULT NULL COMMENT '参数说明' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建人员' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改人员' ,
`F_DeleteMark`  int(2) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_EnabledMark`  int(2) NULL DEFAULT NULL COMMENT '状态' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_message_wechat_user` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键id' ,
`F_GzhId`  varchar(50) NULL DEFAULT NULL COMMENT '微信公众号id' ,
`F_UserId`  varchar(50) NULL DEFAULT NULL COMMENT '用户id' ,
`F_OpenId`  varchar(50) NULL DEFAULT NULL COMMENT '微信公众号用户id' ,
`F_CloseMark`  int(2) NULL DEFAULT NULL COMMENT '是否关注' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建人员' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改人员' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除人员' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
`F_EnabledMark`  int(2) NULL DEFAULT NULL COMMENT '状态' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;


ALTER TABLE `base_module` ADD COLUMN `F_SystemId`  varchar(50) NULL DEFAULT NULL COMMENT '系统id' AFTER `F_ModuleId`;
ALTER TABLE `base_module` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_SystemId`;
ALTER TABLE `base_module` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_module` MODIFY COLUMN `F_ParentId`  varchar(50) NULL DEFAULT NULL COMMENT '功能上级' AFTER `F_Id`;
ALTER TABLE `base_module` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '功能名称' AFTER `F_Type`;
ALTER TABLE `base_module` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '功能编号' AFTER `F_FullName`;
ALTER TABLE `base_module` MODIFY COLUMN `F_UrlAddress`  longtext NULL COMMENT '功能地址' AFTER `F_EnCode`;
ALTER TABLE `base_module` MODIFY COLUMN `F_PropertyJson`  longtext NULL COMMENT '扩展属性' AFTER `F_IsDataAuthorize`;
ALTER TABLE `base_module` MODIFY COLUMN `F_Description`  longtext NULL COMMENT '描述' AFTER `F_PropertyJson`;
ALTER TABLE `base_module` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_module` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_module` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_module` MODIFY COLUMN `F_LinkTarget`  varchar(50) NULL DEFAULT NULL COMMENT '链接目标' AFTER `F_DeleteUserId`;
ALTER TABLE `base_module` MODIFY COLUMN `F_Category`  varchar(50) NULL DEFAULT NULL COMMENT '菜单分类' AFTER `F_LinkTarget`;
ALTER TABLE `base_module` MODIFY COLUMN `F_Icon`  varchar(200) NULL DEFAULT NULL COMMENT '菜单图标' AFTER `F_Category`;
ALTER TABLE `base_module` MODIFY COLUMN `F_ModuleId`  varchar(50) NULL DEFAULT NULL COMMENT '关联功能id' AFTER `F_IsFormAuthorize`;
-- ALTER TABLE `base_module` DROP COLUMN `F_EnabledHide`;
ALTER TABLE `base_module` DROP COLUMN `cc`;
update base_module set F_SystemId = '309228585019769285' where F_SystemId is null;
commit;

-- 流程表单改为 发起表单
delete from base_module where F_FullName='流程表单' and  F_Id = '8ee6ec6db2ac4a5e9b010100c7690798';
INSERT INTO `base_module` VALUES ('8ee6ec6db2ac4a5e9b010100c7690798', '76975bee62074937b8e3ab76e53b0797', 2, '发起表单', 'generator.flowForm', 'generator/flowForm', 1, 1, 1, '{\"iconBackgroundColor\":\"\",\"moduleId\":\"\"}', NULL, 17, 1, '2018-01-17 11:58:36', '***************', '2021-01-21 09:27:37', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-flowForm', 1, NULL, '309228585019769285', NULL,0);
commit;
-- 删除部门管理菜单
delete from base_module where F_FullName='部门管理';
commit;
-- 添加接口认证菜单
INSERT INTO `base_module` VALUES ('304171492382407173', '9193163d20604861b13193d24bcb7b0c', 2, '接口认证', 'systemData.interfaceAuth', 'systemData/interfaceOauth', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '接口认证，可选择接口列表归属于该认证实现接口开放', 34, 1, '2022-06-08 10:48:57', '***************', '2022-11-09 09:49:44', '***************', NULL, NULL, NULL, '_self', 'Web', 'ym-custom ym-custom-key', 1, NULL, '309228585019769285', NULL,0);
commit;
-- 修改magicApi菜单地址
update base_module set F_UrlAddress = replace(F_UrlAddress,'?token=','?Authorization=') where F_FullName like '%Magic Api%';
update base_module set F_UrlAddress = replace(F_UrlAddress,'http://*************/','http://*************:81/') where F_FullName like '%Magic Api%';
commit;
-- 删除 系统模板->短信模板、消息模板 . 添加 审批常用语
delete from base_module where  F_FullName='短信模板' and F_Id = '241521106777801989';
delete from base_module where  F_FullName='消息模板' and F_Id = '241559877435000069';
INSERT INTO `base_module` VALUES ('381460546165177989', '254224928688050885', 2, '审批常用语', 'commonWords', 'system/commonWords', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 28, 1, '2023-01-07 17:28:03', '***************', '2023-01-29 10:54:31', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-extend', 1, NULL, '309228585019769285', NULL,0);
commit;
-- 添加 消息中心 所有菜单
INSERT INTO `base_module` VALUES ('329626608874841989', 'E7443869-AFAD-4597-9B12-CD182369DF3C', 1, '消息中心', 'msgCenter', '', 0, 0, 0, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 40, 1, '2022-08-17 16:38:30', '***************', '2022-08-19 15:25:51', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-msgCenter', 0, NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('329626912135604101', '329626608874841989', 1, '账号配置', 'msgCenter.accountConfig', '', 0, 0, 0, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 0, 1, '2022-08-17 16:39:42', '***************', '2022-11-09 09:51:27', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-flowLaunch', 0, NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('329627156860659589', '329626912135604101', 2, '邮箱配置', 'msgCenter.accountConfig-mail', 'msgCenter/accountConfig/emailConfig', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 0, 1, '2022-08-17 16:40:40', '***************', '2022-11-09 09:51:51', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-flowLaunch', 1, NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('329627419180820357', '329626912135604101', 2, '短信配置', 'msgCenter.accountConfig.shortMsg', 'msgCenter/accountConfig/smsConfig', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 0, 1, '2022-08-17 16:41:43', '***************', '2022-11-09 09:52:12', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-flowLaunch', 1, NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('329627602450933637', '329626912135604101', 2, '企业微信配置', 'msgCenter.accountConfig.weCom', 'msgCenter/accountConfig/wxWorkConfig', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 0, 1, '2022-08-17 16:42:27', '***************', '2022-11-09 09:52:30', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-dataSource', 1, NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('329627785918179205', '329626912135604101', 2, '钉钉配置', 'msgCenter.accountConfig.ding', 'msgCenter/accountConfig/dingConfig', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 0, 1, '2022-08-17 16:43:10', '***************', '2022-11-09 09:52:48', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-dataSource', 1, NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('329627984967264133', '329626912135604101', 2, 'webhook配置', 'msgCenter.accountConfig.webhook', 'msgCenter/accountConfig/webhookConfig', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 0, 1, '2022-08-17 16:43:58', '***************', '2022-11-09 09:53:06', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-sysCache', 1, NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('329633072767045893', '329626608874841989', 2, '消息模板', 'msgCenter.msgTemplate', 'msgCenter/msgTemplate', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 0, 1, '2022-08-17 17:04:11', '048657c1-ecb1-4e5d-aced-6126ceae779e', '2022-11-09 09:50:52', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-msgTemplate', 1, NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('329633407585751301', '329626608874841989', 2, '发送配置', 'msgCenter.sendConfig', 'msgCenter/sendConfig', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 0, 1, '2022-08-17 17:05:31', '048657c1-ecb1-4e5d-aced-6126ceae779e', '2022-11-09 09:53:54', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-sendConfig', 1, NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('329633709172986117', '329626608874841989', 2, '消息监控', 'msgCenter.msgMonitor', 'msgCenter/msgMonitor', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 0, 1, '2022-08-17 17:06:43', '048657c1-ecb1-4e5d-aced-6126ceae779e', '2022-11-09 09:50:31', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-msgMonitor', 1, NULL, '309228585019769285', NULL,0);
commit;
-- 添加分级管理 菜单
INSERT INTO `base_module` VALUES ('301261447164357509', '3899D622-578E-4149-B2B4-C8EBD9DA3FDB', 2, '分级管理', 'permission.grade', 'permission/gradeManage', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 52, 1, '2022-05-31 10:05:28', '***************', '2022-11-09 09:48:44', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-gradeManage', 1, NULL, '309228585019769285', NULL,0);
commit;
-- 删除 接口权限 菜单
delete from base_module where  F_FullName='接口权限' ;
commit;
-- 添加 流程引擎 相关菜单
delete from base_module where F_FullName='流程引擎';
delete from base_module where F_FullName='流程表单' and F_EnCode = 'formDesign';
delete from base_module where F_FullName='流程设计' and F_EnCode = 'workFlow.flowEngine';
delete from base_module where F_FullName='流程监控' and F_EnCode = 'workFlow.flowMonitor';
INSERT INTO `base_module` VALUES ('375298373814589189', '-1', '1', '流程引擎', 'flowEngine', '', '0', '0', '0', '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', '6', '1', '2022-12-21 17:21:47', 'admin', '2022-12-21 17:26:00', 'admin', NULL, NULL, NULL, '_self', 'Web', 'ym-custom ym-custom-chemical-weapon', '0', NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('343010259779981061', '375298373814589189', 2, '流程表单', 'formDesign', 'workFlow/formDesign', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '表单设计', 0, 1, '2022-06-30 16:34:20', 'admin', '2022-12-21 17:27:20', 'admin', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-formDesign', 1, NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('fe0150960dd542ec9328067e4495074b', '375298373814589189', 2, '流程设计', 'workFlow.flowEngine', 'workFlow/flowEngine', 1, 1, 1, '{\"iconBackgroundColor\":\"#7B1AE1\",\"moduleId\":\"\"}', NULL, 53, 1, '2018-01-16 14:51:07', '347042038763589', '2022-12-21 17:23:59', 'admin', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-flowDesign', 1, NULL, '309228585019769285', NULL,0);
INSERT INTO `base_module` VALUES ('856131bc6e2b4b16aa79d35ba5234e78', '375298373814589189', 2, '流程监控', 'workFlow.flowMonitor', 'workFlow/flowMonitor', 1, 1, 1, '{\"iconBackgroundColor\":\"\",\"moduleId\":\"\"}', NULL, 59, 1, '2018-01-16 14:50:53', '347042038718533', '2022-12-21 17:24:17', 'admin', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-flowMonitor', 1, NULL, '309228585019769285', NULL,0);
commit;
-- 修改 工作流程 相关菜单
update base_module set F_SortCode = 7 where F_FullName='工作流程' ;
INSERT INTO `base_module` VALUES ('359244360639841925', '5a45f2ff9d0845b2b80bc11f7f7861cf', 2, '新建流程', 'workFlow.addFlow', 'workFlow/flowQuickLaunch', 1, 1, 1, '{\"moduleId\":\"\",\"iconBackgroundColor\":\"\",\"isTree\":0}', '', 54, 1, '2022-11-07 10:08:52', 'admin', '2022-11-09 10:01:50', '***************', NULL, NULL, NULL, '_self', 'Web', 'icon-ym icon-ym-add-flow', 1, NULL, '309228585019769285', NULL,0);
commit;

ALTER TABLE `base_modulebutton` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_ModuleId`;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_ParentId`  varchar(50) NULL DEFAULT NULL COMMENT '按钮上级' AFTER `F_Id`;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '按钮名称' AFTER `F_ParentId`;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '按钮编号' AFTER `F_FullName`;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_Icon`  varchar(50) NULL DEFAULT NULL COMMENT '按钮图标' AFTER `F_EnCode`;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_UrlAddress`  text NULL COMMENT '请求地址' AFTER `F_Icon`;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_PropertyJson`  longtext NULL COMMENT '扩展属性' AFTER `F_UrlAddress`;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_PropertyJson`;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_modulebutton` MODIFY COLUMN `F_ModuleId`  varchar(50) NULL DEFAULT NULL COMMENT '功能主键' AFTER `F_DeleteUserId`;
ALTER TABLE `base_modulecolumn` ADD COLUMN `F_ChildTableKey`  varchar(50) NULL DEFAULT NULL COMMENT '子表规则key' AFTER `F_FieldRule`;
ALTER TABLE `base_modulecolumn` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_ChildTableKey`;
ALTER TABLE `base_modulecolumn` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_modulecolumn` MODIFY COLUMN `F_ParentId`  varchar(50) NULL DEFAULT NULL COMMENT '列表上级' AFTER `F_Id`;
ALTER TABLE `base_modulecolumn` MODIFY COLUMN `F_BindTable`  varchar(50) NULL DEFAULT NULL COMMENT '绑定表格Id' AFTER `F_EnCode`;
ALTER TABLE `base_modulecolumn` MODIFY COLUMN `F_BindTableName`  varchar(50) NULL DEFAULT NULL COMMENT '绑定表格描述' AFTER `F_BindTable`;
ALTER TABLE `base_modulecolumn` MODIFY COLUMN `F_PropertyJson`  longtext NULL COMMENT '扩展属性' AFTER `F_BindTableName`;
ALTER TABLE `base_modulecolumn` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_PropertyJson`;
ALTER TABLE `base_modulecolumn` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_modulecolumn` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_modulecolumn` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_modulecolumn` MODIFY COLUMN `F_ModuleId`  varchar(50) NULL DEFAULT NULL COMMENT '功能主键' AFTER `F_DeleteUserId`;
ALTER TABLE `base_moduledataauthorize` ADD COLUMN `F_ChildTableKey`  varchar(50) NULL DEFAULT NULL COMMENT '子表规则key' AFTER `F_FieldRule`;
ALTER TABLE `base_moduledataauthorize` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_BindTable`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '字段名称' AFTER `F_Id`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '字段编号' AFTER `F_FullName`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_Type`  varchar(50) NULL DEFAULT NULL COMMENT '字段类型' AFTER `F_EnCode`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_ConditionSymbol`  text NULL COMMENT '条件符号' AFTER `F_Type`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_ConditionSymbolJson`  text NULL COMMENT '条件符号Json' AFTER `F_ConditionSymbol`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_ConditionText`  varchar(50) NULL DEFAULT NULL COMMENT '条件内容' AFTER `F_ConditionSymbolJson`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_PropertyJson`  longtext NULL COMMENT '扩展属性' AFTER `F_ConditionText`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_PropertyJson`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_ModuleId`  varchar(50) NULL DEFAULT NULL COMMENT '功能主键' AFTER `F_DeleteUserId`;
ALTER TABLE `base_moduledataauthorize` MODIFY COLUMN `F_BindTable`  varchar(50) NULL DEFAULT NULL COMMENT '绑定表格Id' AFTER `F_ChildTableKey`;
CREATE TABLE `base_moduledataauthorizelink` (
`F_Id`  varchar(50) NOT NULL COMMENT '自然主键' ,
`F_LinkId`  varchar(50) NULL DEFAULT NULL COMMENT '数据源连接' ,
`F_LinkTables`  varchar(400) NULL DEFAULT NULL COMMENT '连接表名' ,
`F_ModuleId`  varchar(50) NULL DEFAULT NULL COMMENT '菜单主键' ,
`F_Type`  varchar(10) NULL DEFAULT NULL COMMENT '权限类型' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
ALTER TABLE `base_moduledataauthorizescheme` ADD COLUMN `F_AllData`  int(2) NULL DEFAULT NULL COMMENT '全部数据标识' AFTER `F_ModuleId`;
ALTER TABLE `base_moduledataauthorizescheme` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_AllData`;
ALTER TABLE `base_moduledataauthorizescheme` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_moduledataauthorizescheme` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '方案编号' AFTER `F_Id`;
ALTER TABLE `base_moduledataauthorizescheme` MODIFY COLUMN `F_FullName`  varchar(100) NULL DEFAULT NULL COMMENT '方案名称' AFTER `F_EnCode`;
ALTER TABLE `base_moduledataauthorizescheme` MODIFY COLUMN `F_ConditionJson`  text NULL COMMENT '条件规则Json' AFTER `F_FullName`;
ALTER TABLE `base_moduledataauthorizescheme` MODIFY COLUMN `F_ConditionText`  text NULL COMMENT '条件规则描述' AFTER `F_ConditionJson`;
ALTER TABLE `base_moduledataauthorizescheme` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_ConditionText`;
ALTER TABLE `base_moduledataauthorizescheme` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_moduledataauthorizescheme` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_moduledataauthorizescheme` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_moduledataauthorizescheme` MODIFY COLUMN `F_ModuleId`  varchar(50) NULL DEFAULT NULL COMMENT '功能主键' AFTER `F_DeleteUserId`;
ALTER TABLE `base_moduleform` ADD COLUMN `F_ChildTableKey`  varchar(50) NULL DEFAULT NULL COMMENT '子表规则key' AFTER `F_FieldRule`;
ALTER TABLE `base_moduleform` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_BindTable`;
ALTER TABLE `base_moduleform` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_moduleform` MODIFY COLUMN `F_ParentId`  varchar(50) NULL DEFAULT NULL COMMENT '表单上级' AFTER `F_Id`;
ALTER TABLE `base_moduleform` MODIFY COLUMN `F_PropertyJson`  longtext NULL COMMENT '扩展属性' AFTER `F_EnCode`;
ALTER TABLE `base_moduleform` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_PropertyJson`;
ALTER TABLE `base_moduleform` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_moduleform` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_moduleform` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_moduleform` MODIFY COLUMN `F_ModuleId`  varchar(50) NULL DEFAULT NULL COMMENT '功能主键' AFTER `F_DeleteUserId`;
ALTER TABLE `base_moduleform` MODIFY COLUMN `F_BindTable`  varchar(50) NULL DEFAULT NULL COMMENT '绑定表格Id' AFTER `F_ChildTableKey`;
ALTER TABLE `base_organize` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_OrganizeIdTree`;
ALTER TABLE `base_organize` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_organize` MODIFY COLUMN `F_ParentId`  varchar(50) NULL DEFAULT NULL COMMENT '机构上级' AFTER `F_Id`;
ALTER TABLE `base_organize` MODIFY COLUMN `F_Category`  varchar(50) NULL DEFAULT NULL COMMENT '机构分类' AFTER `F_ParentId`;
ALTER TABLE `base_organize` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '机构编号' AFTER `F_Category`;
ALTER TABLE `base_organize` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '机构名称' AFTER `F_EnCode`;
ALTER TABLE `base_organize` MODIFY COLUMN `F_ManagerId`  varchar(50) NULL DEFAULT NULL COMMENT '机构主管' AFTER `F_FullName`;
ALTER TABLE `base_organize` MODIFY COLUMN `F_PropertyJson`  longtext NULL COMMENT '扩展属性' AFTER `F_ManagerId`;
ALTER TABLE `base_organize` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_PropertyJson`;
ALTER TABLE `base_organize` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_organize` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_organize` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_organize` MODIFY COLUMN `F_OrganizeIdTree`  longtext NULL COMMENT '父级组织' AFTER `F_DeleteUserId`;
ALTER TABLE `base_organizeadministrator` ADD COLUMN `F_ThisLayerSelect`  int(2) NULL DEFAULT NULL COMMENT '本层查看' AFTER `F_DeleteUserId`;
ALTER TABLE `base_organizeadministrator` ADD COLUMN `F_SubLayerSelect`  int(2) NULL DEFAULT NULL COMMENT '子层查看' AFTER `F_ThisLayerSelect`;
ALTER TABLE `base_organizeadministrator` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_SubLayerSelect`;
ALTER TABLE `base_organizeadministrator` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_organizeadministrator` MODIFY COLUMN `F_UserId`  longtext NULL COMMENT '用户主键' AFTER `F_Id`;
ALTER TABLE `base_organizeadministrator` MODIFY COLUMN `F_OrganizeId`  varchar(50) NULL DEFAULT NULL COMMENT '机构主键' AFTER `F_UserId`;
ALTER TABLE `base_organizeadministrator` MODIFY COLUMN `F_OrganizeType`  varchar(50) NULL DEFAULT NULL COMMENT '机构类型' AFTER `F_OrganizeId`;
ALTER TABLE `base_organizeadministrator` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_SubLayerDelete`;
ALTER TABLE `base_organizeadministrator` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_organizeadministrator` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_organizeadministrator` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_organize_relation` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_Creator_User_Id`;
ALTER TABLE `base_organize_relation` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_organize_relation` MODIFY COLUMN `F_Organize_Id`  varchar(50) NULL DEFAULT NULL COMMENT '组织主键' AFTER `F_Id`;
ALTER TABLE `base_organize_relation` MODIFY COLUMN `F_Object_Type`  varchar(50) NULL DEFAULT NULL COMMENT '对象类型（角色：role）' AFTER `F_Organize_Id`;
ALTER TABLE `base_organize_relation` MODIFY COLUMN `F_Object_Id`  varchar(50) NULL DEFAULT NULL COMMENT '对象主键' AFTER `F_Object_Type`;
ALTER TABLE `base_organize_relation` MODIFY COLUMN `F_Creator_User_Id`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_Creator_Time`;

ALTER TABLE `base_portal` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_LinkType`;
ALTER TABLE `base_portal` ADD COLUMN `F_EnabledLock`  int(11) NULL DEFAULT NULL COMMENT '锁定（0-锁定，1-自定义）' AFTER `F_TenantId`;
ALTER TABLE `base_portal` ADD COLUMN `F_ParentId`  varchar(50) NULL DEFAULT NULL COMMENT '父级id' AFTER `F_EnabledLock`;
ALTER TABLE `base_portal` ADD COLUMN `F_Platform`  varchar(50) NULL DEFAULT NULL COMMENT 'PC:网页端 APP:手机端 MOD:模板' AFTER `F_ParentId`;
-- 旧的门户设计适配新的表 门户数据需手动重新配置
update base_portal set F_EnabledLock = 1 where F_EnabledLock is null;
-- insert into base_portal_data
-- select uuid() as F_Id,F_Id as F_PortalId,null as F_Platform,F_FormData as F_FormData,null as F_System_Id,'model' as F_Type,F_CreatorUserId,F_CreatorTime,F_LastModifyUserId,F_LastModifyTime,F_DeleteUserId,F_DeleteMark,F_DeleteTime,F_TenantId from base_portal;
-- commit;

ALTER TABLE `base_portal` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '主键' FIRST ;
ALTER TABLE `base_portal` MODIFY COLUMN `F_Description`  longtext NULL COMMENT '描述' AFTER `F_Id`;
ALTER TABLE `base_portal` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_portal` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_portal` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_portal` MODIFY COLUMN `F_FullName`  varchar(100) NULL DEFAULT NULL COMMENT '名称' AFTER `F_DeleteUserId`;
ALTER TABLE `base_portal` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '编码' AFTER `F_FullName`;
ALTER TABLE `base_portal` MODIFY COLUMN `F_Category`  varchar(50) NULL DEFAULT NULL COMMENT '分类（数据字典）' AFTER `F_EnCode`;
ALTER TABLE `base_portal` MODIFY COLUMN `F_FormData`  longtext NULL COMMENT '表单配置JSON' AFTER `F_Category`;
ALTER TABLE `base_portal` MODIFY COLUMN `F_CustomUrl`  varchar(1000) NULL DEFAULT NULL COMMENT '静态页面路径' AFTER `F_Type`;
CREATE TABLE `base_portal_data` (
`F_Id`  varchar(50) NOT NULL COMMENT 'ID' ,
`F_PortalId`  varchar(50) NOT NULL COMMENT '门户ID' ,
`F_Platform`  varchar(50) NULL DEFAULT NULL COMMENT 'PC:网页端 APP:手机端 ' ,
`F_FormData`  longtext NULL COMMENT '表单配置JSON' ,
`F_System_Id`  varchar(50) NULL DEFAULT NULL COMMENT '系统ID' ,
`F_Type`  varchar(255) NOT NULL COMMENT '类型（mod：模型、custom：自定义）' ,
`F_CreatorUserId`  varchar(50) NOT NULL COMMENT '创建用户' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_portal_manage` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键_id' ,
`F_Description`  varchar(50) NULL DEFAULT NULL COMMENT '说明' ,
`F_Portal_Id`  varchar(50) NOT NULL COMMENT '门户_id' ,
`F_System_Id`  varchar(50) NOT NULL COMMENT '系统_id' ,
`F_SortCode`  bigint(20) NOT NULL COMMENT '排序码' ,
`F_EnabledMark`  int(11) NOT NULL COMMENT '有效标志' ,
`F_CreatorTime`  datetime NOT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NOT NULL COMMENT '创建用户_id' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户_id' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户_id' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户_id' ,
`F_Home_Page_Mark`  varchar(255) NULL DEFAULT NULL ,
`F_Platform`  varchar(255) NULL DEFAULT NULL COMMENT '平台' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
ALTER TABLE `base_position` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_OrganizeId`;
ALTER TABLE `base_position` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_position` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '岗位名称' AFTER `F_Id`;
ALTER TABLE `base_position` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '岗位编号' AFTER `F_FullName`;
ALTER TABLE `base_position` MODIFY COLUMN `F_Type`  varchar(50) NULL DEFAULT NULL COMMENT '岗位类型' AFTER `F_EnCode`;
ALTER TABLE `base_position` MODIFY COLUMN `F_PropertyJson`  longtext NULL COMMENT '扩展属性' AFTER `F_Type`;
ALTER TABLE `base_position` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_PropertyJson`;
ALTER TABLE `base_position` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_position` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_position` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_position` MODIFY COLUMN `F_OrganizeId`  varchar(50) NULL DEFAULT NULL COMMENT '机构主键' AFTER `F_DeleteUserId`;
ALTER TABLE `base_printdev` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_PrintTemplate`;
ALTER TABLE `base_printdev` ADD COLUMN `F_PageParam`  text NULL COMMENT '纸张参数' AFTER `F_TenantId`;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '主键_id' FIRST ;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_FullName`  varchar(50) NOT NULL COMMENT '名称' AFTER `F_Id`;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_Encode`  varchar(50) NOT NULL COMMENT '编码' AFTER `F_FullName`;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_Category`  varchar(50) NOT NULL COMMENT '分类' AFTER `F_Encode`;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_Description`  varchar(50) NULL DEFAULT NULL COMMENT '描述' AFTER `F_Type`;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NOT NULL COMMENT '创建用户_id' AFTER `F_CreatorTime`;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户_id' AFTER `F_LastModifyTime`;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户_id' AFTER `F_DeleteTime`;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_DbLinkId`  varchar(50) NOT NULL COMMENT '连接数据 _id' AFTER `F_DeleteUserId`;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_SqlTemplate`  text NULL COMMENT 'sql语句' AFTER `F_DbLinkId`;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_LeftFields`  text NULL COMMENT '左侧字段' AFTER `F_SqlTemplate`;
ALTER TABLE `base_printdev` MODIFY COLUMN `F_PrintTemplate`  text NOT NULL COMMENT '打印模板' AFTER `F_LeftFields`;
CREATE TABLE `base_print_log` (
`F_Id`  varchar(50) NOT NULL ,
`F_PrintMan`  varchar(50) NULL DEFAULT NULL COMMENT '打印人' ,
`F_PrintTime`  datetime NULL DEFAULT NULL COMMENT '打印时间' ,
`F_PrintNum`  int(3) NULL DEFAULT NULL COMMENT '打印条数' ,
`F_PrintTitle`  varchar(255) NULL DEFAULT NULL COMMENT '打印功能名称' ,
`F_PrintId`  varchar(30) NULL DEFAULT NULL COMMENT '基于哪一个模板' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
ALTER TABLE `base_province` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_DeleteUserId`;
ALTER TABLE `base_province` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_province` MODIFY COLUMN `F_ParentId`  varchar(50) NULL DEFAULT NULL COMMENT '区域上级' AFTER `F_Id`;
ALTER TABLE `base_province` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '区域编号' AFTER `F_ParentId`;
ALTER TABLE `base_province` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '区域名称' AFTER `F_EnCode`;
ALTER TABLE `base_province` MODIFY COLUMN `F_QuickQuery`  varchar(50) NULL DEFAULT NULL COMMENT '快速查询' AFTER `F_FullName`;
ALTER TABLE `base_province` MODIFY COLUMN `F_Type`  varchar(50) NULL DEFAULT NULL COMMENT '区域类型' AFTER `F_QuickQuery`;
ALTER TABLE `base_province` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_Type`;
ALTER TABLE `base_province` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_province` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_province` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
CREATE TABLE `base_province_atlas` (
`F_Id`  varchar(50) NOT NULL COMMENT '自然主键' ,
`F_ParentId`  varchar(50) NULL DEFAULT NULL COMMENT '区域上级' ,
`F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '区域编号' ,
`F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '区域名称' ,
`F_QuickQuery`  varchar(50) NULL DEFAULT NULL COMMENT '快速查询' ,
`F_Type`  varchar(50) NULL DEFAULT NULL COMMENT '区域类型' ,
`F_Description`  text NULL COMMENT '描述' ,
`F_SortCode`  bigint(20) NULL DEFAULT NULL COMMENT '排序' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '有效标志' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
`F_DivisionCode`  varchar(50) NULL DEFAULT NULL COMMENT '行政区划编码' ,
`F_AtlasCenter`  varchar(128) NULL DEFAULT NULL COMMENT '中心经纬度' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
ALTER TABLE `base_role` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_Global_Mark`;
ALTER TABLE `base_role` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_role` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '角色名称' AFTER `F_Id`;
ALTER TABLE `base_role` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '角色编号' AFTER `F_FullName`;
ALTER TABLE `base_role` MODIFY COLUMN `F_Type`  varchar(50) NULL DEFAULT NULL COMMENT '角色类型' AFTER `F_EnCode`;
ALTER TABLE `base_role` MODIFY COLUMN `F_PropertyJson`  longtext NULL COMMENT '扩展属性' AFTER `F_Type`;
ALTER TABLE `base_role` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_PropertyJson`;
ALTER TABLE `base_role` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_role` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_role` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_role` MODIFY COLUMN `F_Global_Mark`  tinyint(5) NOT NULL COMMENT '全局标识' AFTER `F_DeleteUserId`;
CREATE TABLE `base_schedule` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键' ,
`F_Type`  varchar(255) NULL DEFAULT NULL COMMENT '类型' ,
`F_Urgent`  varchar(50) NULL DEFAULT NULL COMMENT '紧急程度' ,
`F_Title`  varchar(255) NULL DEFAULT NULL COMMENT '标题' ,
`F_Content`  text NULL COMMENT '内容' ,
`F_AllDay`  int(11) NULL DEFAULT NULL COMMENT '全天' ,
`F_StartDay`  datetime NULL DEFAULT NULL COMMENT '开始时间' ,
`F_StartTime`  varchar(50) NULL DEFAULT NULL COMMENT '开始日期' ,
`F_EndDay`  datetime NULL DEFAULT NULL COMMENT '结束时间' ,
`F_EndTime`  varchar(50) NULL DEFAULT NULL COMMENT '结束日期' ,
`F_Duration`  int(11) NULL DEFAULT NULL COMMENT '时长' ,
`F_Color`  varchar(255) NULL DEFAULT NULL COMMENT '颜色' ,
`F_ReminderTime`  int(11) NULL DEFAULT NULL COMMENT '提醒' ,
`F_ReminderType`  varchar(255) NULL DEFAULT NULL COMMENT '提醒方式' ,
`F_SortCode`  bigint(20) NULL DEFAULT NULL COMMENT '排序' ,
`F_Send`  varchar(255) NULL DEFAULT NULL COMMENT '发送配置' ,
`F_SendName`  varchar(255) NULL DEFAULT NULL COMMENT '发送配置' ,
`F_Repetition`  varchar(255) NULL DEFAULT NULL COMMENT '重复提醒' ,
`F_RepeatTime`  datetime NULL DEFAULT NULL COMMENT '结束重复' ,
`F_PushTime`  datetime NULL DEFAULT NULL COMMENT '推送时间' ,
`F_GroupId`  varchar(50) NULL DEFAULT NULL COMMENT '分组id' ,
`F_Description`  text NULL COMMENT '描述' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '有效标志' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_schedule_log` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键' ,
`F_Type`  varchar(255) NULL DEFAULT NULL COMMENT '类型' ,
`F_Urgent`  varchar(50) NULL DEFAULT NULL COMMENT '紧急程度' ,
`F_Title`  varchar(255) NULL DEFAULT NULL COMMENT '标题' ,
`F_Content`  text NULL COMMENT '内容' ,
`F_AllDay`  int(11) NULL DEFAULT NULL COMMENT '全天' ,
`F_StartDay`  datetime NULL DEFAULT NULL COMMENT '开始时间' ,
`F_StartTime`  varchar(50) NULL DEFAULT NULL COMMENT '开始日期' ,
`F_EndDay`  datetime NULL DEFAULT NULL COMMENT '结束时间' ,
`F_EndTime`  varchar(50) NULL DEFAULT NULL COMMENT '结束日期' ,
`F_Duration`  int(11) NULL DEFAULT NULL COMMENT '时长' ,
`F_Color`  varchar(255) NULL DEFAULT NULL COMMENT '颜色' ,
`F_ReminderTime`  int(11) NULL DEFAULT NULL COMMENT '提醒' ,
`F_ReminderType`  varchar(255) NULL DEFAULT NULL COMMENT '提醒方式' ,
`F_SortCode`  bigint(20) NULL DEFAULT NULL COMMENT '排序' ,
`F_Send`  varchar(255) NULL DEFAULT NULL COMMENT '发送配置' ,
`F_SendName`  varchar(255) NULL DEFAULT NULL COMMENT '发送配置' ,
`F_Repetition`  varchar(255) NULL DEFAULT NULL COMMENT '重复提醒' ,
`F_RepeatTime`  datetime NULL DEFAULT NULL COMMENT '结束重复' ,
`F_PushTime`  datetime NULL DEFAULT NULL COMMENT '推送时间' ,
`F_GroupId`  varchar(50) NULL DEFAULT NULL COMMENT '分组id' ,
`F_Description`  text NULL COMMENT '描述' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '有效标志' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
`F_OperationType`  varchar(255) NULL DEFAULT NULL COMMENT '操作类型' ,
`F_UserId`  text NULL COMMENT '参与用户' ,
`F_ScheduleId`  varchar(50) NULL DEFAULT NULL COMMENT '日程id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_schedule_user` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键' ,
`F_ScheduleId`  varchar(50) NULL DEFAULT NULL COMMENT '日程id' ,
`F_ToUserIds`  varchar(50) NULL DEFAULT NULL COMMENT '用户id' ,
`F_SortCode`  bigint(20) NULL DEFAULT NULL COMMENT '排序' ,
`F_Description`  text NULL COMMENT '描述' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '有效标志' ,
`F_Type`  varchar(50) NULL DEFAULT NULL COMMENT '类型' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_signimg` (
`F_Id`  varchar(50) NOT NULL COMMENT '自然主键' ,
`F_SignImg`  longtext NULL COMMENT '签名图片' ,
`F_IsDefault`  int(11) NULL DEFAULT NULL COMMENT '是否默认' ,
`F_Description`  longtext NULL COMMENT '描述' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '有效标志' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
ALTER TABLE `base_sms_template` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_Region`;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_AppId`  varchar(50) NULL DEFAULT NULL COMMENT '应用编号' AFTER `F_Company`;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_SignContent`  varchar(50) NULL DEFAULT NULL COMMENT '签名内容' AFTER `F_AppId`;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_TemplateId`  varchar(50) NULL DEFAULT NULL COMMENT '模板编号' AFTER `F_SignContent`;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '模板名称' AFTER `F_TemplateId`;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_TemplateJson`  varchar(255) NULL DEFAULT NULL COMMENT '模板参数JSON' AFTER `F_FullName`;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '模板编码' AFTER `F_DeleteUserId`;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_Endpoint`  varchar(50) NULL DEFAULT NULL COMMENT 'Endpoint' AFTER `F_EnCode`;
ALTER TABLE `base_sms_template` MODIFY COLUMN `F_Region`  varchar(50) NULL DEFAULT NULL COMMENT 'region' AFTER `F_Endpoint`;
CREATE TABLE `base_socialsusersentity` (
`F_Id`  varchar(50) NOT NULL COMMENT '自然主键' ,
`F_UserId`  varchar(50) NULL DEFAULT NULL COMMENT '用户id' ,
`F_SocialType`  varchar(50) NULL DEFAULT NULL COMMENT '第三方类型' ,
`F_SocialId`  varchar(100) NULL DEFAULT NULL COMMENT '第三方账号id' ,
`F_SocialName`  varchar(100) NULL DEFAULT NULL COMMENT '第三方账号' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_Description`  varchar(100) NULL DEFAULT NULL COMMENT '备注' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标记' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
ALTER TABLE `base_synthirdinfo` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_LastModifyUserId`;
ALTER TABLE `base_synthirdinfo` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_synthirdinfo` MODIFY COLUMN `F_SysObjId`  varchar(50) NULL DEFAULT NULL COMMENT '系统对象ID' AFTER `F_DataType`;
ALTER TABLE `base_synthirdinfo` MODIFY COLUMN `F_ThirdObjId`  varchar(50) NULL DEFAULT NULL COMMENT '第三对象ID' AFTER `F_SysObjId`;
ALTER TABLE `base_synthirdinfo` MODIFY COLUMN `F_Description`  text NULL COMMENT '备注' AFTER `F_SynState`;
ALTER TABLE `base_synthirdinfo` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建人' AFTER `F_CreatorTime`;
ALTER TABLE `base_synthirdinfo` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改人' AFTER `F_LastModifyTime`;
ALTER TABLE `base_sysconfig` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_Category`;
ALTER TABLE `base_sysconfig` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_sysconfig` MODIFY COLUMN `F_Name`  varchar(50) NULL DEFAULT NULL COMMENT '名称' AFTER `F_Id`;
ALTER TABLE `base_sysconfig` MODIFY COLUMN `F_Key`  varchar(50) NULL DEFAULT NULL COMMENT '键' AFTER `F_Name`;
ALTER TABLE `base_sysconfig` MODIFY COLUMN `F_Value`  longtext NULL COMMENT '值' AFTER `F_Key`;
ALTER TABLE `base_sysconfig` MODIFY COLUMN `F_Category`  varchar(50) NULL DEFAULT NULL COMMENT '分类' AFTER `F_Value`;
ALTER TABLE `base_sysconfig` MODIFY COLUMN `F_Remark`  varchar(255) NULL DEFAULT NULL COMMENT '备注' AFTER `F_TenantId`;
-- 添加 新版本的配置信息
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************25', NULL, 'copyright', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************26', NULL, 'lastLoginTimeSwitch', '0', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************27', NULL, 'companyName', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************28', NULL, 'isLog', '1', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************29', NULL, 'dingSynIsSynOrg', '0', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************30', NULL, 'emailPassword', '333', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************31', NULL, 'pageSize', '30', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************32', NULL, 'lockType', '1', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************33', NULL, 'smsCompany', '2', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************34', NULL, 'qyhIsSynUser', '0', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************35', NULL, 'companyTelePhone', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************36', NULL, 'dingAgentId', '1175831637', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************37', NULL, 'sysDescription', 'XH快速开发平台可以帮助你们解决这一切。XH快速开发平台是一款配置型软件快速开发框架，一次开发，同时生成时生成桌面端和移动端。', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************38', NULL, 'companyEmail', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************39', NULL, 'qyhCorpId', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************40', NULL, 'lockTime', '10', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************41', NULL, 'smsKeySecret', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************42', NULL, 'emailAccount', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************43', NULL, 'emailSmtpHost', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************44', NULL, 'qyhAgentId', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************45', NULL, 'sysName', 'XH快速开发平台', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************46', NULL, 'dingSynAppSecret', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************47', NULL, 'emailSenderName', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************48', NULL, 'emailPop3Port', '110', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************49', NULL, 'companyCode', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************50', NULL, 'qyhCorpSecret', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************51', NULL, 'sysVersion', 'V2.0.0', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************52', NULL, 'dingSynIsSynUser', '0', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************53', NULL, 'qyhAgentSecret', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************54', NULL, 'registerKey', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************55', NULL, 'enableVerificationCode', '0', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************56', NULL, 'tokenTimeout', '900', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************57', NULL, 'whiteListIp', '***********,***********,***********,***********,***********,***********', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************58', NULL, 'whitelistSwitch', '0', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************59', NULL, 'companyContacts', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************60', NULL, 'passwordErrorsNumber', '3', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('419818550929165547', NULL, 'title', 'XH快速开发平台', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************61', NULL, 'verificationCodeNumber', '3', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************62', NULL, 'lastLoginTime', '1', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************63', NULL, 'qyhIsSynOrg', '0', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************64', NULL, 'emailSmtpPort', '587', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************65', NULL, 'companyAddress', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************66', NULL, 'domain', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************67', NULL, 'singleLogin', '1', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************68', NULL, 'dingSynAppKey', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************69', NULL, 'emailPop3Host', 'pop3.sina.com.cn', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************70', NULL, 'sysTheme', '1', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************71', NULL, 'region', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('****************72', NULL, 'smsKeyId', '', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506888', NULL, 'mandatoryModificationOfInitialPassword', '0', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506889', NULL, 'passwordLengthMinNumber', '6', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506894', NULL, 'containsNumbers', '1', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506905', NULL, 'passwordLengthMin', '1', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506906', NULL, 'disableOldPassword', '1', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506909', NULL, 'includeUppercaseLetters', '1', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506912', NULL, 'updateInAdvance', '2', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506918', NULL, 'disableTheNumberOfOldPasswords', '3', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506926', NULL, 'passwordStrengthLimit', '1', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506930', NULL, 'updateCycle', '2', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506932', NULL, 'passwordIsUpdatedRegularly', '0', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878773506943', NULL, 'includeLowercaseLetters', '1', 'SysConfig', NULL);
INSERT INTO `base_sysconfig`(`F_Id`,`F_Name`,`F_Key`,`F_Value`,`F_Category`,`F_TenantId`) VALUES ('392332878777701191', NULL, 'containsCharacters', '1', 'SysConfig', NULL);
commit;


ALTER TABLE `base_syslog` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_ObjectId`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_UserId`  varchar(50) NULL DEFAULT NULL COMMENT '用户主键' AFTER `F_Id`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_UserName`  varchar(100) NULL DEFAULT NULL COMMENT '用户主键' AFTER `F_UserId`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_IPAddress`  varchar(50) NULL DEFAULT NULL COMMENT 'IP地址' AFTER `F_Level`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_IPAddressName`  varchar(50) NULL DEFAULT NULL COMMENT 'IP所在城市' AFTER `F_IPAddress`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_RequestURL`  text NULL COMMENT '请求地址' AFTER `F_IPAddressName`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_RequestMethod`  varchar(50) NULL DEFAULT NULL COMMENT '请求方法' AFTER `F_RequestURL`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_Abstracts`  text NULL COMMENT '日志摘要' AFTER `F_RequestDuration`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_Json`  longtext NULL COMMENT '日志内容' AFTER `F_Abstracts`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_PlatForm`  text NULL COMMENT '平台设备' AFTER `F_Json`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_ModuleId`  varchar(50) NULL DEFAULT NULL COMMENT '功能主键' AFTER `F_CreatorTime`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_ModuleName`  varchar(50) NULL DEFAULT NULL COMMENT '功能名称' AFTER `F_ModuleId`;
ALTER TABLE `base_syslog` MODIFY COLUMN `F_ObjectId`  varchar(50) NULL DEFAULT NULL COMMENT '对象Id' AFTER `F_ModuleName`;
CREATE TABLE `base_system` (
`F_Id`  varchar(50) NOT NULL COMMENT '自然主键' ,
`F_FullName`  varchar(50) NULL DEFAULT NULL COMMENT '系统名称' ,
`F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '系统编号' ,
`F_Icon`  varchar(200) NULL DEFAULT NULL COMMENT '系统图标' ,
`F_IsMain`  int(11) NULL DEFAULT NULL COMMENT '是否是主系统（0-不是，1-是）' ,
`F_PropertyJson`  longtext NULL COMMENT '扩展属性' ,
`F_Description`  longtext NULL COMMENT '描述' ,
`F_SortCode`  bigint(20) NULL DEFAULT NULL COMMENT '排序' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '有效标志' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
INSERT INTO `base_system` VALUES ('309228585019769285', '开发平台', 'mainSystem', 'icon-ym icon-ym-signature', 1, NULL, '主系统', 0, 1, '2022-06-22 09:44:47', '***************', NULL, NULL, NULL, NULL, NULL, NULL);

ALTER TABLE `base_timetask` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_DeleteUserId`;
ALTER TABLE `base_timetask` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_timetask` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '任务编码' AFTER `F_Id`;
ALTER TABLE `base_timetask` MODIFY COLUMN `F_FullName`  varchar(100) NULL DEFAULT NULL COMMENT '任务名称' AFTER `F_EnCode`;
ALTER TABLE `base_timetask` MODIFY COLUMN `F_ExecuteType`  varchar(50) NULL DEFAULT NULL COMMENT '执行类型' AFTER `F_FullName`;
ALTER TABLE `base_timetask` MODIFY COLUMN `F_ExecuteContent`  longtext NULL COMMENT '执行内容' AFTER `F_ExecuteType`;
ALTER TABLE `base_timetask` MODIFY COLUMN `F_ExecuteCycleJson`  longtext NULL COMMENT '执行周期' AFTER `F_ExecuteContent`;
ALTER TABLE `base_timetask` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_RunCount`;
ALTER TABLE `base_timetask` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_timetask` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_timetask` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;

ALTER TABLE `base_user` ADD COLUMN `F_SystemId`  varchar(50) NULL DEFAULT NULL COMMENT '系统id' AFTER `F_GroupId`;
ALTER TABLE `base_user` ADD COLUMN `F_AppSystemId`  varchar(255) NULL DEFAULT NULL COMMENT 'App系统id' AFTER `F_SystemId`;
ALTER TABLE `base_user` ADD COLUMN `F_DingJobNumber`  varchar(50) NULL DEFAULT NULL COMMENT '钉钉工号' AFTER `F_AppSystemId`;
ALTER TABLE `base_user` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_DingJobNumber`;
ALTER TABLE `base_user` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_user` MODIFY COLUMN `F_Account`  varchar(50) NULL DEFAULT NULL COMMENT '账户' AFTER `F_Id`;
ALTER TABLE `base_user` MODIFY COLUMN `F_RealName`  varchar(50) NULL DEFAULT NULL COMMENT '姓名' AFTER `F_Account`;
ALTER TABLE `base_user` MODIFY COLUMN `F_QuickQuery`  varchar(100) NULL DEFAULT NULL COMMENT '快速查询' AFTER `F_RealName`;
ALTER TABLE `base_user` MODIFY COLUMN `F_NickName`  varchar(50) NULL DEFAULT NULL COMMENT '呢称' AFTER `F_QuickQuery`;
ALTER TABLE `base_user` MODIFY COLUMN `F_HeadIcon`  text NULL COMMENT '头像' AFTER `F_NickName`;
ALTER TABLE `base_user` MODIFY COLUMN `F_MobilePhone`  varchar(20) NULL DEFAULT NULL COMMENT '手机' AFTER `F_Birthday`;
ALTER TABLE `base_user` MODIFY COLUMN `F_TelePhone`  varchar(20) NULL DEFAULT NULL COMMENT '电话' AFTER `F_MobilePhone`;
ALTER TABLE `base_user` MODIFY COLUMN `F_Landline`  varchar(50) NULL DEFAULT NULL COMMENT 'F_Landline' AFTER `F_TelePhone`;
ALTER TABLE `base_user` MODIFY COLUMN `F_Email`  varchar(50) NULL DEFAULT NULL COMMENT '邮箱' AFTER `F_Landline`;
ALTER TABLE `base_user` MODIFY COLUMN `F_Nation`  varchar(50) NULL DEFAULT NULL COMMENT '民族' AFTER `F_Email`;
ALTER TABLE `base_user` MODIFY COLUMN `F_NativePlace`  varchar(50) NULL DEFAULT NULL COMMENT '籍贯' AFTER `F_Nation`;
ALTER TABLE `base_user` MODIFY COLUMN `F_CertificatesType`  varchar(50) NULL DEFAULT NULL COMMENT '证件类型' AFTER `F_EntryDate`;
ALTER TABLE `base_user` MODIFY COLUMN `F_CertificatesNumber`  varchar(50) NULL DEFAULT NULL COMMENT '证件号码' AFTER `F_CertificatesType`;
ALTER TABLE `base_user` MODIFY COLUMN `F_Education`  varchar(50) NULL DEFAULT NULL COMMENT '文化程度' AFTER `F_CertificatesNumber`;
ALTER TABLE `base_user` MODIFY COLUMN `F_UrgentContacts`  varchar(50) NULL DEFAULT NULL COMMENT 'F_UrgentContacts' AFTER `F_Education`;
ALTER TABLE `base_user` MODIFY COLUMN `F_UrgentTelePhone`  varchar(50) NULL DEFAULT NULL COMMENT '紧急电话' AFTER `F_UrgentContacts`;
ALTER TABLE `base_user` MODIFY COLUMN `F_PostalAddress`  text NULL COMMENT '通讯地址' AFTER `F_UrgentTelePhone`;
ALTER TABLE `base_user` MODIFY COLUMN `F_Signature`  text NULL COMMENT '自我介绍' AFTER `F_PostalAddress`;
ALTER TABLE `base_user` MODIFY COLUMN `F_Password`  varchar(50) NULL DEFAULT NULL COMMENT '密码' AFTER `F_Signature`;
ALTER TABLE `base_user` MODIFY COLUMN `F_Secretkey`  varchar(50) NULL DEFAULT NULL COMMENT '秘钥' AFTER `F_Password`;
ALTER TABLE `base_user` MODIFY COLUMN `F_FirstLogIP`  varchar(50) NULL DEFAULT NULL COMMENT '首次登录IP' AFTER `F_FirstLogTime`;
ALTER TABLE `base_user` MODIFY COLUMN `F_PrevLogIP`  varchar(50) NULL DEFAULT NULL COMMENT '前次登录IP' AFTER `F_PrevLogTime`;
ALTER TABLE `base_user` MODIFY COLUMN `F_LastLogIP`  varchar(50) NULL DEFAULT NULL COMMENT '最后登录IP' AFTER `F_LastLogTime`;
ALTER TABLE `base_user` MODIFY COLUMN `F_Language`  varchar(50) NULL DEFAULT NULL COMMENT '系统语言' AFTER `F_ChangePasswordDate`;
ALTER TABLE `base_user` MODIFY COLUMN `F_Theme`  varchar(50) NULL DEFAULT NULL COMMENT '系统样式' AFTER `F_Language`;
ALTER TABLE `base_user` MODIFY COLUMN `F_CommonMenu`  longtext NULL COMMENT '常用菜单' AFTER `F_Theme`;
ALTER TABLE `base_user` MODIFY COLUMN `F_PropertyJson`  longtext NULL COMMENT '扩展属性' AFTER `F_IsAdministrator`;
ALTER TABLE `base_user` MODIFY COLUMN `F_Description`  text NULL COMMENT '描述' AFTER `F_PropertyJson`;
ALTER TABLE `base_user` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_user` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_user` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_user` MODIFY COLUMN `F_ManagerId`  varchar(50) NULL DEFAULT NULL COMMENT '主管主键' AFTER `F_DeleteMark`;
ALTER TABLE `base_user` MODIFY COLUMN `F_OrganizeId`  text NULL COMMENT '组织主键' AFTER `F_ManagerId`;
ALTER TABLE `base_user` MODIFY COLUMN `F_PositionId`  text NULL COMMENT '岗位主键' AFTER `F_OrganizeId`;
ALTER TABLE `base_user` MODIFY COLUMN `F_RoleId`  text NULL COMMENT '角色主键' AFTER `F_PositionId`;
ALTER TABLE `base_user` MODIFY COLUMN `F_PortalId`  text NULL COMMENT '门户主键' AFTER `F_RoleId`;
ALTER TABLE `base_user` MODIFY COLUMN `F_GroupId`  varchar(50) NULL DEFAULT NULL COMMENT '分组id' AFTER `F_UnlockTime`;

update base_user set F_SystemId='309228585019769285' where F_SystemId is null;
update base_user set F_AppSystemId='309228585019769285' where F_AppSystemId is null;
commit;

ALTER TABLE `base_userrelation` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_CreatorUserId`;
ALTER TABLE `base_userrelation` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '自然主键' FIRST ;
ALTER TABLE `base_userrelation` MODIFY COLUMN `F_UserId`  varchar(50) NULL DEFAULT NULL COMMENT '用户主键' AFTER `F_Id`;
ALTER TABLE `base_userrelation` MODIFY COLUMN `F_ObjectType`  varchar(50) NULL DEFAULT NULL COMMENT '对象类型' AFTER `F_UserId`;
ALTER TABLE `base_userrelation` MODIFY COLUMN `F_ObjectId`  varchar(50) NULL DEFAULT NULL COMMENT '对象主键' AFTER `F_ObjectType`;
ALTER TABLE `base_userrelation` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
CREATE TABLE `base_user_device` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键id' ,
`F_ClientId`  varchar(50) NULL DEFAULT NULL COMMENT '设备id' ,
`F_UserId`  varchar(50) NULL DEFAULT NULL COMMENT '用户id' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_DeleteMark`  int(2) NULL DEFAULT NULL COMMENT '删除标识' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_EnabledMark`  int(2) NULL DEFAULT NULL COMMENT '状态' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_user_old_password` (
`F_Id`  varchar(50) NOT NULL COMMENT '自然主键' ,
`F_UserId`  varchar(50) NULL DEFAULT NULL COMMENT 'userid' ,
`F_Account`  varchar(50) NULL DEFAULT NULL COMMENT '账户' ,
`F_OldPassword`  varchar(50) NULL DEFAULT NULL COMMENT '旧密码' ,
`F_Secretkey`  varchar(50) NULL DEFAULT NULL COMMENT '秘钥' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_TenantId`  varchar(100) NULL DEFAULT NULL COMMENT '租户id' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
ALTER TABLE `base_visualdev` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_AppColumnData`;
ALTER TABLE `base_visualdev` ADD COLUMN `F_EnableFlow`  int(2) NULL DEFAULT NULL COMMENT '启用流程' AFTER `F_TenantId`;
ALTER TABLE `base_visualdev` ADD COLUMN `F_InterfaceId`  varchar(50) NULL DEFAULT NULL COMMENT '接口id' AFTER `F_EnableFlow`;
ALTER TABLE `base_visualdev` ADD COLUMN `F_InterfaceName`  varchar(100) NULL DEFAULT NULL COMMENT '接口名称' AFTER `F_InterfaceId`;
ALTER TABLE `base_visualdev` ADD COLUMN `F_InterfaceParam`  longtext NULL COMMENT '接口参数' AFTER `F_InterfaceName`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '主键' FIRST ;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_Description`  longtext NULL COMMENT '描述' AFTER `F_Id`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_SortCode`  bigint(20) NULL DEFAULT NULL COMMENT '排序码' AFTER `F_Description`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '有效标志' AFTER `F_SortCode`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' AFTER `F_EnabledMark`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' AFTER `F_CreatorUserId`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' AFTER `F_LastModifyUserId`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' AFTER `F_DeleteMark`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_FullName`  varchar(100) NULL DEFAULT NULL COMMENT '名称' AFTER `F_DeleteUserId`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '编码' AFTER `F_FullName`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_Table`  longtext NULL COMMENT '关联的表' AFTER `F_Type`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_Category`  varchar(50) NULL DEFAULT NULL COMMENT '分类（数据字典）' AFTER `F_Table`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_FormData`  longtext NULL COMMENT '表单配置JSON' AFTER `F_Category`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_ColumnData`  longtext NULL COMMENT '列表配置JSON' AFTER `F_FormData`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_Fields`  longtext NULL COMMENT '功能字段JSON' AFTER `F_ColumnData`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_TemplateData`  longtext NULL COMMENT '前端模板JSON' AFTER `F_Fields`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_DbLinkId`  varchar(50) NULL DEFAULT NULL COMMENT '关联数据连接id' AFTER `F_TemplateData`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_FlowTemplateJson`  longtext NULL COMMENT '工作流模板JSON' AFTER `F_DbLinkId`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_FlowId`  varchar(50) NULL DEFAULT NULL COMMENT '关联工作流连接id' AFTER `F_WebType`;
ALTER TABLE `base_visualdev` MODIFY COLUMN `F_AppColumnData`  longtext NULL COMMENT 'app列表配置JSON' AFTER `F_FlowId`;
-- 原来流程类型的在线表单，是否启用流程都配置为1
update base_visualdev set F_EnableFlow=1 where F_WebType=3 and F_EnableFlow is null;
commit;
update base_visualdev set F_EnableFlow=0 where  F_EnableFlow is null;

ALTER TABLE `base_visualdev_modeldata` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `F_Data`;
ALTER TABLE `base_visualdev_modeldata` MODIFY COLUMN `F_Id`  varchar(50) NOT NULL COMMENT '主键' FIRST ;
ALTER TABLE `base_visualdev_modeldata` MODIFY COLUMN `F_VisualDevId`  varchar(50) NULL DEFAULT NULL COMMENT '功能ID' AFTER `F_Id`;
ALTER TABLE `base_visualdev_modeldata` MODIFY COLUMN `F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' AFTER `F_CreatorTime`;
ALTER TABLE `base_visualdev_modeldata` MODIFY COLUMN `F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' AFTER `F_LastModifyTime`;
ALTER TABLE `base_visualdev_modeldata` MODIFY COLUMN `F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' AFTER `F_DeleteTime`;
ALTER TABLE `base_visualdev_modeldata` MODIFY COLUMN `F_ParentId`  varchar(50) NULL DEFAULT NULL COMMENT '区分主子表-' AFTER `F_DeleteUserId`;
ALTER TABLE `base_visualdev_modeldata` MODIFY COLUMN `F_Data`  longtext NULL COMMENT '数据包' AFTER `F_ParentId`;
CREATE TABLE `base_visualdev_release` (
`F_Id`  varchar(50) NOT NULL COMMENT '主键' ,
`F_Description`  longtext NULL COMMENT '描述' ,
`F_SortCode`  bigint(20) NULL DEFAULT NULL COMMENT '排序码' ,
`F_EnabledMark`  int(11) NULL DEFAULT NULL COMMENT '有效标志' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_FullName`  varchar(100) NULL DEFAULT NULL COMMENT '名称' ,
`F_EnCode`  varchar(50) NULL DEFAULT NULL COMMENT '编码' ,
`F_State`  int(11) NULL DEFAULT NULL COMMENT '状态(0-暂存（默认），1-发布)' ,
`F_Type`  int(11) NOT NULL COMMENT '类型(1-应用开发,2-移动开发,3-流程表单)' ,
`F_Table`  longtext NULL COMMENT '关联的表' ,
`F_Category`  varchar(50) NULL DEFAULT NULL COMMENT '分类（数据字典）' ,
`F_FormData`  longtext NULL COMMENT '表单配置JSON' ,
`F_ColumnData`  longtext NULL COMMENT '列表配置JSON' ,
`F_DbLinkId`  varchar(50) NULL DEFAULT NULL COMMENT '关联数据连接id' ,
`F_WebType`  int(11) NULL DEFAULT NULL COMMENT '页面类型（1、纯表单，2、表单加列表，3、表单列表工作流）' ,
`F_FlowId`  varchar(50) NULL DEFAULT NULL COMMENT '关联工作流连接id' ,
`F_AppColumnData`  longtext NULL COMMENT 'app列表配置JSON' ,
`F_EnableFlow`  int(2) NULL DEFAULT 0 COMMENT '启用流程' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
`F_InterfaceId`  varchar(50) NULL DEFAULT NULL COMMENT '接口id' ,
`F_InterfaceName`  varchar(100) NULL DEFAULT NULL COMMENT '接口名称' ,
`F_InterfaceParam`  longtext NULL COMMENT '接口参数' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
CREATE TABLE `base_visualdev_short_link` (
`F_Id`  varchar(50) NOT NULL COMMENT '自然主键' ,
`F_ShortLink`  varchar(255) NULL DEFAULT NULL COMMENT '短链接' ,
`F_FormUse`  int(11) NULL DEFAULT NULL COMMENT '外链填单开关' ,
`F_FormLink`  varchar(255) NULL DEFAULT NULL COMMENT '外链填单' ,
`F_FormPassUse`  int(11) NULL DEFAULT NULL COMMENT '外链密码开关' ,
`F_FormPassword`  varchar(255) NULL DEFAULT NULL COMMENT '外链填单密码' ,
`F_ColumnUse`  int(11) NULL DEFAULT NULL COMMENT '公开查询开关' ,
`F_ColumnLink`  varchar(255) NULL DEFAULT NULL COMMENT '公开查询' ,
`F_ColumnPassUse`  int(11) NULL DEFAULT NULL COMMENT '查询密码开关' ,
`F_ColumnPassword`  varchar(255) NULL DEFAULT NULL COMMENT '公开查询密码' ,
`F_ColumnCondition`  longtext NULL COMMENT '查询条件' ,
`F_ColumnText`  longtext NULL COMMENT '显示内容' ,
`F_RealPcLink`  longtext NULL COMMENT 'PC端链接' ,
`F_RealAppLink`  longtext NULL COMMENT 'App端链接' ,
`F_UserId`  varchar(50) NULL DEFAULT NULL COMMENT '用户id' ,
`F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' ,
`F_CreatorTime`  datetime NULL DEFAULT NULL COMMENT '创建时间' ,
`F_CreatorUserId`  varchar(50) NULL DEFAULT NULL COMMENT '创建用户' ,
`F_LastModifyTime`  datetime NULL DEFAULT NULL COMMENT '修改时间' ,
`F_LastModifyUserId`  varchar(50) NULL DEFAULT NULL COMMENT '修改用户' ,
`F_DeleteMark`  int(11) NULL DEFAULT NULL COMMENT '删除标志' ,
`F_DeleteTime`  datetime NULL DEFAULT NULL COMMENT '删除时间' ,
`F_DeleteUserId`  varchar(50) NULL DEFAULT NULL COMMENT '删除用户' ,
`F_EnabledMark`  int(2) NULL DEFAULT NULL COMMENT '状态' ,
PRIMARY KEY (`F_Id`)
)
ENGINE=InnoDB
ROW_FORMAT=Dynamic
;
ALTER TABLE `blade_visual` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `is_deleted`;
ALTER TABLE `blade_visual_category` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `is_deleted`;
ALTER TABLE `blade_visual_config` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `component`;
ALTER TABLE `blade_visual_db` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `is_deleted`;
ALTER TABLE `blade_visual_map` ADD COLUMN `F_TenantId`  varchar(50) NULL DEFAULT NULL COMMENT '租户id' AFTER `data`;

