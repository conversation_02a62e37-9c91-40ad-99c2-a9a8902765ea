package com.xinghuo.common.database.datatype.limit;

import com.xinghuo.common.database.datatype.db.interfaces.AbstractDtLimitBase;
import com.xinghuo.common.database.datatype.limit.util.DtLimitUtil;
import com.xinghuo.common.database.datatype.model.DtModel;
import com.xinghuo.common.database.datatype.model.DtModelDTO;
import lombok.NoArgsConstructor;

/**
 * 浮点数据类型
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@NoArgsConstructor
public class FloatLimit extends AbstractDtLimitBase {

    public final static String CATEGORY = "type-Float";
    public final static String JAVA_TYPE = "float";

    public FloatLimit(Boolean modify){
        this.isModifyFlag = modify;
    }

    @Override
    public String initDtCategory() {
        return CATEGORY;
    }

    @Override
    public DtModel convert(DtModelDTO viewDtModel){
        DtModel dataTypeModel = DtLimitUtil.convertNumeric(viewDtModel);
        if(this.isModifyFlag){
            DtLimitUtil.getNumericLength(dataTypeModel);
        }
        return dataTypeModel;
    }

}
