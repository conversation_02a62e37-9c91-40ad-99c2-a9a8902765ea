# ant-design-vue 手动导入组件

手动导入组件是指在 Vue 项目中，通过手动导入组件的方式来使用它们。这种方式可以提高代码的可读性和可维护性，同时也可以减少项目的依赖包大小。

## 导入方式
组件内局部注册

```vue
<template>
  <a-input v-model="inputValue" placeholder="请输入内容" />
  <a-select v-model="selectedValue" placeholder="请选择">
    <a-select-option v-for="option in options" :key="option.value" :value="option.value">
      {{ option.label }}
    </a-select-option>
  </a-select>
</template>

<script>
import { defineComponent, ref } from 'vue';
import { Input, Select } from 'ant-design-vue';

export default defineComponent({
  components: {
    AInput: Input,
    ASelect: Select
  },
  setup() {
    const inputValue = ref('');
    const selectedValue = ref(null);
    const options = ref([
      { value: 'option1', label: '选项 1' },
      { value: 'option2', label: '选项 2' },
      { value: 'option3', label: '选项 3' }
    ]);

    return {
      inputValue,
      selectedValue,
      options
    };
  }
});
</script>
```


## 手动导入清单

表单相关组件
- Form  
- FormItem
- Input
- InputNumber 
- Select
- TreeSelect
- DatePicker
- TimePicker
- AutoComplete
- Switch
- Checkbox
- Radio
- Cascader

布局相关组件
- Space
- Divider
- Row  
- Col
- Layout
- Header
- Sider
- Content

弹窗/提示相关组件
- Modal
- Drawer
- Dropdown
- Tooltip
- Popover
- Message

数据展示相关组件
- Table
- Descriptions
- Empty
- Badge
- Avatar
- Tabs
- TabPane
- Carousel

导航相关组件
- PageHeader
- BackTop

其他基础组件
- Button
- Pagination


