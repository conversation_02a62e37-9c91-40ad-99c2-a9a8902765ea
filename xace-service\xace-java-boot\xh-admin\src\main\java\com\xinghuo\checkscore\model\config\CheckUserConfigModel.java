package com.xinghuo.checkscore.model.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 用于绩效考核人员配置的BeanModel
 * 在构造颜色横条，和详情页的时候使用
 *
 * <AUTHOR>
 */
@Data
public class CheckUserConfigModel {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "用户姓名")
    private String userName;

    @Schema(description = "考核人")
    private String parentUserName;

    @Schema(description = "分部id")
    private String fbId;

    @Schema(description = "分部")
    private String fbName;

    @Schema(description = "开始日期")
    private String startDate;

    @Schema(description = "结束日期")
    private String endDate;

    @Schema(description = "考核备注列表")
    private List<CheckNoteModel> noteList;


}
