package com.xinghuo.common.database.source.impl;

import com.baomidou.mybatisplus.annotation.DbType;
import com.xinghuo.common.database.constant.DbConst;
import com.xinghuo.common.database.source.AbstractDbBase;
import com.xinghuo.common.database.sql.model.DbStruct;

/**
 * 达梦模型
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@SuppressWarnings("AlibabaClassNamingShouldBeCamel")
public class DbDM extends AbstractDbBase {

    @Override
    protected void init() {
        setInstance(
                DM,
                DbType.DM,
                com.alibaba.druid.DbType.dm,
                "5236",
                "SYSDBA",
                "dm",
                "dm.jdbc.driver.DmDriver",
                "jdbc:dm://{host}:{port}/{schema}");
    }

    @Override
    protected String getConnUrl(String prepareUrl, String host, Integer port, DbStruct struct){
        prepareUrl = super.getConnUrl(prepareUrl, host, port, null);
        return prepareUrl.replace(DbConst.DB_SCHEMA, struct.getDmDbSchema());
    }

//    public static void setDmTableModel(DbConnDTO connDTO, List<DbTableModel> tableModelList) {
//        //达梦特殊方法
//        try {
//            @Cleanup Connection dmConn = connDTO.getConn();
//            tableModelList.forEach(tm -> {
//                try {
//                    Integer sum = DbDM.getSum(dmConn, tm.getTable());
//                    tm.setSum(sum);
//                } catch (DataException e) {
//                    e.printStackTrace();
//                }
//            });
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    private static Integer getSum(Connection connection, String table) throws DataException {
//        String sql = "SELECT COUNT(*) as F_SUM FROM " + table;
//        return JdbcUtil.queryOneInt(connection, sql, "F_SUM");
//    }

}
