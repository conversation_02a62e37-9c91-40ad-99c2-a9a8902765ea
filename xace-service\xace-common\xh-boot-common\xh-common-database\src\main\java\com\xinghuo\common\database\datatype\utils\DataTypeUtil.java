package com.xinghuo.common.database.datatype.utils;

import java.util.regex.Pattern;

/**
 * 类功能
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class DataTypeUtil {

    //使用预编译
    private static Pattern numericPattern = Pattern.compile("^[-\\+]?[\\d]*$");
    /**
     * 数据类型判断
     */
    public static Boolean numFlag(String... nums){
        for (String num : nums) {
            if(!(numericPattern.matcher(num).matches())){
                return false;
            }
        }
        return true;
    }

}
