package com.xinghuo.common.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xinghuo.common.constant.DbColumnConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 定义AbstractBaseEntity 的V2版本，逐步替换AbstractBaseEntity
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode
@SuppressWarnings("ALL")
public abstract class BaseEntityV2 implements Serializable {


    /**
     * 添加ID字段，默认是F_ID
     * @param <T>
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode
    public static abstract class IBaseEntityV2<T> extends BaseEntityV2 {
        /**
         * 主键
         */

        //为了cglib 不报错，修改为String类型
        @TableId(DbColumnConstant.F_ID)
        public String id;

    }

    /**
     * 扩展ID、租户ID字段（F_TENANTID）
     * @param <T>
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode
    public static abstract class TBaseEntityV2<T> extends IBaseEntityV2<T> {
        /**
         * 租户id
         */
        @TableField(DbColumnConstant.F_TENANT_ID)
        private String tenantId;

    }

    /**
     * 扩展id，租户id，创建者，创建时间
     * @param <T>
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode
    public static abstract class CBaseEntityV2<T> extends TBaseEntityV2<T> {

        /**
         * 创建时间
         */
        @TableField(value = DbColumnConstant.F_CREATED_AT, fill = FieldFill.INSERT)
        private Date createdAt;

        /**
         * 创建用户
         */
        @TableField(value = DbColumnConstant.F_CREATED_BY, fill = FieldFill.INSERT)
        private String createdBy;

    }

    /**
     * 扩展id，租户id，创建者，创建时间,最后更新时间，最后更改人
     * @param <T>
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode
    public static abstract class CUBaseEntityV2<T> extends CBaseEntityV2<T> {

        /**
         * 修改时间
         */
        @TableField(value = DbColumnConstant.F_LAST_UPDATED_AT, fill = FieldFill.INSERT_UPDATE)
        private Date lastUpdatedAt;

        /**
         * 修改用户
         */
        @TableField(value = DbColumnConstant.F_LAST_UPDATED_BY, fill = FieldFill.INSERT_UPDATE)
        private String lastUpdatedBy;

    }

    /**
     * 扩展id，租户id，创建者，创建时间,最后更新时间，最后更改人,删除标志，删除时间，删除操作用户
     * @param <T>
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode
    public static abstract class CUDBaseEntityV2<T> extends CBaseEntityV2<T> {

        /**
         * 删除标志
         */
        @TableField(value = DbColumnConstant.F_DELETE_MARK , updateStrategy = FieldStrategy.IGNORED)
        private Integer deleteMark;

        /**
         * 删除时间
         */
        @TableField(value = DbColumnConstant.F_DELETED_AT , fill = FieldFill.UPDATE)
        private Date deletedAt;

        /**
         * 删除用户
         */
        @TableField(value = DbColumnConstant.F_DELETED_BY , fill = FieldFill.UPDATE)
        private String deletedBy;
    }



}

