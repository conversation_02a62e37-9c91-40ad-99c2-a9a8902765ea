package com.xinghuo.common.util.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.TextNode;

import java.io.IOException;

public class JsonObjectToStringDeserializer extends JsonDeserializer<String> {

    @Override
    public String deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        JsonNode node = jsonParser.getCodec().readTree(jsonParser);
        // 检查节点类型
        if (node instanceof TextNode) {
            // 如果是文本节点，获取其值
            String text = node.textValue();
            // 检查文本是否是JSON格式的字符串
            if (text.startsWith("\"") && text.endsWith("\"")) {
                // 如果是，去除首尾的引号
                return text.substring(1, text.length() - 1);
            }
            // 否则直接返回文本
            return text;
        }
//        else if (node.isObject() || node.isArray()) {
//            // 如果是对象或数组，返回其JSON格式的字符串表示，但不包含外层的引号
//            return node.toString().substring(1, node.toString().length() - 1);
//        }
        // 对于其他类型的节点，直接返回其toString()结果
        return node.toString();
    }
}