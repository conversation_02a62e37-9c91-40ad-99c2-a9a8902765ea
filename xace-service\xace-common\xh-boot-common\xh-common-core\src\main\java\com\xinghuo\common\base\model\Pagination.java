package com.xinghuo.common.base.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 *  分页对象
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
public class Pagination extends Page {
    private Integer pageSize = 20;
    private String sort="DESC";
    private String sidx="";
    private Integer currentPage = 1;

    private Long total;
    private Long records;

    private String dataType;
    /** 高级查询 */
    private String superQueryJson;
    /** 功能id */
    private String moduleId;
    /** 菜单id */
    private String menuId;

    public <T> List<T> setDataList(List<T> data, Long records) {
        this.total = records;
        return data;
    }

    @JsonIgnore
    public <T> List<T> setDataList(List<T> data) {
        this.total = (long) data.size();
        return data;
    }

    @JsonIgnore
    public void setTotalFromInt(Integer total) {
        this.total = total.longValue();
    }
}
