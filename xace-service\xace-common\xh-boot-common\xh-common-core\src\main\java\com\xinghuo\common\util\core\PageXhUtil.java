package com.xinghuo.common.util.core;

import com.xinghuo.common.base.model.Pagination;
import com.xinghuo.common.base.vo.PaginationVO;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 这是一个用于分页的工具类。
 * <AUTHOR>
 * @date 2023-10-05
 */
public class PageXhUtil {
    /**
     * 自定义分页方法。
     * @param page 当前页数
     * @param pageSize 每页的数据量
     * @param list 需要进行分页的数据列表
     * @return 返回当前页的数据列表
     */
    public static List getListPage(int page, int pageSize, List list) {
        // 参数校验
        // 参数校验
        Objects.requireNonNull(list, "List must not be null");
        assert page > 0 && pageSize > 0 : "Page and pageSize must be greater than 0";

        // 如果列表为空或者列表的大小为0，直接返回空列表
        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        // 获取列表的总数据量
        int totalCount = list.size();
        // 页数从0开始，所以需要将输入的页数减1
        page = page - 1;
        // 计算当前页的第一条数据的索引
        int fromIndex = page * pageSize;
        // 如果第一条数据的索引大于等于总数据量，直接返回空列表
        if (fromIndex >= totalCount) {
            return Collections.emptyList();
        }
        // 计算当前页的最后一条数据的索引
        int toIndex = ((page + 1) * pageSize);
        // 如果最后一条数据的索引大于总数据量，将最后一条数据的索引设置为总数据量
        if (toIndex > totalCount) {
            toIndex = totalCount;
        }
        // 返回当前页的数据列表，创建一个新的列表以避免修改原列表
        return list.subList(fromIndex, toIndex);
    }

    public static PaginationVO getPaginationVO(Pagination pagination) {
        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal(pagination.getTotal());
        return paginationVO;
    }


}