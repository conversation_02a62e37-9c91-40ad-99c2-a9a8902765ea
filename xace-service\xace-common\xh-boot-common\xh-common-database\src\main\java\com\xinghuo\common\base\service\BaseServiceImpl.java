package com.xinghuo.common.base.service;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.xinghuo.common.base.model.Pagination;
import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.common.constant.DbColumnConstant;
import com.xinghuo.common.database.plugins.MyDefaultSqlInjector;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.extra.ServletUtil;
import com.xinghuo.common.util.UserProvider;
import org.apache.ibatis.binding.MapperMethod;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;

/**
 * BaseServiceImpl
 * @param <M>
 * @param <T>
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public abstract class BaseServiceImpl<M extends XHBaseMapper<T>, T> extends ServiceImpl<M, T> implements BaseService<T> {



    /**
     * 基于实体ID删除记录时，填充删除用户ID和删除时间。
     * 如果实体类配置了逻辑删除，并且设置了更新填充标志，则在真正删除前，会填充删除用户ID和删除时间。
     *
     * @param entity 要删除的实体对象
     * @return 删除操作是否成功。返回值为布尔类型，成功为true，失败为false。
     */
    @Override
    public boolean removeById(T entity) {
        // 获取实体类对应的表信息
        TableInfo tableInfo = TableInfoHelper.getTableInfo(getEntityClass());
        // 检查是否配置了逻辑删除且开启了更新填充
        if (tableInfo.isWithLogicDelete() && tableInfo.isWithUpdateFill()) {
            try{
                // 检查删除用户ID是否为空，若为空则填充
                if(tableInfo.getPropertyValue(entity, DbColumnConstant.F_DELETE_USER_ID) == null){
                    // 获取当前登录用户的ID
                    String userId = UserProvider.getLoginUserId();
                    if (userId != null) {
                        // 获取当前时间作为删除时间
                        Date deleteTime = DateXhUtil.date();
                        // 填充删除用户ID和删除时间
                        tableInfo.setPropertyValue(entity, DbColumnConstant.F_DELETE_USER_ID , userId);
                        tableInfo.setPropertyValue(entity, DbColumnConstant.F_DELETE_TIME , deleteTime);
                    }
                }
            }catch (Exception e){ }
        }
        // 调用父类的removeById方法执行删除操作
        return super.removeById(entity);
    }


    /**
     * 批量删除记录，根据提供的主键ID或实体列表进行删除。如果启用填充参数，则在删除前会填充删除用户ID和删除时间。
     * @param list 主键ID的集合或实体列表。
     * @param batchSize 每批次处理的个数。
     * @param useFill 是否启用填充机制。为true时，会对入参实体进行逻辑删除的属性填充。
     * @return 返回是否删除成功。
     */
    @Override
    public boolean removeBatchByIds(Collection<?> list, int batchSize, boolean useFill) {
        // 获取删除方法对应的SQL语句
        String sqlStatement = getSqlStatement(SqlMethod.DELETE_BY_ID);
        // 获取实体类对应的表信息
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        // 执行批量删除操作
        return executeBatch(list, batchSize, (sqlSession, e) -> {
            // 如果启用了填充且表配置了逻辑删除
            if (useFill && tableInfo.isWithLogicDelete()) {
                // 判断入参是否为实体类型，是则直接删除
                if (entityClass.isAssignableFrom(e.getClass())) {
                    sqlSession.update(sqlStatement, e);
                } else {
                    // 非实体类型则创建实体实例，设置主键值
                    T instance = tableInfo.newInstance();
                    tableInfo.setPropertyValue(instance, tableInfo.getKeyProperty(), e);
                    try{
                        // 填充删除用户ID和删除时间
                        if(tableInfo.getPropertyValue(instance, DbColumnConstant.F_DELETE_USER_ID) == null){
                            String userId = UserProvider.getLoginUserId();
                            if (userId != null) {
                                Date deleteTime = DateXhUtil.date();
                                tableInfo.setPropertyValue(instance, DbColumnConstant.F_DELETE_USER_ID , userId);
                                tableInfo.setPropertyValue(instance, DbColumnConstant.F_DELETE_TIME , deleteTime);
                            }
                        }
                    }catch (Exception ex){ }
                    // 使用填充后的实体执行删除操作
                    sqlSession.update(sqlStatement, instance);
                }
            } else {
                // 不启用填充则直接删除
                sqlSession.update(sqlStatement, e);
            }
        });
    }


    /**
     * 获取mapperStatementId
     * 这个方法用于生成一个特定的mapper语句ID，基于给定的方法名和忽略逻辑前缀。
     *
     * @param sqlMethod 包含SQL方法名的枚举对象。
     * @return 返回拼接后的mapper语句ID，格式为：mapper类名.方法名IGNORE_LOGIC_PREFIX。
     * @since 3.4.0
     */
    protected String getSqlStatementIgnoreLogic(SqlMethod sqlMethod) {
        // 拼接mapper语句ID
        return mapperClass.getName() + StringPool.DOT + sqlMethod.getMethod() + MyDefaultSqlInjector.IGNORE_LOGIC_PREFIX;
    }


    /**
     * TableId 注解存在更新记录，否插入一条记录
     *
     * @param entity 实体对象
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdateIgnoreLogic(T entity) {
        if (null != entity) {
            TableInfo tableInfo = TableInfoHelper.getTableInfo(this.entityClass);
            Assert.notNull(tableInfo, "error: can not execute. because can not find cache of TableInfo for entity!");
            String keyProperty = tableInfo.getKeyProperty();
            Assert.notEmpty(keyProperty, "error: can not execute. because can not find column for id from entity!");
            Object idVal = tableInfo.getPropertyValue(entity, tableInfo.getKeyProperty());
            return StringUtils.checkValNull(idVal) || Objects.isNull(getByIdIgnoreLogic((Serializable) idVal)) ? save(entity) : updateByIdIgnoreLogic(entity);
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdateBatchIgnoreLogic(Collection<T> entityList, int batchSize) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        Assert.notNull(tableInfo, "error: can not execute. because can not find cache of TableInfo for entity!");
        String keyProperty = tableInfo.getKeyProperty();
        Assert.notEmpty(keyProperty, "error: can not execute. because can not find column for id from entity!");
        return SqlHelper.saveOrUpdateBatch(this.entityClass, this.mapperClass, this.log, entityList, batchSize, (sqlSession, entity) -> {
            Object idVal = tableInfo.getPropertyValue(entity, keyProperty);
            return StringUtils.checkValNull(idVal)
                    || CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatementIgnoreLogic(SqlMethod.SELECT_BY_ID), entity));
        }, (sqlSession, entity) -> {
            MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap<>();
            param.put(Constants.ENTITY, entity);
            sqlSession.update(getSqlStatementIgnoreLogic(SqlMethod.UPDATE_BY_ID), param);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateBatchByIdIgnoreLogic(Collection<T> entityList, int batchSize) {
        String sqlStatement = getSqlStatementIgnoreLogic(SqlMethod.UPDATE_BY_ID);
        return executeBatch(entityList, batchSize, (sqlSession, entity) -> {
            MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap<>();
            param.put(Constants.ENTITY, entity);
            sqlSession.update(sqlStatement, param);
        });
    }

    @Override
    public T getOneIgnoreLogic(Wrapper<T> queryWrapper, boolean throwEx) {
        if (throwEx) {
            return baseMapper.selectOne(queryWrapper);
        }
        return SqlHelper.getObject(log, baseMapper.selectListIgnoreLogic(queryWrapper));
    }

    @Override
    public Map<String, Object> getMapIgnoreLogic(Wrapper<T> queryWrapper) {
        return SqlHelper.getObject(log, baseMapper.selectMapsIgnoreLogic(queryWrapper));
    }

    @Override
    public <V> V getObjIgnoreLogic(Wrapper<T> queryWrapper, Function<? super Object, V> mapper) {
        return SqlHelper.getObject(log, listObjsIgnoreLogic(queryWrapper, mapper));
    }

    @Override
    public boolean removeByIdIgnoreLogic(Serializable id) {
        return SqlHelper.retBool(getBaseMapper().deleteByIdIgnoreLogic(id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdsIgnoreLogic(Collection<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return SqlHelper.retBool(getBaseMapper().deleteBatchIdsIgnoreLogic(list));
    }

    @Override
    public boolean removeByIdIgnoreLogic(Serializable id, boolean useFill) {
        return SqlHelper.retBool(getBaseMapper().deleteByIdIgnoreLogic(id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByIds(Collection<?> list, int batchSize) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        return removeBatchByIdsIgnoreLogic(list, batchSize, tableInfo.isWithLogicDelete() && tableInfo.isWithUpdateFill());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByIdsIgnoreLogic(Collection<?> list, int batchSize, boolean useFill) {
        String sqlStatement = getSqlStatementIgnoreLogic(SqlMethod.DELETE_BY_ID);
        return executeBatch(list, batchSize, (sqlSession, e) -> {
            sqlSession.update(sqlStatement, e);
        });
    }



    /**
     * 根据排序字段对数据进行排序  默认排序请从前端传递
     * @param queryWrapper 查询包装器，用于构建查询条件
     * @param pagination 分页对象，包含分页和排序信息
     * @param entity 实体对象，用于获取排序字段的实际属性名
     */
    protected void sort(QueryWrapper<T> queryWrapper, Pagination pagination, T entity) {
        // 获取排序字段
        String sidx = pagination.getSidx();
        if (StrXhUtil.isEmpty(sidx)) {
           // 如果没有指定排序字段，则默认按ID降序排序
           queryWrapper.orderByDesc(DbColumnConstant.F_ID);
        } else {
            // 根据排序字段获取实际的排序属性名
            String sortField = getSortField(entity, sidx);
            if (sortField != null) {
                // 根据排序方式（升序或降序）设置排序条件
                if ("asc".equalsIgnoreCase(pagination.getSort())) {
                    queryWrapper.orderByAsc(sortField);
                } else {
                    queryWrapper.orderByDesc(sortField);
                }
            }
        }
    }


    private String getSortField(Object entity, String sidx) {
         try {
            String[] strs = sidx.split("_name");
            String fieldName = strs[0];
            Class<?> clazz = entity.getClass();

            while (clazz != null) {
                try {
                    Field declaredField = clazz.getDeclaredField(fieldName);
                    declaredField.setAccessible(true);
                    TableField annotation = declaredField.getAnnotation(TableField.class);
                    if (annotation != null) {
                        return annotation.value();
                    }
                } catch (NoSuchFieldException e) {
                    // Field not found in this class, try the superclass
                    clazz = clazz.getSuperclass();
                }
            }
            // Field not found in any class
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    protected boolean isPc() {
        return "pc".equals(ServletUtil.getHeader("xh-origin"));
    }

    /**
     * 根据指定数据类型处理数据。
     * 
     * @param queryWrapper 查询条件包装对象，用于构造数据库查询条件。
     * @param pagination 分页参数对象，包含当前页码和每页大小等信息。
     * @return 返回处理后的数据列表，可能包含分页信息。
     */
    protected List<T> processDataType(QueryWrapper<T> queryWrapper, Pagination pagination) {
        // 当数据类型标识为"0"时，执行分页数据处理逻辑
        if("1".equals(pagination.getDataType())){
            // 对于非"0"的数据类型，直接执行列表查询并返回全部结果
            return this.list(queryWrapper);

        }else{
            // 构造分页对象
            Page<T> page=new Page<T>(pagination.getCurrentPage(), pagination.getPageSize());
            // 执行分页查询
            IPage<T> userIpage=this.page(page, queryWrapper);
            // 设置查询结果到分页对象并返回
            return pagination.setDataList(userIpage.getRecords(),userIpage.getTotal());
        }
    }
}
