package com.xinghuo.common.base;

/** 数据库信息类
 *
  * <AUTHOR>
 * @date 2023-10-05
 */
public class DataSourceInfo {
    public static final String MYSQL_URL ="jdbc:mysql://{host}:{port}/{dbName}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMultiQueries=true&serverTimezone=UTC";

    public static final String MYSQL_DRIVER ="com.mysql.cj.jdbc.Driver";



    public static final String ORACLE_URL ="jdbc:oracle:thin:@{host}:{port}:{dbName}";

    public static final String ORACLE_DRIVER ="oracle.jdbc.OracleDriver";



    public static final String SQLSERVER_URL ="jdbc:sqlserver://{host}:{port};Databasename={dbName}";

    public static final String SQLSERVER_DRIVER ="com.microsoft.sqlserver.jdbc.SQLServerDriver";

    public static final String DM_URL ="jdbc:dm://{host}:{port}/{dbName}?zeroDateTimeBehavior=convertToNull&useUnicode" +
            "=true&characterEncoding=utf-8";


    public static final String DM_DRIVER ="dm.jdbc.driver.DmDriver";


    public static final String KINGBASE_URL = "jdbc:kingbase8://{host}:{port}/{dbName}";

    public static final String KINGBASE_DRIVER = "com.kingbase8.Driver";


    public static final String POSTGRE_URL = "jdbc:postgresql://{host}:{port}/{dbName}" ;

    public static final String POSTGRE_DRIVER = "org.postgresql.Driver";
}
