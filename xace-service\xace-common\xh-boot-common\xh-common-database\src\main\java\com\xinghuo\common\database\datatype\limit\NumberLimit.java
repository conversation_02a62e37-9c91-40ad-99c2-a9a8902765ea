package com.xinghuo.common.database.datatype.limit;

import com.xinghuo.common.database.datatype.db.DtOracleEnum;
import com.xinghuo.common.database.datatype.db.interfaces.AbstractDtLimitBase;
import com.xinghuo.common.database.datatype.limit.util.DtLimitUtil;
import com.xinghuo.common.database.datatype.model.DtModel;
import com.xinghuo.common.database.datatype.model.DtModelDTO;
import com.xinghuo.common.database.source.AbstractDbBase;

/**
 * 数字数据类型
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class NumberLimit extends AbstractDtLimitBase {

    public final static String CATEGORY = "type-Number";
    public final static String JAVA_TYPE = "number";

    public NumberLimit(Boolean modify){
        this.isModifyFlag = modify;
    }

    @Override
    public String initDtCategory() {
        return CATEGORY;
    }

    @Override
    public DtModel convert(DtModelDTO viewDtModel){
        DtModel dataTypeModel;
        switch (viewDtModel.getDtEnum().getDtCategory()){
            case DecimalLimit.CATEGORY:
            case IntegerLimit.CATEGORY:
            case NumberLimit.CATEGORY:
                dataTypeModel = DtLimitUtil.convertNumeric(viewDtModel);
                break;
            default:
                dataTypeModel = new DtModel(viewDtModel.getDtEnum());
        }
        if(viewDtModel.getConvertTargetDtEnum().getIsModifyFlag()){
            if(viewDtModel.getConvertTargetDtEnum().getDbType().equals(AbstractDbBase.ORACLE)){
                if(dataTypeModel.getNumPrecision().equals(0) && dataTypeModel.getNumScale().equals(0)){
                    dataTypeModel.setNumPrecision(Integer.valueOf(DtOracleEnum.NUMBER.getNumPrecisionLm().getDefaults().toString()));
                    dataTypeModel.setNumScale(Integer.valueOf(DtOracleEnum.NUMBER.getNumScaleLm().getDefaults().toString()));
                }
            }
            DtLimitUtil.getNumericLength(dataTypeModel);
        }
        return dataTypeModel;
    }

}
