# boot-service 代码风格与规范导航

本文档提供 `boot-service` 仓库代码风格与规范的整体导航，帮助开发者快速找到相关指南。

## 文档目录

1. [核心技术栈与架构](./code-guidelines/01_TECH_STACK.md)
    - Java, Maven, Spring Boot3, MybatisPlus, Lombok
    - 多模块 Maven 项目架构

2. [项目结构与包命名](./code-guidelines/02_PROJECT_STRUCTURE.md)
    - 基础包名规范
    - 模块划分
    - 子模块包结构

3. [基本编码规范](./code-guidelines/03_CODING_CONVENTIONS.md)
    - 命名约定
    - 代码格式化
    - 注释规范
    - 常用库的使用规范

4. [各层级代码规范](./code-guidelines/)
    - [Controller 层规范](./code-guidelines/04_CONTROLLER_GUIDELINES.md)
    - [DAO/Mapper 层规范](./code-guidelines/05_DAO_MAPPER_GUIDELINES.md)
    - [Entity 层规范](./code-guidelines/06_ENTITY_GUIDELINES.md)
    - [Model 层规范](./code-guidelines/07_MODEL_GUIDELINES.md)
    - [Service 层规范](./code-guidelines/08_SERVICE_GUIDELINES.md)

5. [AI 代码生成提示词模板](./code-guidelines/09_PROMPTS_TEMPLATES.md)
    - 通用模板
    - 层级专用模板
    - 使用示例

## 快速使用指南

1. **新手上路**：首先阅读 [核心技术栈与架构](./code-guidelines/01_TECH_STACK.md) 和 [项目结构与包命名](./code-guidelines/02_PROJECT_STRUCTURE.md)，了解项目整体框架

2. **开发规范**：根据开发任务参考相应的层级规范文档

3. **使用 AI 辅助开发**：参考 [AI 代码生成提示词模板](./code-guidelines/09_PROMPTS_TEMPLATES.md) 加速开发

## 文档维护

每个规范文档都是独立的，可以单独更新，不会影响其他文档。如果有新的开发规范或最佳实践，可以更新相应的文档或添加新的文档。

> 特别说明：本规范基于项目当前状态制定，随着项目演进可能需要调整。遇到规范与实际情况冲突时，请与团队讨论并更新规范。