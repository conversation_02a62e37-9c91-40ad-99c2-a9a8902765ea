name: 导入规范检查

on:
  push:
    branches: [ main, develop ]
    paths:
      - '**/*.java'
  pull_request:
    branches: [ main, develop ]
    paths:
      - '**/*.java'

jobs:
  import-check:
    runs-on: ubuntu-latest
    name: 检查导入规范

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 检查导入规范
      shell: pwsh
      run: |
        Write-Host "🔍 开始检查导入规范..." -ForegroundColor Yellow

        # 需要检查的错误导入模式
        $ErrorPatterns = @(
            "import javax\.validation\.",
            "import javax\.servlet\.",
            "import javax\.persistence\.",
            "import javax\.ws\.rs\.",
            "import javax\.json\.",
            "import com\.xinghuo\.common\.base\.Pagination;"
        )

        $ErrorCount = 0
        $ErrorFiles = @()

        # 查找所有 Java 文件
        $JavaFiles = Get-ChildItem -Recurse -Filter "*.java" | Where-Object {
            $_.FullName -notlike "*target*" -and
            $_.FullName -notlike "*build*" -and
            $_.FullName -notlike "*.git*"
        }

        Write-Host "📁 找到 $($JavaFiles.Count) 个 Java 文件" -ForegroundColor Green

        foreach ($File in $JavaFiles) {
            $Content = Get-Content -Path $File.FullName -Raw
            $FileHasErrors = $false

            foreach ($Pattern in $ErrorPatterns) {
                if ($Content -match $Pattern) {
                    $ErrorCount++
                    $FileHasErrors = $true

                    $RelativePath = $File.FullName
                    Write-Host "❌ $RelativePath" -ForegroundColor Red

                    # 显示具体的错误行
                    $Lines = $Content -split "`n"
                    for ($i = 0; $i -lt $Lines.Length; $i++) {
                        if ($Lines[$i] -match $Pattern) {
                            Write-Host "   第 $($i + 1) 行: $($Lines[$i].Trim())" -ForegroundColor Yellow
                        }
                    }
                }
            }

            if ($FileHasErrors) {
                $ErrorFiles += $File.FullName
            }
        }

        # 输出总结
        Write-Host ""
        Write-Host "📊 检查完成!" -ForegroundColor Green
        Write-Host "   检查文件数: $($JavaFiles.Count)" -ForegroundColor White
        Write-Host "   发现错误数: $ErrorCount" -ForegroundColor $(if ($ErrorCount -eq 0) { "Green" } else { "Red" })

        if ($ErrorCount -gt 0) {
            Write-Host ""
            Write-Host "❌ 发现不符合导入规范的文件!" -ForegroundColor Red
            Write-Host "请将以下包名进行替换:" -ForegroundColor Yellow
            Write-Host "  javax.validation.* → jakarta.validation.*" -ForegroundColor Cyan
            Write-Host "  javax.servlet.* → jakarta.servlet.*" -ForegroundColor Cyan
            Write-Host "  javax.persistence.* → jakarta.persistence.*" -ForegroundColor Cyan
            Write-Host "  javax.ws.rs.* → jakarta.ws.rs.*" -ForegroundColor Cyan
            Write-Host "  javax.json.* → jakarta.json.*" -ForegroundColor Cyan
            Write-Host "  com.xinghuo.common.base.Pagination → com.xinghuo.common.base.model.Pagination" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "参考文档: .github/code-guidelines/01_TECH_STACK.md" -ForegroundColor Cyan
            exit 1
        } else {
            Write-Host "✅ 所有文件都符合导入规范!" -ForegroundColor Green
            exit 0
        }
