package com.xinghuo.common.database.model.dto;

import com.xinghuo.common.database.model.interfaces.DbSourceOrDbLink;
import com.xinghuo.common.database.source.AbstractDbBase;
import com.xinghuo.common.database.util.DataSourceUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Connection;
import java.util.function.Function;

/**
 * 数据连接相关数据传输对象
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
@NoArgsConstructor
public class DbConnDTO {

    public DbConnDTO(AbstractDbBase dbBase, DataSourceUtil dbSource, Connection conn){
        this.dbBase = dbBase;
        this.dbSourceInfo = dbSource;
        this.conn = conn;
    }

    /**
     * 数据库基类
     */
    private AbstractDbBase dbBase;

    /**
     * 数据源信息
     */
    private DbSourceOrDbLink dbSourceInfo;

    /**
     * 数据连接
     */
    private Connection conn;


    private Function<String, Connection> connFunc;

}
