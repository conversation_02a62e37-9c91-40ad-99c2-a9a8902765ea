package com.xinghuo.common.util.core;

import cn.hutool.core.util.ReflectUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

@Slf4j
public class ReflectXhUtil  extends ReflectUtil {
    /** 是否Debug模式 */
    private static boolean isDebug = false;
    /**
     * 转换对象为字符串，支持自定义对象的 toString 方法或者反射获取对象的成员变量值。
     *
     * @param obj 传入对象
     * @return 对象的字符串表示
     * @since
     */
    public static String toString(final Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj.getClass() == Object.class || obj.getClass().isPrimitive()) {
            return obj.toString();
        }
        try {
            Method method = obj.getClass().getDeclaredMethod("toString", new Class[] {});
            if (isDebug) {
                log.debug("传入的对象实现了自己的toString方法，直接调用！");
            }
            return (String) method.invoke(obj, new Object[] {});
        } catch (NoSuchMethodException e) {
            if (isDebug) {
                log.debug("传入的对象没有实现自己的toString方法，反射获取！");
            }
            StringBuffer buf = new StringBuffer(obj.getClass().getName());
            buf.append(" [");
            // 获取所有成员变量
            Field[] fileds = obj.getClass().getDeclaredFields();
            int size = fileds.length;
            for (int i = 0; i < size; i++) {
                Field field = fileds[i];
                Object value = ReflectXhUtil.getFieldValue(obj, field.getName());
                buf.append(field.getName() + "=" + ReflectXhUtil.toString(value));
                if (i != size - 1) {
                    buf.append(", ");
                }
            }
            buf.append("]");
            return buf.toString();
        } catch (Exception e) {
            throw convertReflectionExceptionToUnchecked(e);
        }
    }

    /**
     * 将反射时的checked exception转换为unchecked exceptions.
     */
    public static RuntimeException convertReflectionExceptionToUnchecked(Exception e) {
        if (e instanceof IllegalAccessException || e instanceof IllegalArgumentException
                || e instanceof NoSuchMethodException) {
            return new IllegalArgumentException("Reflection com.xinghuo.exception.", e);
        } else if (e instanceof InvocationTargetException) {
            return new RuntimeException("Reflection com.xinghuo.exception.", ((InvocationTargetException) e).getTargetException());
        } else if (e instanceof RuntimeException) {
            return (RuntimeException) e;
        }
        return new RuntimeException("Unexpected Checked com.xinghuo.exception.", e);
    }
}
