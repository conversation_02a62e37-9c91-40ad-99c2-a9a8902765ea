<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xinghuo.xace</groupId>
        <artifactId>xh-dependencies</artifactId>
        <version>2.1.0</version>
        <relativePath>xh-dependencies/pom.xml</relativePath>
    </parent>

    <artifactId>xh-common</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>xh-dependencies</module>
        <module>xh-boot-common</module>
    </modules>

    <properties>

    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-dependencies</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-boot-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-common-connector</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-common-database</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-common-office</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-common-cache</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-common-shardingsphere</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-common-sms</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-cloud-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-common-dubbo</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-common-feign</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-common-seata</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


</project>
