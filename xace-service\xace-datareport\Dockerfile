# 基础镜像
FROM eclipse-temurin:8u362-b09-jre-centos7
LABEL maintainer=xh-team

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
	&& echo 'Asia/Shanghai' >/etc/timezone \
	&& yum clean all

# 指定运行时的工作目录
WORKDIR /xace/xh-server/xh-ureport2-datareport

# 将构建产物jar包拷贝到运行时目录中
COPY xh-ureport2-console/target/*.jar ./xh-ureport2-datareport.jar

# 指定容器内运行端口
EXPOSE 30007

# 指定容器启动时要运行的命令
ENTRYPOINT ["/bin/sh","-c","java -Dfile.encoding=utf8 -Djava.security.egd=file:/dev/./urandom -jar xh-ureport2-datareport.jar"]
