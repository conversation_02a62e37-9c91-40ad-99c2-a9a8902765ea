package com.xinghuo.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 设备类型
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Getter
@AllArgsConstructor
public enum DeviceTypeEnum {

    /**
     * pc端
     */
    PC("PC"),

    /**
     * app端 手机都归为移动 自行扩展
     */
    APP("APP"),

    /**
     * 程序运行中使用的无限制临时用户
     */
    TEMPUSER("TEMPUSER"),


    /**
     * 程序运行中使用的限制临时用户， 不可访问主系统, CurrentUser接口报错
     */
    TEMPUSERLIMITED("TEMPUSERLIMITED");


    private final String device;

}
