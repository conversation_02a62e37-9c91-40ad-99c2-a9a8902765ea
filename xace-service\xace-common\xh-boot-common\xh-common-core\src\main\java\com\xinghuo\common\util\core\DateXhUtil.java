package com.xinghuo.common.util.core;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 归总 系统中用到Date的转换类
 * <AUTHOR>
 */
public class DateXhUtil extends DateUtil {

    public static String formatDate(long date) {
        return DatePattern.NORM_DATE_FORMAT.format(date);
    }

    public static String formatDateTime(long date) {
        return DatePattern.NORM_DATETIME_FORMAT.format(date);
    }

    /**
     * 获取某天的开始时间
     *
     * @param date 日期
     * @return {@link DateTime}
     */
    public static DateTime beginOfDay(long date) {
        return new DateTime(beginOfDay(calendar(date)));
    }

    public static DateTime beginOfMonth(String yearMonth){
        return beginOfMonth(DateUtil.parse(yearMonth,"yyyyMM"));
    }

    public static DateTime endOfMonth(String yearMonth){
        return endOfMonth(DateUtil.parse(yearMonth,"yyyyMM"));
    }

    /**
     * 获取某天的结束时间
     *
     * @param date 日期
     * @return {@link DateTime}
     */
    public static DateTime endOfDay(long date) {
        return new DateTime(endOfDay(calendar(date)));
    }

    public static String getZonedDateTimeToString(ZonedDateTime zonedDateTime) {
        ZonedDateTime zoneDateTime1 = zonedDateTime.plusHours(11);
        return DateUtil.format(DateUtil.date(zoneDateTime1.toInstant().toEpochMilli()), "yyyy-MM-dd HH:mm:ss");
    }

    public static String today(String format) {
        return format(new DateTime(),format);
    }


    /**
     * 获取当前时间
     */
    public static String getmmNow() {
        LocalDateTime ldt1 = LocalDateTime.now();
        DateTimeFormatter dtf1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm" );
        String temp = dtf1.format(ldt1);
        return temp;
    }

    public static String now(String pattern) {
        Date date = new Date();
        if (pattern == null && "".equals(pattern)) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat(pattern);
        String dateString = formatter.format(date);
        return dateString;
    }

    public static String cstFormatDateTime(String str) {
        try {
            Date date =  parse(str,DatePattern.JDK_DATETIME_FORMAT);
            return formatDateTime(date);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * TODO 待优化
     * @param num
     * @param repetition
     * @param dates
     * @param repeatTime
     * @param dataList
     */
    public static void getNextDate(int num, String repetition, Date dates, Date repeatTime, List<Date> dataList) {
        if (dates == null) {
            dates = new Date();
        }
        if (repeatTime == null) {
            repeatTime = new Date();
        }
        int result = 0;
        List<String> repetitionList = new ArrayList() {{
            add("2" );
            add("3" );
            add("4" );
            add("5" );
        }};
        if (ObjectUtil.isNotEmpty(repetition) && repetitionList.contains(repetition)) {
            switch (repetition) {
                case "2":
                    DateTime day = cn.hutool.core.date.DateUtil.offsetDay(dates, num);
                    if (day.getTime() <= repeatTime.getTime()) {
                        dataList.add(day);
                        num++;
                        result++;
                    }
                    break;
                case "3":
                    DateTime week = cn.hutool.core.date.DateUtil.offsetDay(dates, num * 7);
                    if (week.getTime() <= repeatTime.getTime()) {
                        dataList.add(week);
                        num++;
                        result++;
                    }
                    break;
                case "4":
                    DateTime month = cn.hutool.core.date.DateUtil.offsetMonth(dates, num);
                    if (month.getTime() <= repeatTime.getTime()) {
                        dataList.add(month);
                        num++;
                        result++;
                    }
                    break;
                case "5":
                    DateTime year = cn.hutool.core.date.DateUtil.offsetMonth(dates, num * 12);
                    if (year.getTime() <= repeatTime.getTime()) {
                        dataList.add(year);
                        num++;
                        result++;
                    }
                    break;
                default:
                    break;

            }
            if (result > 0) {
                getNextDate(num, repetition, dates, repeatTime, dataList);
            }
        } else {
            dataList.add(dates);
        }
    }


    public static String formatSysDateTime(Object value) {
        String dateValue = "";
        if (ObjectUtil.isNotEmpty(value)) {
            if (value instanceof Timestamp || value instanceof Date) {
                dateValue = formatDateTime((Date) value);
            } else if (value instanceof LocalDateTime) {
                dateValue = LocalDateTimeUtil.format((LocalDateTime) value,DatePattern.NORM_DATETIME_FORMATTER);
            }
        }
        return dateValue;
    }

    public static String formatSysDate(Object value) {
        String dateValue = "";
        if (ObjectUtil.isNotEmpty(value)) {
            if (value instanceof Timestamp || value instanceof Date) {
                dateValue = formatDate((Date) value);
            } else if (value instanceof LocalDateTime) {
                dateValue = LocalDateTimeUtil.format((LocalDateTime) value,DatePattern.NORM_DATE_FORMATTER);
            }
        }
        return dateValue;
    }


    public static String[] getDatesByMonth(String monthStr) {
        DateTime date = DateUtil.parse(monthStr, "yyyyMM");
        int maxDay = DateUtil.endOfMonth(date).dayOfMonth();
        String[] dates = new String[maxDay];
        for (int i = 0; i < maxDay; i++) {
            dates[i] = DateUtil.formatDate(offsetDay(date, i));
        }
        return dates;
    }

    public static Integer lengthOfMonth(String yearMonth){
        int year = Integer.parseInt(yearMonth.substring(0, 4));
        int month = Integer.parseInt(yearMonth.substring(4));
        return DateUtil.lengthOfMonth(month, DateUtil.isLeapYear(year));
    }

}
