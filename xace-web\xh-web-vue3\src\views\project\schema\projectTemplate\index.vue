<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate">
          <Icon icon="ant-design:plus-outlined" />
          新增项目模板
        </a-button>
        <a-button
          type="primary"
          ghost
          @click="handleBatchEnable"
          :disabled="!hasSelected"
        >
          <Icon icon="ant-design:check-outlined" />
          批量启用
        </a-button>
        <a-button 
          type="primary" 
          ghost 
          @click="handleBatchDisable" 
          :disabled="!hasSelected"
        >
          <Icon icon="ant-design:stop-outlined" />
          批量禁用
        </a-button>
        <a-button 
          color="error" 
          @click="handleBatchDelete" 
          :disabled="!hasSelected"
        >
          <Icon icon="ant-design:delete-outlined" />
          批量删除
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                label: '查看详情',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'clarity:note-edit-line',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:copy-outlined',
                label: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                label: '删除',
                onClick: handleDelete.bind(null, record),
              },
            ]"
            :dropDownActions="[
              {
                label: getStatusActionLabel(record.status),
                onClick: getStatusAction(record),
              },
              {
                label: '模板配置',
                onClick: handleTemplateConfig.bind(null, record),
              },
              {
                label: '导入配置',
                onClick: handleImportConfig.bind(null, record),
              },
              {
                label: '应用到项目',
                onClick: handleApplyToProjects.bind(null, record),
              },
              {
                label: '使用情况',
                onClick: handleUsageInfo.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <FormDrawer @register="registerFormDrawer" @reload="handleSuccess" />
    <DetailDrawer @register="registerDetailDrawer" />
    <ProjectTemplateCopyModal @register="registerCopyModal" @success="handleSuccess" />
    <TemplateConfigDrawer @register="registerConfigDrawer" @reload="handleSuccess" />
    <ImportConfigModal @register="registerImportModal" @success="handleSuccess" />
    <ApplyToProjectsModal @register="registerApplyModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, computed } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useDrawer } from '/@/components/Drawer';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  
  import FormDrawer from './FormDrawer.vue';
  import DetailDrawer from './DetailDrawer.vue';
  import ProjectTemplateCopyModal from './ProjectTemplateCopyModal.vue';
  import TemplateConfigDrawer from './TemplateConfigDrawer.vue';
  import ImportConfigModal from './ImportConfigModal.vue';
  import ApplyToProjectsModal from './ApplyToProjectsModal.vue';
  import { columns, searchFormSchema } from './projectTemplate.data';
  import {
    getProjectTemplateList,
    deleteProjectTemplate,
    batchDeleteProjectTemplate,
    enableProjectTemplate,
    disableProjectTemplate,
    batchEnableProjectTemplate,
    batchDisableProjectTemplate,
    getProjectTemplateUsageInfo,
  } from '/@/api/project/projectTemplate';

  defineOptions({ name: 'project-schema-projectTemplate' });

  const { createMessage } = useMessage();
  const { hasPermission } = usePermission();

  // 搜索信息
  const searchInfo = reactive<Recordable>({});

  // 表格配置
  const [registerTable, { getSelectRows, clearSelectedRowKeys, reload }] = useTable({
    title: '项目模板列表',
    api: getProjectTemplateList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    handleSearchInfoFn(info) {
      console.log('handleSearchInfoFn', info);
      return info;
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: undefined,
    },
    rowSelection: {
      type: 'checkbox',
    },
  });

  // 抽屉配置
  const [registerFormDrawer, { openDrawer: openFormDrawer }] = useDrawer();
  const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
  const [registerConfigDrawer, { openDrawer: openConfigDrawer }] = useDrawer();

  // 模态框配置
  const [registerCopyModal, { openModal: openCopyModal }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerApplyModal, { openModal: openApplyModal }] = useModal();

  // 计算属性
  const hasSelected = computed(() => getSelectRows().length > 0);

  // 获取状态操作标签
  function getStatusActionLabel(status: number) {
    switch (status) {
      case 0:
        return '禁用';
      case 1:
        return '启用';
      default:
        return '启用';
    }
  }

  // 获取状态操作函数
  function getStatusAction(record: Recordable) {
    switch (record.status) {
      case 0:
        return () => handleDisable(record);
      case 1:
        return () => handleEnable(record);
      default:
        return () => handleEnable(record);
    }
  }

  // 新增
  function handleCreate() {
    openFormDrawer(true, {
      isUpdate: false,
    });
  }

  // 编辑
  function handleEdit(record: Recordable) {
    openFormDrawer(true, {
      record,
      isUpdate: true,
    });
  }

  // 查看详情
  function handleView(record: Recordable) {
    openDetailDrawer(true, { record });
  }

  // 复制
  function handleCopy(record: Recordable) {
    openCopyModal(true, { record });
  }

  // 模板配置
  function handleTemplateConfig(record: Recordable) {
    openConfigDrawer(true, { record });
  }

  // 导入配置
  function handleImportConfig(record: Recordable) {
    openImportModal(true, { record });
  }

  // 应用到项目
  function handleApplyToProjects(record: Recordable) {
    openApplyModal(true, { record });
  }

  // 删除
  async function handleDelete(record: Recordable) {
    try {
      await deleteProjectTemplate(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      console.error('删除失败:', error);
    }
  }

  // 批量删除
  async function handleBatchDelete() {
    const rows = getSelectRows();
    if (rows.length === 0) {
      createMessage.warning('请选择要删除的数据');
      return;
    }

    try {
      const ids = rows.map((row) => row.id);
      await batchDeleteProjectTemplate(ids);
      createMessage.success('批量删除成功');
      clearSelectedRowKeys();
      reload();
    } catch (error) {
      console.error('批量删除失败:', error);
    }
  }

  // 启用
  async function handleEnable(record: Recordable) {
    try {
      await enableProjectTemplate(record.id);
      createMessage.success('启用成功');
      reload();
    } catch (error) {
      console.error('启用失败:', error);
    }
  }

  // 禁用
  async function handleDisable(record: Recordable) {
    try {
      await disableProjectTemplate(record.id);
      createMessage.success('禁用成功');
      reload();
    } catch (error) {
      console.error('禁用失败:', error);
    }
  }

  // 批量启用
  async function handleBatchEnable() {
    const rows = getSelectRows();
    if (rows.length === 0) {
      createMessage.warning('请选择要启用的数据');
      return;
    }

    try {
      const ids = rows.map((row) => row.id);
      await batchEnableProjectTemplate(ids);
      createMessage.success('批量启用成功');
      clearSelectedRowKeys();
      reload();
    } catch (error) {
      console.error('批量启用失败:', error);
    }
  }

  // 批量禁用
  async function handleBatchDisable() {
    const rows = getSelectRows();
    if (rows.length === 0) {
      createMessage.warning('请选择要禁用的数据');
      return;
    }

    try {
      const ids = rows.map((row) => row.id);
      await batchDisableProjectTemplate(ids);
      createMessage.success('批量禁用成功');
      clearSelectedRowKeys();
      reload();
    } catch (error) {
      console.error('批量禁用失败:', error);
    }
  }

  // 使用情况
  async function handleUsageInfo(record: Recordable) {
    try {
      const result = await getProjectTemplateUsageInfo(record.id);
      // TODO: 显示使用情况信息
      console.log('使用情况:', result);
      createMessage.info('使用情况查询功能待完善');
    } catch (error) {
      console.error('获取使用情况失败:', error);
    }
  }

  // 操作成功回调
  function handleSuccess() {
    reload();
  }
</script>
