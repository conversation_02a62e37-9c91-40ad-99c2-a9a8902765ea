package com.xinghuo.common.database.datatype.limit;

import com.xinghuo.common.database.datatype.db.interfaces.AbstractDtLimitBase;
import com.xinghuo.common.database.datatype.model.DtModel;
import com.xinghuo.common.database.datatype.model.DtModelDTO;
import lombok.NoArgsConstructor;

/**
 * 时间数据类型
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@NoArgsConstructor
public class DateTimeLimit extends AbstractDtLimitBase {

    public final static String CATEGORY = "type-DateTime";
    public final static String JAVA_TYPE = "date";

    public DateTimeLimit(Boolean modify){
        this.isModifyFlag = modify;
    }

    @Override
    public String initDtCategory() {
        return CATEGORY;
    }

    @Override
    public DtModel convert(DtModelDTO dtModelDTO){
        DtModel dataTypeModel = new DtModel(dtModelDTO.getConvertTargetDtEnum());
        if(this.isModifyFlag){
            dataTypeModel.setFormatLengthStr("");
        }
        return dataTypeModel;
    }

}
