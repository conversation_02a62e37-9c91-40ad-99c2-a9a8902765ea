package com.xinghuo.checkscore.model.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-01-29
 */
@Data
public class CheckRatioModel {
    @Schema(description = "ID")
    private String id;
    @Schema(description = "员工ID")
    private String userId;
    @Schema(description = "员工姓名")
    private String userName;
    @Schema(description = "考核人姓名")
    private String parentUserName;
    @Schema(description = "考核人ID")
    private String parentUserId;
    @Schema(description = "考核比例")
    private Integer ratio;

    @Schema(description = "开始日期")
    private String startDate;
    @Schema(description = "结束日期")
    private String endDate;

    @Schema(description = "考核原因")
    private String checkReason;
}
