package com.xinghuo.common.annotation;


import org.junit.jupiter.api.Test;
import org.springframework.core.annotation.AnnotationUtils;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 请求日志注解Test类
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
class HandleLogTest {

    @Test
    void shouldHaveModuleName() throws NoSuchMethodException {
        // 测试方法是否有 moduleName 注解属性
        Method method = SampleClass.class.getMethod("sampleMethod");
        HandleLog handleLog = AnnotationUtils.findAnnotation(method, HandleLog.class);
        assertNotNull(handleLog.moduleName());
    }

    @Test
    void shouldHaveRequestMethod() throws NoSuchMethodException {
        // 测试方法是否有 requestMethod 注解属性
        Method method = SampleClass.class.getMethod("sampleMethod");
        HandleLog handleLog = AnnotationUtils.findAnnotation(method, HandleLog.class);
        assertNotNull(handleLog.requestMethod());
    }

    @Test
    void moduleNameShouldNotBeEmpty() throws NoSuchMethodException {
        // 测试 moduleName 注解属性是否为空
        Method method = SampleClass.class.getMethod("sampleMethod");
        HandleLog handleLog = AnnotationUtils.findAnnotation(method, HandleLog.class);
        assertFalse(handleLog.moduleName().isEmpty());
    }

    @Test
    void requestMethodShouldNotBeEmpty() throws NoSuchMethodException {
        // 测试 requestMethod 注解属性是否为空
        Method method = SampleClass.class.getMethod("sampleMethod");
        HandleLog handleLog = AnnotationUtils.findAnnotation(method, HandleLog.class);
        assertFalse(handleLog.requestMethod().isEmpty());
    }
}

class SampleClass {
    @HandleLog(moduleName = "Test Module", requestMethod = "GET")
    public void sampleMethod() {
    }
}