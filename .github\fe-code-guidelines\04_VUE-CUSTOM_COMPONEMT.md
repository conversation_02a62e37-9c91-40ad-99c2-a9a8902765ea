# Vue 自定义组件

> + 自定义组件是 Vue 中常用的一种组件化开发方式，它允许开发者将一个组件的功能和样式封装起来，以便在不同的地方重复使用。自定义组件可以提高代码的可维护性和复用性，同时也可以提高开发效率。
> + 框架优先使用自定义组件，其次使用第三方组件，最后使用官方组件。

## src/components/card

- **Card.vue** - 基础卡片容器组件

## src/components/Charts

- **ChartCard.vue** - 图表卡片容器,支持图表嵌入
- **DynamicChart.vue** - 动态可配置Echarts图表组件
- **MiniProgress.vue** - 迷你进度条组件

## src/components/Container

- **LazyContainer.vue** - 懒加载容器,可延迟加载内容
- **ScrollContainer.vue** - 滚动容器,支持虚拟滚动和自定义滚动条
- **SimpleContainer.vue** - 简单容器组件

## src/components/contextmenu

- **Contextmenu.vue** - 右键菜单组件
- **ContextmenuItem.vue** - 右键菜单项组件

## src/components/CountDown

- **CountdownInput.vue** - 倒计时输入框组件
- **CountButton.vue** - 倒计时按钮组件

## src/components/Drawer

- **DrawerFooter.vue** - 抽屉底部组件
- **DrawerHeader.vue** - 抽屉头部组件

## src/components/Excel

- **ExportExcelModal.vue** - Excel导出模态框组件
- **ImportExcelModal.vue** - Excel导入模态框组件

## src/components/FlowProcess

- **FlowCard.vue** - 流程卡片组件
- **FlowDesigner.vue** - 流程设计器组件
- **ProcessCardModal.vue** - 流程卡片模态框组件

## src/components/Form

- **ApiSelect.vue** - 远程数据选择器组件
- **ApiTreeSelect.vue** - 远程数据树选择器组件
- **CheckboxGroup.vue** - 复选框组组件
- **InputNumberRange.vue** - 数字范围输入组件
- **RadioButtonGroup.vue** - 单选按钮组组件
- **SettingDrawer.vue** - 设置抽屉组件
- **StrengthMeter.vue** - 密码强度计量组件

## src/components/Modal

- **ModalClose.vue** - 模态框关闭按钮组件
- **ModalFooter.vue** - 模态框底部组件
- **ModalHeader.vue** - 模态框头部组件

## src/components/Page

- **PageFooter.vue** - 页面底部组件
- **PageWrapper.vue** - 页面包装容器组件

## src/components/Table

- **BasicTable.vue** - 基础表格组件
- **EditableCell.vue** - 可编辑单元格组件
- **TableAction.vue** - 表格操作列组件

## src/components/Time

- **DatePicker.vue** - 日期选择组件
- **DateRangePicker.vue** - 日期范围选择组件
- **TimePicker.vue** - 时间选择组件

## src/components/Tree

- **DirectoryTree.vue** - 目录树组件
- **TreeIcon.vue** - 树节点图标组件

## src/components/Upload

- **ThumbUrl.vue** - 图片缩略图组件
- **UploadModal.vue** - 上传模态框组件
- **UploadPreviewModal.vue** - 上传预览模态框组件

## src/components/Verify

- **ImgRotateDrag.vue** - 图片旋转拖动验证组件
- **RotateDragVerify.vue** - 旋转拖动验证组件
- **SlideVerify.vue** - 滑动验证组件

## src/components/xh

- **XhColorPicker.vue** - 颜色选择器组件
- **XhCron.vue** - Cron表达式编辑器组件
- **XhDepSelect.vue** - 部门选择器组件
- **XhEditor.vue** - 富文本编辑器组件
- **XhOrgSelect.vue** - 组织机构选择器组件
- **XhRegionSelect.vue** - 地区选择器组件
- **XhRelationForm.vue** - 关联表单组件
- **XhSelect.vue** - 通用选择器组件
- **XhTreeSelect.vue** - 树形选择器组件
- **XhUserSelect.vue** - 用户选择器组件

## src/components/Application

- **AppDarkModeToggle.vue** - 暗黑模式切换组件
- **AppLocalePicker.vue** - 多语言选择器组件
- **AppSearch.vue** - 应用搜索组件

## src/components/Authority

- **Authority.vue** - 权限控制组件
- **AuthorityApi.vue** - API权限控制组件

## src/components/BasicTitle

- **BasicTitle.vue** - 标题组件

## src/components/CollapseContainer

- **CollapseContainer.vue** - 可折叠容器组件

## src/components/ContextMenu

- **ContextMenu.vue** - 右键菜单组件
- **ContextMenuItem.vue** - 右键菜单项组件

## src/components/CountTo

- **CountTo.vue** - 数字滚动组件

## src/components/VirtualScroll

- **VirtualScroll.vue** - 虚拟滚动组件

## src/components/Tinymce

- **Tinymce.vue** - TinyMCE编辑器组件

## src/components/Loading

- **Loading.vue** - 加载组件