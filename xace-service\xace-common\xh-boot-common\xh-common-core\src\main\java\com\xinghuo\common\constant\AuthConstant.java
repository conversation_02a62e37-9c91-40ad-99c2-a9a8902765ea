package com.xinghuo.common.constant;


import cn.dev33.satoken.same.SaSameUtil;

/**
 * 鉴权常量类
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class AuthConstant {

    public static final String DEF_TENANT_ID = "";
    public static final String DEF_TENANT_DB = "";

    public static final String ACCOUNT_TYPE_DEFAULT = "login";
    public static final String ACCOUNT_TYPE_TENANT = "tenant";
    public static final String ACCOUNT_LOGIC_BEAN_DEFAULT = "defaultStpLogic";
    public static final String ACCOUNT_LOGIC_BEAN_TENANT = "tenantStpLogic";

    public static final String PAR_GRANT_TYPE = "grant_type";

    public static final String SYSTEM_INFO = "system_info";

    /**
     * 跨服务调用验证KEY
     */
    public static final String INNER_TOKEN_KEY = SaSameUtil.SAME_TOKEN;

    public static final String INNER_GATEWAY_TOKEN_KEY = INNER_TOKEN_KEY + "_GATEWAY";

    public static final String TENANT_SESSION = "tenant:";

    public static final String TOKEN_PREFIX = "bearer";
    public static final String TOKEN_PREFIX_SP = TOKEN_PREFIX + " ";

    public static final String PARAMS_XH_TICKET = "xh_ticket";
    public static final String PARAMS_SSO_LOGOUT_TICKET = "ticket";

    public static final Integer REDIRECT_PAGETYPE_LOGIN = 1;
    public static final Integer REDIRECT_PAGETYPE_LOGOUT = 2;

    public static final Integer TMP_TOKEN_UNLOGIN = -1;
    public static final Integer TMP_TOKEN_ERRLOGIN = -2;

    public static final String ONLINE_TICKET_KEY = "online_ticket:";
    public static final String ONLINE_TICKET_TOKEN = "online_token";

    public static final String JWT_SECRET = "WviMjFNC72VKwGqm5LPoheQo5XN9iN4d";

    /**
     * clientId
     */
    public static final String CLIENT_ID = "Client_Id";

    static final String USER_DETAIL_PREFIX = "USERDETAIL_";

    /**
     * 用户信息获取方式 account
     */
    public static final String USERDETAIL_ACCOUNT = USER_DETAIL_PREFIX + "UserAccount";
    /**
     * 用户信息获取方式 user_id
     */
    public static final String USERDETAIL_USER_ID = USER_DETAIL_PREFIX + "UserId";

    /**
     * 认证方式 常规账号密码
     */
    public static final String GRANT_TYPE_PASSWORD = "password";
    /**
     * 认证方式 单点 CAS
     */
    public static final String GRANT_TYPE_CAS = "cas";
    /**
     * 认证方式 单点 OAUTH
     */
    public static final String GRANT_TYPE_OAUTH = "auth2";

    /**
     * sa accountSession中权限码的key
     */
    public static final String PERMISSION_KEY = "user_permission";

    /**
     * sa accountSession中角色的key
     */
    public static final String ROLE_KEY = "user_roles";

    /**
     * 管理员标识
     */
    public static final String ADMIN_KEY = "admin";

}
