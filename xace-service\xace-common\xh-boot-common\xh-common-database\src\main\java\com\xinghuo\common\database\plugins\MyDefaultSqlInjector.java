package com.xinghuo.common.database.plugins;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.injector.methods.*;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.xinghuo.common.util.core.ReflectXhUtil;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * MyDefaultSqlInjector 是 MyBatis Plus 的自定义 SQL 注入器。
 *
 * 继承了 DefaultSqlInjector，并通过增强（Enhancer）方式为每个默认方法添加了 IgnoreLogic 结尾的方法，
 * 用于操作已逻辑删除的数据。
 *
 * @see DefaultSqlInjector
 * @see AbstractMethod
 */
public class MyDefaultSqlInjector extends DefaultSqlInjector {

    /**
     * IgnoreLogic 方法的前缀
     */
    public static final String IGNORE_LOGIC_PREFIX = "IgnoreLogic";

    /**
     * 重写获取方法列表的方法，为每个默认方法增加 IgnoreLogic 结尾的方法
     *
     * @param mapperClass   Mapper 接口类
     * @param tableInfo     表信息
     * @return              方法列表
     */
    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
        Stream.Builder<AbstractMethod> builder = Stream.<AbstractMethod>builder();
        addInjector(builder, new Insert());
        addInjector(builder, new Delete());
        addInjector(builder, new DeleteByMap());
        addInjector(builder, new Update());
        addInjector(builder, new SelectByMap());
        addInjector(builder, new SelectCount());
        addInjector(builder, new SelectMaps());
        addInjector(builder, new SelectMapsPage());
        addInjector(builder, new SelectObjs());
        addInjector(builder, new SelectList());
        addInjector(builder, new SelectPage());
        if (tableInfo.havePK()) {
            addInjector(builder, new DeleteById());
            addInjector(builder, new DeleteBatchByIds());
            addInjector(builder, new UpdateById());
            addInjector(builder, new SelectById());
            addInjector(builder, new SelectBatchByIds());
        } else {
            logger.warn(String.format("%s ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method." ,
                    tableInfo.getEntityType()));
        }
        return builder.build().collect(toList());
    }

    /**
     * 添加注入器
     *
     * @param builder   注入器列表
     * @param method    抽象方法
     */
    private void addInjector(Stream.Builder<AbstractMethod> builder, AbstractMethod method) {
        builder.add(method);
        // 为每个默认方法增加 IgnoreLogic 结尾的方法
        builder.add(enhancerMethod(method));
    }

    /**
     * 通过 CGLib Enhancer 为默认方法增加 IgnoreLogic 结尾的方法
     *
     * @param method    抽象方法
     * @return          增强后的方法
     */
    private AbstractMethod enhancerMethod(AbstractMethod method){
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(method.getClass());
        enhancer.setCallback(new MethodInterceptor() {
            @Override
            public Object intercept(Object o, Method method, Object[] objects, MethodProxy methodProxy) throws Throwable {
                if(method.getName().equals("inject")){
                    // 获取方法的 TableInfo 参数
                    TableInfo tableInfo = (TableInfo) objects[3];
                    // 如果表启用了逻辑删除
                    if(tableInfo.isWithLogicDelete()) {
                        // 创建一个新的 TableInfo，并复制属性，将 withLogicDelete 设置为 false
                        TableInfo tableInfo1 = new TableInfo(tableInfo.getConfiguration(), tableInfo.getEntityType());
                        BeanUtil.copyProperties(tableInfo, tableInfo1);
                        ReflectXhUtil.setFieldValue(tableInfo1, "withLogicDelete" , false);
                        objects[3] = tableInfo1;
                    }
                }
                return methodProxy.invokeSuper(o, objects);
            }
        });
        // 创建增强后的方法，方法名加上 IGNORE_LOGIC_PREFIX
        return (AbstractMethod) enhancer.create(new Class[]{String.class}, new Object[]{ReflectXhUtil.getFieldValue(method, "methodName" ) + IGNORE_LOGIC_PREFIX});
    }

    /**
     * 获取方法名
     *
     * @param method    抽象方法
     * @return          方法名
     */
    private String getMethodName(AbstractMethod method) {
        try {
            Field field = method.getClass().getField("methodName" );
            field.setAccessible(true);
            return field.get(method).toString();
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
