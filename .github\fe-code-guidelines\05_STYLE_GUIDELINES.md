# CSS/LESS 样式编写规范

## 基本原则

1. **一致性**：遵循项目已有的样式命名和组织方式
2. **可维护性**：编写清晰易读的样式代码
3. **可重用性**：抽象通用样式，避免重复
4. **命名语义化**：类名应反映内容而非外观
5. **模块化**：样式应该按组件或功能划分
6. **性能优化**：避免过度复杂的选择器和不必要的嵌套
7. **响应式设计**：确保在不同设备上的良好体验

## 样式命名规范

### 命名方法

1. **使用kebab-case**:
   ```css
   .user-profile {}
   .nav-item {}
   .btn-primary {}
   ```

2. **功能性命名**:
   ```css
   /* 推荐 */
   .nav-header {}
   .product-list {}

   /* 不推荐 */
   .red-text {}
   .big-margin {}
   ```

3. **BEM命名法则**:
   ```css
   /* Block（块） */
   .card {}

   /* Element（元素） - 使用双下划线连接 */
   .card__header {}
   .card__body {}
   .card__footer {}

   /* Modifier（修饰符） - 使用双连字符连接 */
   .card--featured {}
   .card__header--large {}
   ```

4. **组件前缀**:
   ```css
   /* 组件作用域内的类名 */
   .card-header {}
   .card-body {}
   .card-footer {}

   /* 使用前缀区分不同类型的组件 */
   .c-card {} /* 组件 */
   .l-header {} /* 布局 */
   .u-clearfix {} /* 工具类 */
   .t-dark {} /* 主题 */
   ```

5. **状态类名**:
   ```css
   .is-active {}
   .is-disabled {}
   .has-error {}
   .is-loading {}
   ```

6. **工具类命名**:
   ```css
   /* 通用工具类 */
   .u-text-center {}
   .u-margin-top-lg {}
   .u-flex-center {}
   .u-hidden {}
   ```

## 选择器使用

1. **选择器嵌套**:
   ```less
   // 最多嵌套3层
   .article {
     .header {
       .title {
         font-size: 20px;
       }
     }
   }
   ```

2. **避免过度依赖嵌套**:
   ```less
   // 不推荐
   .user {
     .info {
       .name {
         .first-name {
           color: red;
         }
       }
     }
   }

   // 推荐
   .user-info-name-first {
     color: red;
   }
   ```

3. **使用scoped或CSS Modules**:
   ```vue
   <style lang="less" scoped>
   /* 局部作用域样式 */
   </style>
   ```

## 样式组织

### 文件结构

1. **组件样式**:
   - 直接在组件内使用`<style scoped>`
   - 复杂组件可以抽离到单独的样式文件

2. **全局样式**:
   - `/src/assets/styles/`目录下组织

3. **样式文件命名**:
   - 与对应组件同名: `UserCard.vue` → `UserCard.less`
   - 通用样式用途命名: `variables.less`, `mixins.less`

### 样式顺序

1. **属性顺序**:
   ```css
   .element {
     /* 定位属性 */
     position: absolute;
     top: 0;
     right: 0;

     /* 盒模型属性 */
     display: block;
     width: 100px;
     height: 100px;
     padding: 10px;
     margin: 10px;

     /* 排版属性 */
     font-size: 14px;
     text-align: center;
     line-height: 1.5;

     /* 视觉效果 */
     color: #333;
     background-color: #f5f5f5;
     border: 1px solid #e5e5e5;

     /* 其他属性 */
     opacity: 0.8;
     cursor: pointer;
   }
   ```

2. **媒体查询位置**:
   ```css
   .element {
     /* 基础样式 */
   }

   @media (max-width: 768px) {
     .element {
       /* 响应式样式 */
     }
   }
   ```

## 变量与主题

### 使用变量

1. **颜色变量**:
   ```less
   // 基础颜色
   @primary-color: #1890ff;
   @success-color: #52c41a;
   @warning-color: #faad14;
   @error-color: #f5222d;
   @info-color: #1890ff;

   // 文本颜色
   @text-color-primary: rgba(0, 0, 0, 0.85);
   @text-color-secondary: rgba(0, 0, 0, 0.65);
   @text-color-disabled: rgba(0, 0, 0, 0.45);

   // 背景颜色
   @bg-color-base: #f5f5f5;
   @bg-color-light: #fafafa;
   @bg-color-dark: #f0f0f0;

   // 边框颜色
   @border-color-base: #d9d9d9;
   @border-color-split: #f0f0f0;

   // 使用示例
   .btn-primary {
     background-color: @primary-color;
     color: #fff;
   }

   .alert-warning {
     background-color: fade(@warning-color, 15%);
     border: 1px solid fade(@warning-color, 30%);
   }
   ```

2. **尺寸变量**:
   ```less
   // 字体大小
   @font-size-base: 14px;
   @font-size-lg: 16px;
   @font-size-sm: 12px;
   @font-size-xs: 10px;
   @font-size-xxl: 24px;
   @font-size-xl: 20px;

   // 行高
   @line-height-base: 1.5;
   @line-height-compact: 1.3;

   // 间距
   @spacing-unit: 8px;
   @padding-lg: @spacing-unit * 3; // 24px
   @padding-md: @spacing-unit * 2; // 16px
   @padding-sm: @spacing-unit * 1.5; // 12px
   @padding-xs: @spacing-unit; // 8px
   @padding-xxs: @spacing-unit / 2; // 4px

   // 边框
   @border-radius-base: 4px;
   @border-radius-sm: 2px;
   @border-radius-lg: 8px;
   @border-width-base: 1px;

   // 使用示例
   .card {
     padding: @padding-md;
     font-size: @font-size-base;
     border-radius: @border-radius-base;
     line-height: @line-height-base;
   }

   .tag {
     padding: @padding-xxs @padding-xs;
     font-size: @font-size-sm;
     border-radius: @border-radius-sm;
   }
   ```

3. **z-index管理**:
   ```less
   // z-index层级管理
   @z-index-base: 1;
   @z-index-dropdown: 1000;
   @z-index-sticky: 1020;
   @z-index-fixed: 1030;
   @z-index-modal-backdrop: 1040;
   @z-index-modal: 1050;
   @z-index-popover: 1060;
   @z-index-tooltip: 1070;

   // 使用示例
   .dropdown {
     z-index: @z-index-dropdown;
   }

   .modal {
     z-index: @z-index-modal;
   }

   .modal-backdrop {
     z-index: @z-index-modal-backdrop;
   }
   ```

### 主题定制

1. **利用Less变量覆盖**:
   ```less
   // 覆盖Ant Design Vue主题变量
   @import 'ant-design-vue/dist/antd.less';

   // 自定义主题
   @primary-color: #1DA57A;
   @link-color: #1DA57A;
   @success-color: #52c41a;
   @border-radius-base: 4px;

   // 组件特定变量
   @btn-border-radius-base: @border-radius-base;
   @input-border-color: @border-color-base;
   ```

2. **CSS变量与动态主题**:
   ```less
   // 定义CSS变量
   :root {
     // 亮色主题（默认）
     --primary-color: #1890ff;
     --text-color: rgba(0, 0, 0, 0.85);
     --bg-color: #ffffff;
     --border-color: #d9d9d9;
     --border-radius: 4px;
     --shadow-color: rgba(0, 0, 0, 0.15);
   }

   // 暗色主题
   html[data-theme='dark'] {
     --primary-color: #177ddc;
     --text-color: rgba(255, 255, 255, 0.85);
     --bg-color: #141414;
     --border-color: #434343;
     --shadow-color: rgba(0, 0, 0, 0.45);
   }

   // 使用CSS变量
   .card {
     border-radius: var(--border-radius);
     color: var(--text-color);
     background-color: var(--bg-color);
     border: 1px solid var(--border-color);
     box-shadow: 0 2px 8px var(--shadow-color);
   }

   .btn-primary {
     background-color: var(--primary-color);
     color: white;
   }
   ```

3. **主题切换实现**:
   ```typescript
   // 主题切换逻辑
   function toggleTheme(theme: 'light' | 'dark') {
     document.documentElement.setAttribute('data-theme', theme);
     localStorage.setItem('theme', theme);
   }

   // 初始化主题
   function initTheme() {
     const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' || 'light';
     toggleTheme(savedTheme);
   }
   ```

## Mixins与函数

### Less Mixins

```less
// 文本省略
.text-ellipsis() {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 多行文本省略
.multi-line-ellipsis(@lines: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
}

// 示例使用
.card-title {
  .text-ellipsis();
}

.card-description {
  .multi-line-ellipsis(3);
}
```

## 响应式设计

### 媒体查询断点

```less
// 断点变量
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// 使用媒体查询
@media (max-width: @screen-md) {
  .container {
    width: 100%;
    padding: 0 15px;
  }
}
```

### 响应式布局

1. **灵活单位**:
   ```css
   .container {
     width: 100%;
     max-width: 1200px;
     padding: 0 15px;
   }

   .text {
     font-size: 1rem;
     line-height: 1.5;
   }
   ```

2. **栅格系统**:
   ```html
   <a-row>
     <a-col :xs="24" :sm="12" :md="8" :lg="6">
       <!-- 响应式布局内容 -->
     </a-col>
   </a-row>
   ```

## 浏览器兼容性

1. **使用PostCSS Autoprefixer**:
   ```css
   /* 编写 */
   .example {
     display: flex;
     transition: transform 1s;
   }

   /* 输出 */
   .example {
     display: -webkit-box;
     display: -ms-flexbox;
     display: flex;
     -webkit-transition: -webkit-transform 1s;
     transition: -webkit-transform 1s;
     transition: transform 1s;
     transition: transform 1s, -webkit-transform 1s;
   }
   ```

2. **特定浏览器样式**:
   ```css
   /* 尽量避免使用浏览器前缀，交给Autoprefixer处理 */
   @supports (display: grid) {
     .layout {
       display: grid;
     }
   }
   ```

## StyleLint规则

项目使用StyleLint确保样式代码质量，关键规则:

1. 缩进使用2个空格
2. 使用双引号
3. 块之间需要空行
4. 属性声明顺序遵循特定规则
5. 选择器命名使用kebab-case

完整规则见项目根目录的`stylelint.config.js`文件。

## 性能优化

1. **减少选择器复杂度**:
   ```css
   /* 不推荐 - 复杂选择器 */
   .header .navigation ul li a.active span {}

   /* 推荐 - 简单直接的选择器 */
   .nav-link-active {}
   ```

2. **避免过度使用通配符**:
   ```css
   /* 不推荐 - 性能较差 */
   .container * {}

   /* 推荐 - 明确指定元素 */
   .container > .item {}
   ```

3. **使用CSS属性简写**:
   ```css
   /* 不推荐 - 分散的属性声明 */
   .element {
     margin-top: 10px;
     margin-right: 20px;
     margin-bottom: 10px;
     margin-left: 20px;
   }

   /* 推荐 - 简写形式 */
   .element {
     margin: 10px 20px;
   }
   ```

4. **避免重复的样式声明**:
   ```css
   /* 不推荐 - 重复声明 */
   .button-primary {
     color: white;
     background-color: blue;
     padding: 10px 15px;
   }

   .button-secondary {
     color: white;
     background-color: green;
     padding: 10px 15px;
   }

   /* 推荐 - 抽象共同样式 */
   .button {
     color: white;
     padding: 10px 15px;
   }

   .button-primary {
     background-color: blue;
   }

   .button-secondary {
     background-color: green;
   }
   ```

## 常见问题与解决方案

1. **全局污染**:
   - **问题**: 样式泄露影响其他组件
   - **原因**: 未使用作用域隔离或命名冲突
   - **解决方案**:
     - 使用`scoped`属性隔离组件样式
     - 使用CSS Modules实现局部作用域
     - 采用BEM命名规范避免命名冲突
     - 使用前缀区分不同模块的样式

2. **权重过高**:
   - **问题**: 使用`!important`或过深的嵌套导致样式难以覆盖
   - **原因**: 选择器特异性过高或滥用`!important`
   - **解决方案**:
     - 避免使用`!important`
     - 减少选择器嵌套层级（最多3层）
     - 使用类选择器而非ID选择器
     - 按功能组织样式，避免特异性战争

3. **样式覆盖困难**:
   - **问题**: 第三方组件样式难以覆盖
   - **原因**: 组件库使用了高特异性选择器或内联样式
   - **解决方案**:
     - 使用`:deep()`深度选择器（Vue 3）
     - 使用`::v-deep`（Vue 2）
     - 创建全局覆盖样式（谨慎使用）
     - 利用第三方库提供的主题定制功能

4. **响应式布局问题**:
   - **问题**: 在不同设备上显示不一致
   - **原因**: 未正确使用响应式设计技术
   - **解决方案**:
     - 使用相对单位（rem, em, %）而非固定像素
     - 使用媒体查询适配不同屏幕尺寸
     - 采用移动优先的设计方法
     - 使用Flexbox或Grid进行灵活布局

5. **CSS体积膨胀**:
   - **问题**: 样式文件过大影响加载性能
   - **原因**: 未移除未使用的CSS或重复代码
   - **解决方案**:
     - 使用PurgeCSS移除未使用的样式
     - 拆分样式文件按需加载
     - 提取公共样式避免重复
     - 使用CSS压缩工具减小文件体积
