<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xh-boot-common</artifactId>
        <groupId>com.xinghuo.xace</groupId>
        <version>2.1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xh-common-sms</artifactId>

    <dependencies>
        <!--
                <dependency>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </dependency>
               <dependency>-->
<!--            <groupId>com.tencentcloudapi</groupId>-->
<!--            <artifactId>tencentcloud-sdk-java</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.aliyun</groupId>-->
<!--            <artifactId>dysmsapi20170525</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>dingtalk-sdk-java</groupId>-->
<!--            <artifactId>taobao-sdk-java</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>dingtalk-sdk-java</groupId>-->
<!--            <artifactId>taobao-sdk-java-source</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-common-core</artifactId>
            <version>${xace.version}</version>
        </dependency>
    </dependencies>

</project>
