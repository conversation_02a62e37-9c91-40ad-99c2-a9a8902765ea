package com.bstek.ureport.console.config;

import com.bstek.ureport.model.MultiTenantType;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
public class DataSourceConfig {

    private static Logger logger = LoggerFactory.getLogger(DataSourceConfig.class);

    //判断是否为多租户
    @Value("${config.MultiTenancy}")
    private String multiTenancy;
    /**
     * 多租户模式
     */

    @Value("${config.MultiTenantType}")
    private MultiTenantType multiTenantType;
    /**
     * 多租户字段
     */
    @Value("${config.MultiTenantColumn:F_TenantId}")
    private String multiTenantColumn;
    //表空间
    @Value("${spring.tableSpace}")
    private String tableSpace;
    //驱动包
    @Value("${spring.datasource.druid.driver-class-name}")
    private String driverClassName;
    //数据连接字符串
    @Value("${spring.datasource.druid.url}")
    private String url;
    //账号
    @Value("${spring.datasource.druid.username}")
    private String userName;
    //密码
    @Value("${spring.datasource.druid.password}")
    private String password;

    //库名
    @Value("${spring.datasource.druid.dbname}")
    private String dbName;
    //初始库名
    @Value("${spring.datasource.druid.dbinit}")
    private String dbInit;
    //空库名
    @Value("${spring.datasource.druid.dbnull}")
    private String dbNull;

    public boolean isMultiTenancy(){
        return "true".equals(this.multiTenancy);
    }

    public boolean isMultiTenancyColumn(){
        return isMultiTenancy() && MultiTenantType.COLUMN.eq(this.multiTenantType);
    }

}
