# xace-service 核心技术栈与架构

## 核心技术栈

* **语言:** Java
* **构建工具:** Maven
* **主要框架:**
  * Spring Boot3
  * MybatisPlus (用于数据持久化)
  * Lombok (用于简化代码)
  * Spring Security (用于安全认证)
  * Spring Cloud (微服务支持)

## 项目架构

* **多模块 Maven 项目架构**
  * `xh-admin` - 系统管理与业务主模块
  * `xh-system` - 系统功能支持模块
  * `xh-oauth` - 身份验证与授权模块
  * `xh-file` - 文件管理模块
  * `xh-workflow-engine` - 工作流引擎模块
  * `xh-visualdev` - 可视化开发模块
  * `xh-ext-*` - 扩展功能模块

## 环境要求

* **JDK:** 17及以上版本
* **Maven:** 3.6.3及以上版本
* **数据库:** MySQL 5.7+/SQLServer 2012+/Oracle 11g+/PostgreSQL 12+
* **Redis:** 5.0+（用于缓存和会话管理）

## 重要技术迁移说明

### Jakarta EE 规范（JDK 17+）

由于项目使用 **JDK 17+ 和 Spring Boot 3.x**，必须使用 **Jakarta EE** 规范，不能使用旧的 **Java EE** 规范。

#### ⚠️ 关键导入包变更

**❌ 错误的导入（Java EE）：**
```java
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.servlet.http.HttpServletRequest;
import javax.persistence.Entity;
```

**✅ 正确的导入（Jakarta EE）：**
```java
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.persistence.Entity;
```

#### 常用包名映射

| 功能 | 旧包名 (Java EE) | 新包名 (Jakarta EE) |
|------|------------------|---------------------|
| 参数校验 | `javax.validation.*` | `jakarta.validation.*` |
| Servlet API | `javax.servlet.*` | `jakarta.servlet.*` |
| JPA | `javax.persistence.*` | `jakarta.persistence.*` |
| Bean Validation | `javax.validation.constraints.*` | `jakarta.validation.constraints.*` |
| JSON-B | `javax.json.*` | `jakarta.json.*` |
| JAX-RS | `javax.ws.rs.*` | `jakarta.ws.rs.*` |

#### 开发注意事项

1. **新建文件时**：确保使用 `jakarta.*` 包名
2. **代码审查时**：检查是否有遗留的 `javax.*` 导入
3. **IDE 配置**：配置 IDE 自动导入 `jakarta.*` 包
4. **依赖管理**：确保所有依赖都支持 Jakarta EE

## 项目内部包导入规范

### 分页基类导入

**✅ 正确的导入：**
```java
import com.xinghuo.common.base.model.Pagination;
```

**❌ 错误的导入：**
```java
import com.xinghuo.common.base.Pagination;  // 错误！缺少 model 包
```

#### 常用项目内部包导入

| 功能 | 正确导入路径 | 错误导入路径 |
|------|-------------|-------------|
| 分页基类 | `com.xinghuo.common.base.model.Pagination` | `com.xinghuo.common.base.Pagination` |
| 时间分页基类 | `com.xinghuo.common.base.PaginationTime` | - |
| 基础服务接口 | `com.xinghuo.common.base.service.BaseService` | - |
| 基础服务实现 | `com.xinghuo.common.base.service.BaseServiceImpl` | - |
| 基础Mapper | `com.xinghuo.common.base.dao.XHBaseMapper` | - |

## IDEA 推荐插件

* `Lombok`(必须)
* `Alibaba Java Coding Guidelines`
* `MybatisX`
* `SonarLint`（代码质量检查）
* `Maven Helper`（依赖管理）
* `Spring Boot Assistant`（Spring配置辅助）

## 开发规范要点

* 使用统一的代码格式化配置
* 遵循阿里巴巴Java开发手册规范
* 使用统一的异常处理机制
* 遵循RESTful API设计规范
* 编写完整的单元测试

## 参考链接

* [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
* [MyBatis-Plus 官方文档](https://baomidou.com/)
* [Lombok 官方文档](https://projectlombok.org/features/)
* [Spring Security 官方文档](https://spring.io/projects/spring-security)
* [阿里巴巴Java开发手册](https://github.com/alibaba/p3c)
