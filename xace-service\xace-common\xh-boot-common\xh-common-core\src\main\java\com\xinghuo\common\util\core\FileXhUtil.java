package com.xinghuo.common.util.core;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.file.PathUtil;
import com.xinghuo.common.util.security.XSSEscape;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件处理工具类  继承hutool的FileUtil
 * 扩展 MultipartFile的相关类
 *
 * <AUTHOR>
 */
@Slf4j
public class FileXhUtil extends FileUtil {

    // 成员变量
    private static final int BUFFER_SIZE = 2 * 1024;

    /**
     * 判断是否为格式且不为空
     */
    public static boolean existsSuffix(MultipartFile multipartFile, String type) {
        return !Objects.requireNonNull(multipartFile.getOriginalFilename()).endsWith("." + type) || multipartFile.getSize() < 1;
    }
    /**
     * 导入生成临时文件后，获取文件内容
     *
     * @param multipartFile 文件
     */
    public static String getFileContent(MultipartFile multipartFile) {
        try (InputStream is = multipartFile.getInputStream()) {
            // 使用 Hutool 的 IoUtil 读取 InputStream 内容，默认使用 UTF-8 编码
            return IoUtil.read(is, StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("读取文件内容时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 判断文件类型是否被允许。
     *
     * @param fileType  允许的文件类型
     * @param fileExtension 文件扩展名
     * @return 布尔值
     */
    public static boolean fileType(String fileType, String fileExtension) {
        String[] allowExtension = fileType.split(",");
        return Arrays.asList(allowExtension).contains(fileExtension.toLowerCase());
    }

    /**
     * 检查文件大小是否超出允许的最大值。
     *
     * @param fileSize 文件大小
     * @param maxSize  最大文件大小
     * @return 布尔值
     */
    public static boolean fileSize(Long fileSize, int maxSize) {
        return fileSize > maxSize;
    }

    /**
     * 复制文件。
     *
     * @param fromFile 源文件路径
     * @param toFile   目标文件路径
     * @return 布尔值
     */
    public static boolean copyFile(String fromFile, String toFile) {
        try {
            // 将String路径转换为Path对象
            java.nio.file.Path fromPath = Paths.get(XSSEscape.escapePath(fromFile));
            java.nio.file.Path toPath = Paths.get(XSSEscape.escapePath(toFile));

            // 使用PathUtil.copy进行文件复制，这里使用REPLACE_EXISTING选项作为示例，根据需要可以调整
            PathUtil.copy(fromPath, toPath, StandardCopyOption.REPLACE_EXISTING);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 压缩文件夹到ZIP文件。
     *
     * @param outPath 压缩文件的输出路径。
     * @param srcDirs 要压缩的文件夹路径数组。
     * @param keepDirStructure 是否保留目录结构。
     */
    public static void toZip(String outPath, boolean keepDirStructure, String... srcDirs) {
        // 确保输出路径是安全的
        String safeOutPath = XSSEscape.escapePath(outPath);
        try (OutputStream out = new FileOutputStream(safeOutPath);
             ZipOutputStream zos = new ZipOutputStream(out)) {

            for (String srcDir : srcDirs) {
                // 确保源文件夹路径是安全的
                String safeSrcDir = XSSEscape.escapePath(srcDir);
                File sourceFile = new File(safeSrcDir);
                if (sourceFile.exists() && sourceFile.isDirectory()) {
                    compress(sourceFile, zos, sourceFile.getName(), keepDirStructure);
                } else {
                    log.error("源文件夹不存在或不是目录: {}", srcDir);
                }
            }
        } catch (IOException e) {
            log.error("创建ZIP文件时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("zip error from ZipUtils", e);
        }
    }

    /**
     * 递归压缩文件或目录。
     *
     * @param sourceFile 要压缩的文件或目录。
     * @param zos        zip输出流。
     * @param name       压缩条目的名称。
     * @param keepDirStructure 是否保留目录结构。
     * @throws IOException 如果压缩过程中发生IO错误。
     */
    private static void compress(File sourceFile, ZipOutputStream zos, String name, boolean keepDirStructure) throws IOException {
        if (sourceFile.isFile()) {
            zos.putNextEntry(new ZipEntry(name));
            try (FileInputStream in = new FileInputStream(sourceFile)) {
                byte[] buffer = new byte[BUFFER_SIZE];
                int len;
                while ((len = in.read(buffer)) > 0) {
                    zos.write(buffer, 0, len);
                }
            }
            zos.closeEntry();
        } else {
            File[] files = sourceFile.listFiles();
            if (files == null || files.length == 0) {
                if (keepDirStructure) {
                    zos.putNextEntry(new ZipEntry(name + "/"));
                    zos.closeEntry();
                }
            } else {
                for (File file : files) {
                    String entryName = keepDirStructure ? name + "/" + file.getName() : file.getName();
                    compress(file, zos, entryName, keepDirStructure);
                }
            }
        }
    }

    /**
     * 将输入流写入到文件。
     *
     * @param inputStream 输入流
     * @param path        文件路径
     * @param fileName    文件名
     */
    public static void write(InputStream inputStream, String path, String fileName) {
        try {
            // 确保目录存在
            File dir = FileUtil.mkdir(XSSEscape.escapePath(path));
            // 构建完整文件路径
            String newFilePath = dir.getAbsolutePath() + File.separator + fileName;
            log.info("保存文件：" + newFilePath);
            // 将输入流写入到文件
            FileUtil.writeFromStream(inputStream, newFilePath);
        } finally {
            // 关闭输入流
            IoUtil.close(inputStream);
        }
    }

    /**
     * 创建一个新文件。
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 是否成功
     */
    public static boolean createFile(String filePath, String fileName) {
        try {
            // 创建文件，如果父目录不存在，Hutool会自动创建
            FileUtil.touch(XSSEscape.escapePath(filePath) + File.separator + fileName);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 创建一个MultipartFile对象。
     *
     * @param file 文件
     * @return MultipartFile对象
     * @throws IOException IO异常
     */
    public static MultipartFile createFileItem(File file) throws IOException {
        byte[] fileBytes = FileCopyUtils.copyToByteArray(file);
        String fileName = file.getName();
        // 设置文件类型，具体类型根据实际情况调整
        String contentType = "text/plain";

        return new CustomMultipartFile(fileBytes, fileName, contentType);

    }


    /**
     * 删除临时文件。
     *
     * @param multipartFile 要删除的文件
     * @return 是否成功
     */
    public static boolean deleteTmp(MultipartFile multipartFile) {
        try {
//            CommonsMultipartFile commonsMultipartFile = (CommonsMultipartFile) multipartFile;
//            DiskFileItem diskFileItem = (DiskFileItem) commonsMultipartFile.getFileItem();
//            File storeLocation = diskFileItem.getStoreLocation();
//            FileUtil.deleteEmptyDirectory(storeLocation);
//            Spring 在处理文件上传时会自动管理临时文件，并在请求处理完毕后清理它们。大多数情况下，你不需要手动删除这些临时文件。然而，如果你确实需要这样做（比如出于某种特定的资源管理考虑），你可能需要自定义你的 MultipartResolver 并覆盖其文件存储逻辑。
            return true;
        } catch (Exception e) {
            log.error("删除tmp文件失败,错误：" + e.getMessage());
            return false;
        }
    }

    /**
     * 上传文件。
     *
     * @param file     要上传的文件
     * @param filePath 文件要保存的路径
     * @param fileName 文件名
     */
    public static void uploadFile(MultipartFile file, String filePath, String fileName) {
        try {
            // 确保目录存在
            FileUtil.mkdir(XSSEscape.escapePath(filePath));
            // 写入文件
            FileUtil.writeBytes(file.getBytes(), XSSEscape.escapePath(filePath) + File.separator + fileName);
        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }


}
