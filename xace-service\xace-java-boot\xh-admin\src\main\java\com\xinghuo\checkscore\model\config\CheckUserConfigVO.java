package com.xinghuo.checkscore.model.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-29
 */
@Data
public class CheckUserConfigVO {

    @Schema(description = "是否为设置考核人")
    private Boolean hasNoChecker;

    @Schema(description = "数据是否可编辑")
    private Boolean editAble;

    @Schema(description = "考核人员设置编辑状态")
    private Integer reviewCheckUserStatus;

    @Schema(description = "有误的数据，不需要考核的人员")
    private String errorCheckMessage;

    @Schema(description = "考核数据")
    private List<CheckUserModel> checkUserList;
}
