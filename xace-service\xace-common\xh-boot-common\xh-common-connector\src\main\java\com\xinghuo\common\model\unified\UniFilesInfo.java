package com.xinghuo.common.model.unified;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * 统一文件服务配置Model
 *
 * <AUTHOR>
 * @date 2022-07-21
 */
@Data
@Component
@ConfigurationProperties(prefix = "unifuser")
public class UniFilesInfo {
    @NotBlank(message = "必填")
    @Schema(description = "统一文件服务访问地址")
    private String url;
    @NotBlank(message = "必填")
    @Schema(description = "消费者Id")
    private String consumerId;
    @NotBlank(message = "必填")
    @Schema(description = "消费者名称")
    private String consumerName;
    @NotBlank(message = "必填")
    @Schema(description = "单个文件上传地址")
    private String singleFileUploadUrl;
    @NotBlank(message = "必填")
    @Schema(description = "根据id查询文件信息地址")
    private String searchFileInfoUrl;
    @NotBlank(message = "必填")
    @Schema(description = "根据文件信息ID批量删除文件")
    private String deleteFileUrl;

}
