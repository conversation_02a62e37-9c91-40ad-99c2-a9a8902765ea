{"groups": [{"name": "spring.datasource", "type": "com.xinghuo.common.database.util.DataSourceUtil", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}], "properties": [{"name": "spring.datasource.db-name", "type": "java.lang.String", "description": "库名", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.db-schema", "type": "java.lang.String", "description": "模式", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.db-table-space", "type": "java.lang.String", "description": "表空间", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.db-type", "type": "java.lang.String", "description": "数据库类型", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.driver", "type": "java.lang.String", "description": "驱动包", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.host", "type": "java.lang.String", "description": "主机ip", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.oracle-param", "type": "java.lang.String", "description": "oracle多方式登录参数", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.password", "type": "java.lang.String", "description": "密码 TODO 没有加密", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.port", "type": "java.lang.Integer", "description": "端口", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.prepare-url", "type": "java.lang.String", "description": "数据连接jdbc-url参数", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.url", "type": "java.lang.String", "description": "url地址", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.url-params", "type": "java.lang.String", "description": "数据连接jdbc-url参数", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}, {"name": "spring.datasource.user-name", "type": "java.lang.String", "description": "用户", "sourceType": "com.xinghuo.common.database.util.DataSourceUtil"}], "hints": []}