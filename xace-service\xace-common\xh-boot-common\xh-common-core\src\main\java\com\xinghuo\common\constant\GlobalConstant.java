package com.xinghuo.common.constant;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

public class GlobalConstant {

    /**
     * 网关、Feign转发后携带原有HOST(网关或者FEIGN转发后HOST不准确)
     */
    public static final String HEADER_HOST = "MY_HOST";

    /**
     * 默认租户字段值
     */
    public static final String DEFAULT_TENANT_VALUE = "0";

    public static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;
    public static final String DEFAULT_CHARSET_STR = DEFAULT_CHARSET.name();

    /**
     * 默认语种
     */
    public static final String DEFAULT_LANGUAGE = "zh-CN";

    /**
     * 扫包路径 多个使用封号分割
     */
    public static final String PROJECT_SCAN_PACKAGES = "com.xinghuo";
}
