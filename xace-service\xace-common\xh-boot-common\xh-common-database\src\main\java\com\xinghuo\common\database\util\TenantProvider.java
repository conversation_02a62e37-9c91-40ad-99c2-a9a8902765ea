package com.xinghuo.common.database.util;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.session.SaSessionCustomUtil;
import com.xinghuo.common.base.model.BaseSystemInfo;
import com.xinghuo.common.util.context.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;

import static com.xinghuo.common.constant.AuthConstant.DEF_TENANT_ID;
import static com.xinghuo.common.constant.AuthConstant.TENANT_SESSION;


/**
 * 租户提供者工具类 用于提供租户相关功能的方法
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Slf4j
public class TenantProvider {

    /**
     * 租户超时时间，单位为秒，默认为30天
     */
    private static final long TENANT_TIMEOUT = 60 * 60 * 24 * 30L;

    /**
     * 获取租户Redis存储对象
     *
     * @param tenantId 租户ID
     * @return SaSession对象
     */
    @SuppressWarnings("AlibabaUndefineMagicConstant")
    public static SaSession getTenantSession(String tenantId) {
        if (tenantId == null) {
            // 如果租户ID为空，则从数据源上下文中获取
            tenantId = DataSourceContextHolder.getDatasourceId();
            if (tenantId == null) {
                // 如果数据源上下文中的租户ID也为空，则使用默认租户ID
                tenantId = DEF_TENANT_ID;
            }
        }
        SaSession saSession = SaSessionCustomUtil.getSessionById(TENANT_SESSION + tenantId);
        if (saSession != null && !saSession.get("init", false)) {
            // 如果租户会话对象不为空且未初始化，则设置初始化标志为true，并更新超时时间
            saSession.set("init", true);
            saSession.updateTimeout(TENANT_TIMEOUT);
        }
        return saSession;
    }

    /**
     * 存入租户缓存空间
     *
     * @param tenantId 租户ID
     * @param key      缓存键
     * @param value    缓存值
     */
    public static void putTenantCache(String tenantId, String key, Object value) {
        SaSession saSession = getTenantSession(tenantId);
        if (saSession != null) {
            // 如果租户会话对象不为空，则存入缓存键值对，并更新超时时间
            saSession.set(key, value).updateTimeout(TENANT_TIMEOUT);
        }
    }

    /**
     * 获取租户缓存数据
     *
     * @param tenantId 租户ID
     * @param key      缓存键
     * @param <T>      缓存值类型
     * @return 缓存值
     */
    public static <T> T getTenantCache(String tenantId, String key) {
        SaSession saSession = getTenantSession(tenantId);
        if (saSession != null) {
            // 如果租户会话对象不为空，则获取缓存值
            return (T) saSession.get(key);
        }
        return null;
    }

    /**
     * 删除租户缓存数据
     *
     * @param tenantId 租户ID
     * @param key      缓存键
     */
    public static void delTenantCache(String tenantId, String key) {
        SaSession saSession = getTenantSession(tenantId);
        if (saSession != null) {
            // 如果租户会话对象不为空，则删除缓存键值对
            saSession.delete(key);
        }
    }

    /**
     * 更新租户超时时间
     *
     * @param tenantId 租户ID
     * @param timeout  超时时间，单位为秒
     */
    public static void renewTimeout(String tenantId, long timeout) {
        if (tenantId == null) {
            // 如果租户ID为空，则使用默认租户ID
            tenantId = DEF_TENANT_ID;
        }
        SaSession saSession = getTenantSession(tenantId);
        if (saSession != null) {
            // 如果租户会话对象不为空
            saSession.updateTimeout(timeout);
        }
    }

    /**
     * 线程局部变量，用于保存系统设置信息
     */
    private static ThreadLocal<BaseSystemInfo> systemInfoThreadLocal = new ThreadLocal<>();

    /**
     * 获取系统设置信息
     *
     * @return 系统设置信息对象
     */
    public static BaseSystemInfo getBaseSystemInfo() {
        BaseSystemInfo systemInfo = systemInfoThreadLocal.get();
        return systemInfo;
    }

    /**
     * 设置系统设置信息
     *
     * @param baseSystemInfo 系统设置信息对象
     */
    public static void setBaseSystemInfo(BaseSystemInfo baseSystemInfo) {
        systemInfoThreadLocal.set(baseSystemInfo);
    }

    /**
     * 清除系统设置信息
     */
    public static void clearBaseSystemIfo() {
        systemInfoThreadLocal.remove();
    }


}
