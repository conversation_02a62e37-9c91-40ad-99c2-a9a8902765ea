<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="FacetManager">
    <facet type="JRebel" name="JRebel">
      <configuration>
        <option name="ideModuleStorage">
          <map>
            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
            <entry key="jrebelEnabled" value="true" />
            <entry key="lastExternalPluginCheckTime" value="1744072076274" />
            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD54aC11cmVwb3J0Mi1jb3JlPC9pZD48Y2xhc3NwYXRoPjxkaXIgbmFtZT0iRzovdjIvcGQteGFjZS12Mi94YWNlLXNlcnZpY2UveGFjZS1kYXRhcmVwb3J0L3hoLXVyZXBvcnQyLWNvcmUvdGFyZ2V0L2NsYXNzZXMiPjwvZGlyPjwvY2xhc3NwYXRoPjwvYXBwbGljYXRpb24+" />
          </map>
        </option>
        <option name="version" value="19" />
      </configuration>
    </facet>
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
  </component>
</module>