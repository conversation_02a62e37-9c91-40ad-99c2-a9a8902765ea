# 项目结构与文件命名规范

## 目录结构

```
xace-web-vue3/
├── build/                  # 构建相关配置
├── public/                 # 静态资源
├── src/
│   ├── api/                # 后端API接口定义
│   │   ├── model/          # API相关的数据模型
│   │   └── modules/        # 按模块划分的API
│   ├── assets/             # 项目资源文件
│   │   ├── icons/          # 图标资源
│   │   ├── images/         # 图片资源
│   │   └── styles/         # 全局样式
│   ├── components/         # 公共组件
│   │   ├── Basic/          # 基础UI组件
│   │   ├── Form/           # 表单相关组件
│   │   ├── Table/          # 表格相关组件
│   │   └── Xh/             # 自定义业务组件
│   ├── directives/         # 自定义指令
│   ├── enums/              # 枚举定义
│   ├── hooks/              # 业务逻辑钩子
│   │   ├── core/           # 核心钩子函数
│   │   ├── event/          # 事件相关钩子
│   │   └── web/            # Web API相关钩子
│   ├── layouts/            # 布局组件
│   ├── locales/            # 国际化资源
│   ├── router/             # 路由配置
│   │   ├── guard/          # 路由守卫
│   │   ├── modules/        # 路由模块
│   │   └── helper/         # 路由辅助函数
│   ├── store/              # 状态管理
│   │   └── modules/        # 状态模块
│   ├── utils/              # 工具函数
│   │   ├── auth/           # 认证相关
│   │   ├── cache/          # 缓存相关
│   │   ├── http/           # HTTP请求相关
│   │   └── helper/         # 辅助函数
│   ├── views/              # 页面组件（按业务模块划分）
│   ├── App.vue             # 根组件
│   └── main.ts             # 入口文件
├── types/                  # 全局类型定义
├── .env                    # 环境变量
├── .env.development        # 开发环境变量
├── .env.production         # 生产环境变量
├── .env.test               # 测试环境变量
├── .eslintrc.js            # ESLint配置
├── .prettierrc             # Prettier配置
├── stylelint.config.js     # StyleLint配置
├── tsconfig.json           # TypeScript配置
├── vite.config.ts          # Vite配置
└── package.json            # 项目依赖
```

## 命名规范

### 文件与文件夹命名

1. **组件文件:**
   - 使用PascalCase命名: `UserProfile.vue`, `DataTable.vue`
   - 基础组件使用`Base`前缀: `BaseButton.vue`, `BaseInput.vue`
   - 单例组件使用`The`前缀: `TheHeader.vue`, `TheFooter.vue`
   - 业务组件可使用`Biz`前缀: `BizProjectCard.vue`, `BizUserSelector.vue`

2. **工具/Hook文件:**
   - 使用camelCase命名: `useUserState.ts`, `formatDate.ts`
   - Hook函数使用`use`前缀: `useRequest.ts`, `usePermission.ts`
   - 工具函数使用动词开头: `formatTime.ts`, `validateEmail.ts`

3. **类型定义文件:**
   - 使用与相关模块相同的名称，加后缀`.d.ts`: `user.d.ts`, `api.d.ts`
   - 全局类型定义放在`types`文件夹中
   - 模块特定类型定义放在相应模块的`types.ts`文件中

4. **API服务文件:**
   - 使用模块名称: `userApi.ts`, `projectApi.ts`
   - 统一放在`api/modules`目录下

5. **视图文件夹:**
   - 使用kebab-case命名: `user-management/`, `project-list/`
   - 按业务模块组织，避免过深的嵌套

### 组件命名

1. **组件名:**
   - 使用PascalCase: `UserProfile`, `ProjectTable`
   - 多个单词组成，避免单个单词
   - 与文件名保持一致
   - 名称应当表明组件的用途

2. **Props命名:**
   - 使用camelCase: `tableData`, `userInfo`
   - 避免缩写和单个字符
   - 布尔类型props使用`is/has/should`等前缀: `isVisible`, `hasHeader`

3. **事件名:**
   - 使用kebab-case: `change`, `update:modelValue`, `item-click`
   - 使用动词表示动作: `click`, `submit`, `select`

4. **自定义事件:**
   - 使用`on`前缀: `on-click`, `on-change`, `on-success`
   - 事件处理函数使用`handle`前缀: `handleClick`, `handleSubmit`

### 变量命名

1. **常量:**
   - 使用全大写和下划线: `MAX_COUNT`, `API_URL`
   - 模块级常量放在单独的`constants.ts`文件中

2. **变量:**
   - 使用camelCase: `userData`, `projectList`
   - 布尔类型使用`is/has/should`等前缀: `isLoading`, `hasPermission`
   - 数组使用复数形式: `users`, `projects`

3. **私有属性/方法:**
   - 使用下划线前缀: `_privateMethod()`, `_internalData`
   - 或使用TypeScript的private修饰符

### 样式命名

1. **类名:**
   - 使用kebab-case: `user-card`, `data-table`
   - 功能前缀: `btn-primary`, `text-center`
   - 使用BEM命名法则: `block__element--modifier`

2. **组件作用域类名:**
   - 使用scoped样式或CSS Modules避免全局污染
   - 组件前缀: `.comp-user-card`, `.view-dashboard`

3. **变量命名:**
   - 使用kebab-case: `--primary-color`, `--font-size-large`
   - 按功能分类: `--color-primary`, `--spacing-large`

## 模块划分

按功能域划分模块，同一业务功能的组件、API、类型、工具等放在一起，如:

```
views/project-management/
├── components/           # 该功能特有组件
│   ├── ProjectCard.vue
│   └── ProjectForm.vue
├── hooks/                # 该功能特有hooks
│   └── useProjectData.ts
├── api/                  # 可选，模块特定API
│   └── project.ts
├── constants.ts          # 常量定义
├── types.ts              # 类型定义
├── utils.ts              # 工具函数
├── list.vue              # 列表页面
├── detail.vue            # 详情页面
└── index.vue             # 入口页面
```

## 导入顺序规范

组件导入顺序应当遵循:

1. 外部库导入
2. 类型导入
3. API导入
4. 组件导入
5. 工具/Hook导入
6. 常量/枚举导入
7. 资源导入
8. 样式导入

```typescript
// 1. 外部库导入
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// 2. 类型导入
import type { UserInfo } from '/@/types/user';

// 3. API导入
import { getUserInfo } from '/@/api/modules/user';

// 4. 组件导入
import UserAvatar from '/@/components/Xh/UserAvatar.vue';

// 5. 工具/Hook导入
import { usePermission } from '/@/hooks/usePermission';
import { formatDate } from '/@/utils/formatDate';

// 6. 常量/枚举导入
import { UserStatus } from '/@/enums/user';
import { MAX_FILE_SIZE } from '/@/constants';

// 7. 资源导入
import defaultAvatar from '/@/assets/images/default-avatar.png';

// 8. 样式导入
import styles from './index.module.less';
```

## 代码组织原则

1. **单一职责原则**
   - 每个组件、函数、类只负责一个功能
   - 组件过大时应拆分为多个小组件

2. **可复用性原则**
   - 抽取通用逻辑到hooks或工具函数
   - 抽取通用UI到基础组件

3. **可测试性原则**
   - 纯函数优先，便于单元测试
   - 关注点分离，UI与业务逻辑分离
