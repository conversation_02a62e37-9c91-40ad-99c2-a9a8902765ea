package com.bstek.ureport.console.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.incrementer.IKeyGenerator;
import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.incrementer.H2KeyGenerator;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.bstek.ureport.console.config.datasource.DataSourceContextHolder;
import com.bstek.ureport.console.util.DbTableConModel;
import com.bstek.ureport.console.util.JdbcUtil;
import com.bstek.ureport.utils.TenantLineSqlParseUtil;
import org.apache.ibatis.logging.slf4j.Slf4jImpl;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import javax.sql.DataSource;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@Configuration
@EnableConfigurationProperties(SysConfig.class)
@MapperScan(basePackages = {"com.bstek.ureport.console.ureport.mapper"}, sqlSessionTemplateRef = "sqlSessionTemplate")
public class MybatisPlusConfig {
    //对接数据库的实体层
    static final String ALIASES_PACKAGE = "com.bstek.ureport.console.ureport.entity";
    @Autowired
    private DataSourceConfig datasource;

    @Primary
    @Bean(name = "dataSourceSystem")
    public DataSource dataSourceOne(Environment env) {
        String prefix = "spring.datasource.druid.";
        return druidDataSource(env, prefix);
    }

    @Bean(name = "sqlSessionFactorySystem")
    public SqlSessionFactory sqlSessionFactoryOne(@Qualifier("dataSourceSystem") DataSource dataSource) throws Exception {
        return createSqlSessionFactory(dataSource);
    }

    @Bean("myTenantLineInnerInterceptor")
    public TenantLineInnerInterceptor myTenantLineInnerInterceptor(){
        TenantLineSqlParseUtil tenantLineInnerInterceptor = new TenantLineSqlParseUtil();
        return tenantLineInnerInterceptor;
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        //判断是否多租户
        if (datasource.isMultiTenancy()) {
            if(datasource.isMultiTenancyColumn()){
                TenantLineInnerInterceptor tenantLineInterceptor = myTenantLineInnerInterceptor();
                interceptor.addInnerInterceptor(tenantLineInterceptor);
            }else {
                DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
                HashMap<String, TableNameHandler> map = new HashMap<>(150);
                String url = datasource.getUrl().replace("{dbName}", datasource.getDbInit());
                Connection conn = JdbcUtil.getConn(datasource.getUserName(), datasource.getPassword(), url);
                List<DbTableConModel> dbTableModels = new ArrayList<>();
                if (conn != null) {
                    dynamicTableNameInnerInterceptor.setTableNameHandler((sql, tableName) -> {
                        String schema = DataSourceContextHolder.getDatasourceName();
                        if (datasource.getDriverClassName().toLowerCase().contains(DbType.MYSQL.getDb())) {
                            return schema + "." + tableName;
                        } else if (datasource.getDriverClassName().toLowerCase().contains(DbType.SQL_SERVER.getDb())) {
                            return schema + ".dbo." + tableName;
                        } else if (datasource.getDriverClassName().toLowerCase().contains(DbType.POSTGRE_SQL.getDb())) {
                            return schema.toLowerCase() + "." + tableName;
                        } else if (datasource.getDriverClassName().toLowerCase().contains(DbType.ORACLE.getDb())) {
                            return schema.toUpperCase() + "." + tableName;
                        }
                        return schema + "." + tableName;
                    });
                    interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);
                }
            }
        }
        //新版本分页必须指定数据库，否则分页不生效
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return interceptor;
    }


    protected DataSource druidDataSource(Environment env, String prefix) {
        String url;
        if (datasource.isMultiTenancy() && !datasource.isMultiTenancyColumn()) {
            String dbNull = env.getProperty(prefix + "dbnull");
            url = env.getProperty(prefix + "url").replace("{dbName}", dbNull);
        } else {
            String dbName = env.getProperty(prefix + "dbname");
            url = env.getProperty(prefix + "url").replace("{dbName}", dbName);
        }
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUsername(env.getProperty(prefix + "username"));
        dataSource.setUrl(url);
        dataSource.setPassword(env.getProperty(prefix + "password"));
        dataSource.setDriverClassName(env.getProperty(prefix + "driver-class-name"));
        return dataSource;
    }

    public Resource[] resolveMapperLocations() {
        ResourcePatternResolver resourceResolver = new PathMatchingResourcePatternResolver();
        List<String> mapperLocations = new ArrayList<>();
        mapperLocations.add("classpath:mapper/*/*.xml");
        List<Resource> resources = new ArrayList();
        if (mapperLocations != null) {
            for (String mapperLocation : mapperLocations) {
                try {
                    Resource[] mappers = resourceResolver.getResources(mapperLocation);
                    resources.addAll(Arrays.asList(mappers));
                } catch (IOException e) {
                    // ignore
                }
            }
        }
        return resources.toArray(new Resource[resources.size()]);
    }

    public SqlSessionFactory createSqlSessionFactory(DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setVfs(SpringBootVFS.class);
        bean.setTypeAliasesPackage(ALIASES_PACKAGE);
        bean.setMapperLocations(resolveMapperLocations());
        bean.setConfiguration(configuration());
        return bean.getObject();
    }

    public MybatisConfiguration configuration() {
        MybatisConfiguration mybatisConfiguration = new MybatisConfiguration();
        mybatisConfiguration.setMapUnderscoreToCamelCase(true);
        mybatisConfiguration.setCacheEnabled(false);
        mybatisConfiguration.addInterceptor(mybatisPlusInterceptor());
        mybatisConfiguration.setLogImpl(Slf4jImpl.class);
        mybatisConfiguration.setJdbcTypeForNull(JdbcType.NULL);
        return mybatisConfiguration;
    }

    @Bean
    public IKeyGenerator keyGenerator() {
        return new H2KeyGenerator();
    }

    @Bean
    public ISqlInjector sqlInjector() {
        return (builderAssistant, mapperClass) -> {

        };
    }

}
