package com.xinghuo.common.util.http;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xinghuo.common.util.json.JsonXhUtil;
import io.micrometer.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.entity.UrlEncodedFormEntity;
import org.apache.hc.client5.http.entity.mime.ContentBody;
import org.apache.hc.client5.http.entity.mime.MultipartEntityBuilder;
import org.apache.hc.client5.http.fluent.Form;
import org.apache.hc.client5.http.fluent.Request;
import org.apache.hc.client5.http.fluent.Response;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.ParseException;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.HttpEntities;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.springframework.http.HttpMethod;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509ExtendedTrustManager;
import java.io.File;
import java.io.IOException;
import java.net.Socket;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

/**
 * 由于引入的antisamy 使用的httpclient，所以继续使用。
 * 提供HTTP请求相关工具方法，包括文件上传和POST、GET请求等。
 *
 * <AUTHOR>
 * @date 2024-03-31
 **/
@Slf4j
public class HttpXhUtil {
    // 最大连接总数
    private static final int maxConnTotal = 200;
    // 连接超时时间
    private static final Timeout connectTimeout = Timeout.ofSeconds(10);
    // 请求超时时间
    private static final Timeout requestTimeout = Timeout.ofSeconds(30);

    public static void main(String[] args) throws Exception {
    }

    /**
     * 判断微信接口返回是否错误。
     *
     * @param jsonObject 微信接口返回的JSON对象
     * @return 如果错误代码（errcode）非0，返回true，否则返回false
     */
    public static boolean isWxError(ObjectNode jsonObject) {
        return null == jsonObject || jsonObject.get("errcode").asInt() != 0;
    }

    /**
     * 上传文件。
     *
     * @param urlString 目标URL
     * @param paramMap  参数映射，可以包含文件和文本参数
     * @return 返回HTTP响应内容
     */
    public static String uploadFile(String urlString, Map<String, Object> paramMap) throws IOException {
        return uploadFile(urlString, null, paramMap, CharsetUtil.CHARSET_UTF_8)
                .returnContent()
                .asString(CharsetUtil.CHARSET_UTF_8);
    }

    /**
     * 上传文件，指定字符集。
     *
     * @param urlString 目标URL
     * @param paramMap  参数映射，可以包含文件和文本参数
     * @param charSet   字符集
     * @return 返回HTTP响应内容
     */
    public static String uploadFile(String urlString, Map<String, Object> paramMap, Charset charSet) throws IOException {
        return uploadFile(urlString, null, paramMap, charSet)
                .returnContent()
                .asString(charSet);
    }

    /**
     * 上传文件，可指定请求头和字符集。
     *
     * @param urlString 目标URL
     * @param headerMap 请求头映射
     * @param paramMap  参数映射，可以包含文件和文本参数
     * @return 返回HTTP响应
     */
    public static Response uploadFile(String urlString, Map<String, Object> headerMap, Map<String, Object> paramMap) throws IOException {
        return uploadFile(urlString, headerMap, paramMap, CharsetUtil.CHARSET_UTF_8);
    }

    /**
     * 上传文件，可指定请求头、字符集和参数。
     *
     * @param urlString      目标URL
     * @param headerMap      请求头映射
     * @param paramMap       参数映射，可以包含文件和文本参数
     * @param charSet        字符集
     * @return 返回HTTP响应
     */
    public static Response uploadFile(String urlString, Map<String, Object> headerMap, Map<String, Object> paramMap, Charset charSet) throws IOException {
        MultipartEntityBuilder builder = MultipartEntityBuilder.create().setCharset(charSet);
        paramMap.forEach((k, v) -> {
            // 判断是文件还是文本
            if (v instanceof File) {
                File file = (File) v;
                builder.addBinaryBody(k, file, ContentType.MULTIPART_FORM_DATA.withCharset(charSet), FileUtil.getName(file));
            } else if (v instanceof ContentBody) {
                builder.addPart(k, (ContentBody) v);
            } else {
                builder.addTextBody(k, String.valueOf(v), ContentType.TEXT_PLAIN.withCharset(charSet));
            }
        });
        Request request = Request.post(urlString)
                .body(builder.build());
        // 添加消息头
        if (CollUtil.isNotEmpty(headerMap)) {
            headerMap.forEach((k, v) -> request.addHeader(k, String.valueOf(v)));
        }
        return call(request);
    }

    public static EnhancedResponse request(String urlString,String method,Map<String, Object> headerMap, Map<String, Object> paramMap,String token)  {

        if(headerMap==null)
            headerMap = new HashMap<>();
        headerMap.put("Authorization",token);
        headerMap.put("Content-Type","application/json");
        if(HttpMethod.PUT.name().equalsIgnoreCase(method))
        {
            return put(urlString,headerMap,paramMap,StandardCharsets.UTF_8);
        }else if (HttpMethod.GET.name().equalsIgnoreCase(method))
        {
            return get(urlString,headerMap,paramMap,StandardCharsets.UTF_8);
        }else if (HttpMethod.POST.name().equalsIgnoreCase(method))
        {
            return post(urlString,headerMap, JsonXhUtil.toJSONString(paramMap) ,StandardCharsets.UTF_8);
        }else if (HttpMethod.DELETE.name().equalsIgnoreCase(method))
        {
            return delete(urlString,headerMap,paramMap,StandardCharsets.UTF_8);
        }
        return get(urlString,headerMap,paramMap,StandardCharsets.UTF_8);

    }





    public static EnhancedResponse post(String urlString, String body)  {
        return post(urlString, null, body, StandardCharsets.UTF_8);
    }

    public static EnhancedResponse post(String urlString, Map<String, Object> paramMap)  {
        return post(urlString, null, paramMap, StandardCharsets.UTF_8);
    }


    /**
     * 发送POST请求，可指定请求头、字符集和参数。
     *
     * @param urlString      目标URL
     * @param headerMap      请求头映射
     * @param paramMap       参数映射
     * @param charSet        字符集
     * @return 返回HTTP响应
     */
    public static EnhancedResponse post(String urlString, Map<String, Object> headerMap, Map<String, Object> paramMap, Charset charSet)  {
        Form form = Form.form();
        if (CollUtil.isNotEmpty(paramMap)) {
            paramMap.forEach((k, v) -> form.add(k, String.valueOf(v)));
        }
        Request request = Request.post(urlString)
                .bodyForm(form.build(), charSet);
        // 添加消息头
        if (CollUtil.isNotEmpty(headerMap)) {
            headerMap.forEach((k, v) -> request.addHeader(k, String.valueOf(v)));
        }
        return new EnhancedResponse(call(request));
    }
    public static EnhancedResponse put(String urlString, Map<String, Object> headerMap, Map<String, Object> paramMap) {
        return put(urlString, headerMap, paramMap, StandardCharsets.UTF_8);
    }

    public static EnhancedResponse put(String urlString, Map<String, Object> headerMap, Map<String, Object> paramMap, Charset charSet)  {
        Form form = Form.form();
        if (CollUtil.isNotEmpty(paramMap)) {
            paramMap.forEach((k, v) -> form.add(k, String.valueOf(v)));
        }
        Request request = Request.put(urlString)
                .bodyForm(form.build(), charSet);
        // 添加消息头
        if (CollUtil.isNotEmpty(headerMap)) {
            headerMap.forEach((k, v) -> request.addHeader(k, String.valueOf(v)));
        }
        return new EnhancedResponse(call(request));
    }



    /**
     * 发送POST请求，可指定请求头、字符集和请求体内容。
     *
     * @param urlString      目标URL
     * @param headerMap      请求头映射
     * @param body           请求体内容
     * @param charSet        字符集
     * @return 返回HTTP响应
     */
    public static EnhancedResponse post(String urlString, Map<String, Object> headerMap, String body, Charset charSet)  {
        if(headerMap==null) {
            headerMap = new HashMap<>();
            headerMap.put("Content-Type", "application/json");
        }
        HttpEntity httpEntity = HttpEntities.create(body, getContentType(body, charSet));
        Request request = Request.post(urlString)
                .body(httpEntity);
        // 添加消息头
        if (CollUtil.isNotEmpty(headerMap)) {
            headerMap.forEach((k, v) -> request.addHeader(k, String.valueOf(v)));
        }
        return new EnhancedResponse(call(request));
    }

    public static EnhancedResponse post(String urlString, String body, Charset charSet,String token)  {
        HttpEntity httpEntity = HttpEntities.create(body, getContentType(body, charSet));
        Request request = Request.post(urlString)
                .body(httpEntity);
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put("Authorization",token);
        // 添加消息头
        if (CollUtil.isNotEmpty(headerMap)) {
            headerMap.forEach((k, v) -> request.addHeader(k, String.valueOf(v)));
        }
        return new EnhancedResponse(call(request));
    }


    public static EnhancedResponse put(String urlString, String body, Charset charSet,String token)  {
        HttpEntity httpEntity = HttpEntities.create(body, getContentType(body, charSet));
        Request request = Request.put(urlString)
                .body(httpEntity);
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put("Authorization",token);
         headerMap.put("Content-Type", "application/json");

        // 添加消息头
        if (CollUtil.isNotEmpty(headerMap)) {
            headerMap.forEach((k, v) -> request.addHeader(k, String.valueOf(v)));
        }
        return new EnhancedResponse(call(request));
    }


    public static EnhancedResponse get(String urlString,String token){
        if(StringUtils.isNotBlank(token)){
            Map<String, Object> headerMap = new HashMap<>();
            headerMap.put("Authorization",token);
            headerMap.put("Content-Type","application/json");
            return get(urlString,null,headerMap, StandardCharsets.UTF_8);
        }
        return get(urlString,null,null, StandardCharsets.UTF_8);
    }

    public static EnhancedResponse get(String urlString){
        return get(urlString,null,null, StandardCharsets.UTF_8);
    }

    /**
     * 发送GET请求，可指定请求头、参数映射和字符集。
     *
     * @param urlString      目标URL
     * @param headerMap      请求头映射
     * @param paramMap       参数映射
     * @param charSet        字符集
     * @return 返回HTTP响应
     */
    public static EnhancedResponse get(String urlString, Map<String, Object> headerMap, Map<String, Object> paramMap, Charset charSet)  {
        Form form = Form.form();
        if (CollUtil.isNotEmpty(paramMap)) {
            paramMap.forEach((k, v) -> form.add(k, String.valueOf(v)));
        }
        String paramStr = null;
        try {
            paramStr = EntityUtils.toString(new UrlEncodedFormEntity(form.build(), charSet));
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Request request = Request.get(urlString + '?' + paramStr);
        // 添加消息头
        if (CollUtil.isNotEmpty(headerMap)) {
            headerMap.forEach((k, v) -> request.addHeader(k, String.valueOf(v)));
        }
        return new EnhancedResponse(call(request));
    }


    public static EnhancedResponse delete(String urlString, Map<String, Object> headerMap) {
        return delete(urlString, headerMap, null, StandardCharsets.UTF_8);
    }
    public static EnhancedResponse delete(String urlString, Map<String, Object> headerMap, Map<String, Object> paramMap, Charset charSet)  {
        Form form = Form.form();
        if (CollUtil.isNotEmpty(paramMap)) {
            paramMap.forEach((k, v) -> form.add(k, String.valueOf(v)));
        }
        String paramStr = null;
        try {
            paramStr = EntityUtils.toString(new UrlEncodedFormEntity(form.build(), charSet));
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Request request = Request.delete(urlString + '?' + paramStr);
        // 添加消息头
        if (CollUtil.isNotEmpty(headerMap)) {
            headerMap.forEach((k, v) -> request.addHeader(k, String.valueOf(v)));
        }
        return new EnhancedResponse(call(request));
    }




    /**
     * 执行HTTP请求。
     *
     * @param request HTTP请求对象
     * @return 返回HTTP响应
     */
    public static Response call(Request request)  {
        try {
            return request.execute(Client.c);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据请求体内容和字符集确定请求体的Content-Type。
     *
     * @param body     请求体内容
     * @param charSet  字符集
     * @return 返回Content-Type
     */
    public static ContentType getContentType(String body, Charset charSet) {
        ContentType contentType = ContentType.TEXT_PLAIN;
        if (StrUtil.isNotBlank(body)) {
            char firstChar = body.charAt(0);
            switch (firstChar) {
                case '{':
                case '[':
                    // JSON请求体
                    contentType = ContentType.APPLICATION_JSON;
                    break;
                case '<':
                    // XML请求体
                    contentType = ContentType.APPLICATION_XML;
                    break;
                default:
                    break;
            }
        }
        if (charSet != null) {
            contentType.withCharset(charSet);
        }
        return contentType;
    }

    /**
     * 定义客户端类，用于创建并管理HTTP连接。
     */
    private static class Client {
        /**
         * 创建并初始化一个CloseableHttpClient实例。
         */
        private static final CloseableHttpClient c = HttpClientBuilder.create()
                .setConnectionManager(PoolingHttpClientConnectionManagerBuilder.create()
                        .setSSLSocketFactory(getSSLFactory())
                        .setValidateAfterInactivity(TimeValue.ofSeconds(10))
                        .setMaxConnPerRoute(maxConnTotal - 1)
                        .setMaxConnTotal(maxConnTotal)
                        .build())
                .evictIdleConnections(TimeValue.ofMinutes(1))
                .disableAutomaticRetries()
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setConnectTimeout(connectTimeout)
                        .setConnectionRequestTimeout(requestTimeout)
                        .build())
                .build();

        /**
         * 创建并初始化一个SSLConnectionSocketFactory实例。
         *
         * @return 返回SSLConnectionSocketFactory实例
         */
        private static SSLConnectionSocketFactory getSSLFactory() {
            X509ExtendedTrustManager trustManager = new X509ExtendedTrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] x509Certificates, String s, Socket socket) {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] x509Certificates, String s, Socket socket) {
                }

                @Override
                public void checkClientTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) {
                }

                @Override
                public void checkClientTrusted(X509Certificate[] arg0, String arg1) {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] arg0, String arg1) {
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
            };
            SSLContext ctx = null;
            try {
                ctx = SSLContext.getInstance("TLS");
                ctx.init(null, new TrustManager[]{trustManager}, null);
            } catch (NoSuchAlgorithmException | KeyManagementException e) {
                e.printStackTrace();
            }
            assert ctx != null;
            return new SSLConnectionSocketFactory(ctx, NoopHostnameVerifier.INSTANCE);
        }
    }
}