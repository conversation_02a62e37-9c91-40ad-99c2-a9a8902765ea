package com.xinghuo.common.database.plugins;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.xinghuo.common.database.util.DynamicDataSourceUtil;
import com.xinghuo.common.database.util.NotTenantPluginHolder;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.context.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.*;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
/**
 * Column模式租户插件
 * 该类是 MyBatis-Plus 的租户插件的一个扩展，主要在原有的 TenantLineInnerInterceptor 基础上进行了一些自定义的逻辑。
 *
 * @see TenantLineInnerInterceptor
 * @see DynamicDataSourceUtil
 * @see NotTenantPluginHolder
 *
 * @Slf4j 用于简化日志记录
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Slf4j
public class MyTenantLineInnerInterceptor extends TenantLineInnerInterceptor {

    /**
     * 在执行查询前的操作。
     *
     * @param executor Executor 实例
     * @param ms MappedStatement 实例
     * @param parameter 查询参数
     * @param rowBounds 分页信息
     * @param resultHandler 结果处理器
     * @param boundSql 绑定 SQL
     * @throws SQLException SQL 异常
     */
    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        // 租户指定数据源不处理
        if (DataSourceContextHolder.isAssignDataSource()) {
            return;
        }
        if (NotTenantPluginHolder.isNotSwitch()) {
            NotTenantPluginHolder.clearNotSwitchFlag();
            return;
        }
        // 非主库不切库
        if (!DynamicDataSourceUtil.isPrimaryDataSoure()) {
            return;
        }
        // 不绑定数据源的接口不切库
        /*if (NotTenantPluginHolder.isNotSwitchAlways()) {
            return;
        }*/
        if (StrXhUtil.isEmpty(DataSourceContextHolder.getDatasourceId())) {
            log.debug("未获取到线程租户ID, 跳过切库");
            return;
        }
        try {
            super.beforeQuery(executor, ms, parameter, rowBounds, resultHandler, boundSql);
        } catch (Exception e) {
            // 特殊语句解析失败
            if (log.isDebugEnabled()) {
                log.debug("语句解析失败", e);
            }
        }
    }

    /**
     * 在准备语句之前的操作。
     *
     * @param sh StatementHandler 实例
     * @param connection 数据库连接
     * @param transactionTimeout 事务超时时间
     */
    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        // 租户指定数据源不处理
        if (DataSourceContextHolder.isAssignDataSource()) {
            return;
        }
        if (NotTenantPluginHolder.isNotSwitch()) {
            NotTenantPluginHolder.clearNotSwitchFlag();
            return;
        }
        // 非主库不切库
        if (!DynamicDataSourceUtil.isPrimaryDataSoure()) {
            return;
        }
        // 不绑定数据源的接口不切库
        /*if (NotTenantPluginHolder.isNotSwitchAlways()) {
            return;
        }*/
        if (StrXhUtil.isEmpty(DataSourceContextHolder.getDatasourceId())) {
            log.debug("未获取到线程租户ID, 跳过切库");
            return;
        }
        try {
            super.beforePrepare(sh, connection, transactionTimeout);
        } catch (Exception e) {
            // 特殊语句解析失败
            if (log.isDebugEnabled()) {
                log.debug("语句解析失败", e);
            }
        }
    }

    /**
     * 获取带别名的租户字段列。
     *
     * @param table 表
     * @return 带别名的租户字段列
     */
    @Override
    protected Column getAliasColumn(Table table) {
        return getAliasColumnWithFromItem(table);
    }

    /**
     * 根据 FromItem 获取带别名的租户字段列。
     *
     * @param table FromItem 实例
     * @return 带别名的租户字段列
     */
    protected Column getAliasColumnWithFromItem(FromItem table) {
        StringBuilder column = new StringBuilder();
        if (table.getAlias() != null) {
            column.append(table.getAlias().getName()).append(".");
        } else {
            if (table instanceof Table) {
                column.append(((Table) table).getName()).append(".");
            }
        }

        column.append(super.getTenantLineHandler().getTenantIdColumn());
        return new Column(column.toString());
    }

    /**
     * 在 Select 语句中追加租户字段。
     *
     * @param selectItems SelectItem 列表
     * @param from FromItem 实例
     */
    protected void appendSelectItem(List<SelectItem> selectItems, FromItem from) {
        if (!CollectionUtils.isEmpty(selectItems)) {
            SelectItem item = (SelectItem) selectItems.get(0);
            if (selectItems.size() == 1) {
                if (item instanceof AllColumns || item instanceof AllTableColumns) {
                    return;
                }
            }
            selectItems.add(new SelectExpressionItem(getAliasColumnWithFromItem(from)));
        }
    }

    /**
     * 处理插入选择语句，追加租户字段。
     *
     * @param selectBody SelectBody 实例
     * @param whereSegment Where 段
     */
    @Override
    protected void processInsertSelect(SelectBody selectBody, final String whereSegment) {
        PlainSelect plainSelect = (PlainSelect) selectBody;
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem instanceof Table) {
            this.processPlainSelect(plainSelect, whereSegment);
            this.appendSelectItem(plainSelect.getSelectItems(), fromItem);
        } else if (fromItem instanceof SubSelect) {
            SubSelect subSelect = (SubSelect) fromItem;
            this.appendSelectItem(plainSelect.getSelectItems(), fromItem);
            this.processInsertSelect(subSelect.getSelectBody(), whereSegment);
        }
    }
}
