package com.xinghuo.common.database.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * JdbcMapper 接口，继承自 MyBatis Plus 的 BaseMapper。
 * 该接口用于执行自定义的 SQL 查询，返回结果为 Map 类型的列表。
 *
 * 注意：此接口并不绑定具体的实体对象，因为查询结果为 Map，灵活适用于各种查询场景。
 *
 * @Mapper 注解表示该接口为 MyBatis 的映射器接口，由 MyBatis 框架扫描并生成实现类。
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Mapper
public interface JdbcMapper extends BaseMapper<Object> {

    /**
     * 执行 SQL 查询，返回 Map 类型的结果列表。
     *
     * @param sql 执行的 SQL 语句
     * @return 查询结果列表，每个元素为一个 Map，表示一条记录
     */
    List<Map<String, Object>> getList(@Param("sql") String sql);
}
