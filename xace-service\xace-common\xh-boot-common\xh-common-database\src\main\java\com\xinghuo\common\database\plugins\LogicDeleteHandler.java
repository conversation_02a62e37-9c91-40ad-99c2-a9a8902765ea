package com.xinghuo.common.database.plugins;

import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.schema.Column;

import java.util.List;

/**
 * LogicDeleteHandler 是一个用于处理逻辑删除的接口。
 *
 * 该接口定义了一些方法，用于获取租户 ID 值表达式、租户字段名、逻辑删除 SQL 语句等信息。
 *
 * 实现类需要提供适当的实现，以满足具体项目中的逻辑删除需求。
 *
 * @see Expression
 * @see Column
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public interface LogicDeleteHandler {

    /**
     * 获取未删除的值表达式。
     *
     * @return 未删除的值表达式
     */
    Expression getNotDeletedValue();

    /**
     * 获取逻辑删除字段名，默认为 "F_DELETEMARK"。
     *
     * @return 逻辑删除字段名
     */
    default String getLogicDeleteColumn() {
        return "F_DELETEMARK";
    }

    /**
     * 获取逻辑删除 SQL 语句，默认为 "UPDATE a SET F_DELETEMARK = 1"。
     *
     * @return 逻辑删除 SQL 语句
     */
    default String getDeleteSql() {
        return "UPDATE a SET F_DELETEMARK = 1";
    }

    /**
     * 根据表名判断是否忽略拼接多租户条件，默认为不忽略。
     *
     * @param tableName 表名
     * @return 是否忽略，true 表示忽略，false 表示不忽略
     */
    default boolean ignoreTable(String tableName) {
        return false;
    }

    /**
     * 判断是否忽略插入租户字段逻辑。
     *
     * @param columns        插入字段列表
     * @param tenantIdColumn 租户 ID 字段名
     * @return 是否忽略插入租户字段逻辑，true 表示忽略，false 表示不忽略
     */
    default boolean ignoreInsert(List<Column> columns, String tenantIdColumn) {
        return columns.stream().map(Column::getColumnName).anyMatch(i -> i.equalsIgnoreCase(tenantIdColumn));
    }
}
