# 配置端口
server:
  port: 30007
  max-http-header-size: 102400
  compression:
    enabled: true
    min-response-size: 102400
spring:
  # 表空间(Oracle)
  tableSpace: XACE200
  datasource:
    # MySQL配置
    druid:
      dbinit: xace200
      dbname: xace200
      dbnull: xace200
      url: ***************************/{dbName}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMultiQueries=true&serverTimezone=GMT%2B8
      username: root
      password: root
      driver-class-name: com.mysql.cj.jdbc.Driver


  # Oracle配置
  #    druid:
  #      dbname: xace200
  #      url: jdbc:oracle:thin:@************:1521:{dbName}
  #      username: sys as SYSDBA
  #      password: xace200
  #      driver-class-name: oracle.jdbc.OracleDriver

  #Redis配置
  redis:
    database: 1
    host: 127.0.0.1
    port: 6379
    password: XH12345@
    timeout: 3000
    lettuce:
      pool:
        max-active: 8 # 连接池最大连接数
        max-wait: -1ms  # 连接池最大阻塞等待时间（使用负值表示没有限制）
        min-idle: 0 # 连接池中的最小空闲连接
        max-idle: 8 # 连接池中的最大空闲连接
logging:
  level:
    root: info
    com.bstek.ureport.console: debug
  file:
    path: log/xh-report
config:
  #是否开启多租户
  MultiTenancy: false
  #数据库请求最长执行时间， 防止查询的报表数据过大导致内存溢出， -1不限制 单位：秒
  queryTimeout: 5
  #最大处理表格数据量
  maxRows: 100000
  #报表内引用的图片文件夹路径
  imgPath:
  #租户模式 COLUMN,SCHEMA 根据XACE租户模式决定
  MultiTenantType: SCHEMA
  #Column模式 租户字段
  MultiTenantColumn: F_TenantId
  #用户接口地址
  userUrl: http://127.0.0.1:30000/api/permission/Users/<USER>/ReportUserInfo
