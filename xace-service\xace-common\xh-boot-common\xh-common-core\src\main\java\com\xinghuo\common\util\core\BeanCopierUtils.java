package com.xinghuo.common.util.core;

import org.springframework.cglib.beans.BeanCopier;
import org.springframework.cglib.core.Converter;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 作为项目中BeanUtil的门面类
 * 项目中用到属性复制，必须使用该类，目前仅支持 浅拷贝。
 * 该类使用cglib的BeanCopier来实现属性复制，性能优于Spring当中的BeanUtils。
 * 需要使用spring中cglib ，支持cglib的JDK17以上的分支。
 *  该类支持属性类型相同的复制，但是属性名必须相同。扩展支持父类属性的复制。
 *  该类支持属性类型不同的复制，但是属性名必须相同。转换规则在 CGLIB_XH_CONVERTER 中定义。
 *
 * <AUTHOR>
 * @since 2.0.0
 *
 */
public class BeanCopierUtils {

    /**
     * 创建一个map来存储BeanCopier缓存
     */
    private static final Map<String, BeanCopier> BEAN_COPIER_MAP = new ConcurrentHashMap<>();

    private static final CglibXhConverter CGLIB_XH_CONVERTER = new CglibXhConverter();

    /**
     * 从源对象复制属性到目标对象。
     * 如果源对象为空，返回null。
     * 如果目标类为空，抛出NullPointerException。
     *
     * @param source 源对象
     * @param target 目标类
     * @param <T>    目标类型
     * @return 目标对象
     */
    public static <T> T copy(Object source, Class<T> target) {
        Objects.requireNonNull(target);
        return copy(source, target,null);
    }

    /**
     * 从源对象列表复制属性到目标对象列表。
     * 它会遍历源对象列表，对每个源对象调用copy方法，将其属性复制到一个新的目标对象，并将所有目标对象收集到一个列表中。
     * 如果目标类为空，那么抛出NullPointerException。
     *
     * @param sources 源对象列表
     * @param target 目标类
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     * @return 目标对象列表
     */
    public static <S, T> List<T> copyList(List<S> sources, Class<T> target) {
        Objects.requireNonNull(target);
        return sources.stream().map(src -> copy(src, target)).collect(Collectors.toList());
    }

    /**
     * 此方法用于使用转换器从源对象复制属性到目标类的新实例。
     *  转换器为系统默认的  CGLIB_XH_CONVERTER
     * 如果源对象为null，方法将返回null。
     * 如果目标类为null，将抛出NullPointerException。
     */
    public static <T> T copyUsingConvert(Object source, Class<T> target) {
        Objects.requireNonNull(target);
        return copy(source, target, CGLIB_XH_CONVERTER);
    }

    public static <S, T> List<T> copyListUsingConvert(List<S> sources, Class<T> target) {
        Objects.requireNonNull(target);
        return sources.stream().map(src -> copyUsingConvert(src, target)).collect(Collectors.toList());
    }


    /**
     *   此方法用于从源对象复制属性到目标对象。
     *
     * @param source 源对象
     * @param target 目标对象
     * @param converter 转换器
     * @return
     * @param <T>
     */
    private static <T> T copy(Object source, Class<T> target, Converter converter) {
        if (source == null) {
            return null;
        }
        Objects.requireNonNull(target);
        T result ;
        try {
            result = target.getDeclaredConstructor().newInstance();
            copy(source, result, converter);
        } catch (InstantiationException | IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            throw new RuntimeException("Failed to instantiate target object", e);
        }
        return result;
    }


     /**
     * 这个方法用于从源对象复制属性到目标对象。
     * 它首先复制源对象的超类的属性，然后复制源对象本身的属性。
     * 复制是使用cglib库中的BeanCopier类完成的。
     * BeanCopier实例在map中缓存，以优化性能。
     *
     * @param source 源对象，从中复制属性。
     * @param target 目标对象，将属性复制到此对象。
     * @param converter 转换器
     */
    private static void copy(Object source, Object target, Converter converter) {
        Class<?> parentClass = source.getClass().getSuperclass();
        boolean useConverter = Objects.nonNull(converter);
        while (parentClass != null && parentClass != Object.class) {
            BeanCopier beanCopier = getBeanCopier(parentClass, target.getClass(), useConverter);
            beanCopier.copy(source, target, converter);
            parentClass = parentClass.getSuperclass();
        }
        BeanCopier beanCopier = getBeanCopier(source.getClass(), target.getClass(), useConverter);
        beanCopier.copy(source, target, converter);
    }

    /**
     * 获取BeanCopier实例，如果Map中已经存在则直接返回，否则创建一个新的BeanCopier实例并将其缓存到Map中。
     *
     * @param sourceClass 源对象类
     * @param targetClass 目标对象类
     * @param useConverter 是否使用转换器
     * @return BeanCopier实例
     */
    private static BeanCopier getBeanCopier(Class<?> sourceClass, Class<?> targetClass, boolean useConverter) {
        String key = getKey(sourceClass, targetClass, useConverter);
        return BEAN_COPIER_MAP.computeIfAbsent(key, k -> BeanCopier.create(sourceClass, targetClass, useConverter));
    }

    /**
     * 获取Map中的Key
     *
     * @param source 源对象类
     * @param target 目标类
     * @param useConverter 是否使用转换器
     * @return 源对象与目标类名字的拼接
     */
    private static String getKey(Class<?> source, Class<?> target, boolean useConverter) {
        return source.getName() + "_" + target.getName() + "_" + useConverter;
    }

    static class CglibXhConverter implements Converter {
        /**
         *  此方法用于将给定值转换为基于其当前类型和目标类型的不同类型。
         *  1、如果值是List并且目标类型是String，它将用逗号连接列表元素并返回结果字符串。
         *  2、如果值是String并且目标类型是List，它将按逗号分割字符串并返回结果列表。
         *  3、如果值的类型与上述条件都不匹配，它将原样返回值。
         *  根据项目需求和业务场景，可以继续自定义规则，但是不建议。
         */
        @Override
        public Object convert(Object value, Class target, Object context) {
            if (value instanceof List && target == String.class) {
                return StrXhUtil.join(",", (List<?>) value);
            } else if (value instanceof String && target == List.class) {
                String stringValue = (String) value;
                return Arrays.asList(stringValue.split(","));
            }
            return value;
        }
    }
}