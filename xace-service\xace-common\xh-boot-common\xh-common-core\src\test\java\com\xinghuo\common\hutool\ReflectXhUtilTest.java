package com.xinghuo.common.hutool;

import com.xinghuo.common.util.core.ReflectXhUtil;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ReflectXhUtilTest {

    @Test
    void toStringShouldReturnNonNullValue() {
        String result = ReflectXhUtil.toString(new Object());
        assertNotNull(result);
    }

    @Test
    void toStringShouldReturnClassNameForObject() {
        String result = ReflectXhUtil.toString(new Object());
        assertTrue(result.contains("java.lang.Object"));
    }

    @Test
    void toStringShouldReturnFieldValuesForCustomObject() {
        class TestObject {
            private String field1 = "value1";
            private int field2 = 2;
        }
        String result = ReflectXhUtil.toString(new TestObject());
        assertTrue(result.contains("field1=value1"));
        assertTrue(result.contains("field2=2"));
    }

    @Test
    void convertReflectionExceptionToUncheckedShouldReturnRuntimeException() {
        Exception e = new NoSuchMethodException();
        RuntimeException runtimeException = ReflectXhUtil.convertReflectionExceptionToUnchecked(e);
        assertTrue(runtimeException instanceof RuntimeException);
    }

    @Test
    void convertReflectionExceptionToUncheckedShouldReturnIllegalArgumentException() {
        Exception e = new IllegalAccessException();
        RuntimeException runtimeException = ReflectXhUtil.convertReflectionExceptionToUnchecked(e);
        assertTrue(runtimeException instanceof IllegalArgumentException);
    }

    @Test
    void convertReflectionExceptionToUncheckedShouldReturnOriginalRuntimeException() {
        Exception e = new RuntimeException();
        RuntimeException runtimeException = ReflectXhUtil.convertReflectionExceptionToUnchecked(e);
        assertSame(e, runtimeException);
    }
}

