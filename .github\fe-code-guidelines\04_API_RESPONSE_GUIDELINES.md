# API 响应处理规范

## 1. 后台通用返回值结构

### 1.1 ActionResult 结构说明

后台统一使用 `ActionResult<T>` 作为接口返回格式：

```typescript
interface ActionResult<T> {
  code: number;     // 状态码
  msg: string;      // 返回信息
  data: T;          // 返回数据
}
```

### 1.2 成功响应格式

```typescript
// 无数据成功响应
{
  code: 200,
  msg: "操作成功",
  data: null
}

// 带数据成功响应
{
  code: 200,
  msg: "操作成功",
  data: {
    // 具体业务数据
  }
}

// 分页数据响应
{
  code: 200,
  msg: "操作成功",
  data: {
    list: [...],           // 数据列表
    pagination: {          // 分页信息
      current: 1,
      pageSize: 10,
      total: 100
    }
  }
}
```

### 1.3 失败响应格式

```typescript
// 业务错误
{
  code: 400,
  msg: "参数错误",
  data: null
}

// 系统错误
{
  code: 500,
  msg: "系统内部错误",
  data: null
}
```

## 2. 前端响应处理规范

### 2.1 API 调用封装

```typescript
// api/base.ts
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 统一的响应处理
export function handleApiResponse<T>(response: ApiResponse<T>): T {
  if (response.code === 200) {
    return response.data;
  } else {
    throw new Error(response.msg || '请求失败');
  }
}
```

### 2.2 业务 API 定义

```typescript
// api/project/business.ts
import { defHttp } from '/@/utils/http/axios';
import { ApiResponse } from '../base';

export interface BusinessModel {
  id: string;
  projectName: string;
  businessNo: string;
  // ... 其他字段
}

// ❌ 错误写法 - 直接返回 data
export function getBusinessInfo(id: string) {
  return defHttp.get<BusinessModel>({
    url: `/api/business/${id}`,
  });
}

// ✅ 正确写法 - 返回完整的 ActionResult
export function getBusinessInfo(id: string) {
  return defHttp.get<ApiResponse<BusinessModel>>({
    url: `/api/business/${id}`,
  });
}
```

### 2.3 组件中的使用

```typescript
// ❌ 错误写法
async function loadBusinessData() {
  try {
    const businessDetail = await getBusinessInfo(businessId.value);
    // 这里会出错，因为 businessDetail 是 ActionResult 结构
    businessInfo.value = businessDetail;
  } catch (error) {
    console.error('获取商机详情失败:', error);
  }
}

// ✅ 正确写法
async function loadBusinessData() {
  try {
    const response = await getBusinessInfo(businessId.value);
    // 正确处理 ActionResult 结构
    if (response.code === 200) {
      businessInfo.value = response.data;
    } else {
      createMessage.error(response.msg || '获取商机详情失败');
    }
  } catch (error) {
    console.error('获取商机详情失败:', error);
    createMessage.error('网络请求失败');
  }
}
```

## 3. 分页数据处理

### 3.1 分页响应结构

```typescript
interface PageListVO<T> {
  list: T[];
  pagination: PaginationVO;
}

interface PaginationVO {
  current: number;    // 当前页码
  pageSize: number;   // 每页大小
  total: number;      // 总记录数
}
```

### 3.2 分页数据处理示例

```typescript
// ✅ 正确的分页数据处理
async function loadPageData() {
  try {
    const response = await getBusinessList(params);
    if (response.code === 200) {
      const { list, pagination } = response.data;
      dataSource.value = list;
      setPagination({
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
      });
    }
  } catch (error) {
    console.error('加载数据失败:', error);
  }
}
```

## 4. 错误处理最佳实践

### 4.1 统一错误处理

```typescript
// utils/errorHandler.ts
export function handleApiError(error: any, defaultMsg = '操作失败') {
  if (error.response?.data?.msg) {
    createMessage.error(error.response.data.msg);
  } else if (error.msg) {
    createMessage.error(error.msg);
  } else {
    createMessage.error(defaultMsg);
  }
}
```

### 4.2 在组件中使用

```typescript
async function handleSubmit() {
  try {
    const response = await updateBusiness(businessId.value, formData);
    if (response.code === 200) {
      createMessage.success('更新成功');
      closeDrawer();
      emit('reload');
    } else {
      createMessage.error(response.msg || '更新失败');
    }
  } catch (error) {
    handleApiError(error, '更新失败');
  }
}
```

## 5. TypeScript 类型定义

### 5.1 创建类型定义文件

```typescript
// types/api.ts
export interface ActionResult<T = any> {
  code: number;
  msg: string;
  data: T;
}

export interface PageListVO<T> {
  list: T[];
  pagination: PaginationVO;
}

export interface PaginationVO {
  current: number;
  pageSize: number;
  total: number;
}

// 常用的响应类型
export type ApiResponse<T> = ActionResult<T>;
export type PageResponse<T> = ActionResult<PageListVO<T>>;
```

## 6. 常见错误及解决方案

### 6.1 直接访问 data 属性

```typescript
// ❌ 错误
const businessDetail = await getBusinessInfo(id);
console.log(businessDetail.projectName); // 可能报错

// ✅ 正确
const response = await getBusinessInfo(id);
if (response.code === 200) {
  console.log(response.data.projectName);
}
```

### 6.2 忽略错误状态码

```typescript
// ❌ 错误 - 只检查是否有数据
if (response.data) {
  // 处理数据
}

// ✅ 正确 - 检查状态码
if (response.code === 200 && response.data) {
  // 处理数据
}
```

### 6.3 分页数据处理错误

```typescript
// ❌ 错误 - 直接使用 response.data 作为列表
dataSource.value = response.data;

// ✅ 正确 - 使用 response.data.list
if (response.code === 200) {
  dataSource.value = response.data.list;
}
```

## 7. 检查清单

在处理 API 响应时，请确保：

- [ ] 检查 `response.code` 是否为 200
- [ ] 正确访问 `response.data` 获取业务数据
- [ ] 处理错误情况并显示 `response.msg`
- [ ] 分页数据使用 `response.data.list` 和 `response.data.pagination`
- [ ] 使用 TypeScript 类型定义确保类型安全
- [ ] 统一的错误处理和用户提示
