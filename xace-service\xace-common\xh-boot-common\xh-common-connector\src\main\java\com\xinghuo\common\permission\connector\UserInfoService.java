package com.xinghuo.common.permission.connector;

import java.util.Map;

/**
 * 拉取用户
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public interface UserInfoService {

    /**
     * 添加
     *
     * @param map
     */
    Boolean create(Map<String, Object> map) throws Exception;

    /**
     * 修改
     *
     * @param map
     */
    Boolean update(Map<String, Object> map);

    /**
     * 删除
     *
     * @param map
     */
    Boolean delete(Map<String, Object> map);

    /**
     * 获取信息
     *
     * @param id
     */
    Map<String, Object> getInfo(String id);

}
