package com.xinghuo.common.base.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.xinghuo.common.base.dao.XHBaseMapper;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用服务接口，继承自MyBatis-Plus的IService接口
 * @param <T> 实体类型
 *
 * <AUTHOR>
 * @date  2023-10-05
 */
public interface BaseService<T> extends IService<T> {

    /**
     * 获取对应 entity 的 BaseMapper
     *
     * @return BaseMapper
     */
    @Override
    XHBaseMapper<T> getBaseMapper();

    /**
     * 批量修改或插入实体集合，忽略逻辑删除
     *
     * @param entityList 实体对象集合
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    default boolean saveOrUpdateBatchIgnoreLogic(Collection<T> entityList) {
        return saveOrUpdateBatchIgnoreLogic(entityList, DEFAULT_BATCH_SIZE);
    }

    /**
     *   批量修改或插入实体集合，忽略逻辑删除
     *
     * @param entityList 实体对象集合
     * @param batchSize 每次的数量
     * @return
     */
    boolean saveOrUpdateBatchIgnoreLogic(Collection<T> entityList, int batchSize);

    /**
     * 根据ID删除记录，忽略逻辑删除标志
     *
     * @param id 主键ID
     * @return
     */
    default boolean removeByIdIgnoreLogic(Serializable id) {
        return SqlHelper.retBool(getBaseMapper().deleteByIdIgnoreLogic(id));
    }

    /**
     * 根据 ID 删除 忽略逻辑删除标志
     *
     * @param id      主键(类型必须与实体类型字段保持一致)
     * @param useFill 是否启用填充(为true的情况,会将入参转换实体进行delete删除)
     * @return 删除结果
     * @since 3.5.0
     */
    default boolean removeByIdIgnoreLogic(Serializable id, boolean useFill) {
        throw new UnsupportedOperationException("不支持的方法!");
    }

    /**
     * 根据实体(ID)删除 忽略逻辑删除标志
     *
     * @param entity 实体
     * @since 3.4.4
     */
    default boolean removeByIdIgnoreLogic(T entity) {
        return SqlHelper.retBool(getBaseMapper().deleteByIdIgnoreLogic(entity));
    }

    /**
     * 根据 columnMap 条件，删除记录 忽略逻辑删除标志
     *
     * @param columnMap 表字段 map 对象
     * @return
     */
    default boolean removeByMapIgnoreLogic(Map<String, Object> columnMap) {
        Assert.notEmpty(columnMap, "error: columnMap must not be empty");
        return SqlHelper.retBool(getBaseMapper().deleteByMapIgnoreLogic(columnMap));
    }

    /**
     * 根据 entity 条件，删除记录 忽略逻辑删除标志
     *
     * @param queryWrapper 实体包装类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @return
     */
    default boolean removeIgnoreLogic(Wrapper<T> queryWrapper) {
        return SqlHelper.retBool(getBaseMapper().deleteIgnoreLogic(queryWrapper));
    }

    /**
     * 删除（根据ID 批量删除） 忽略逻辑删除标志
     *
     * @param list 主键ID或实体列表
     * @return
     */
    default boolean removeByIdsIgnoreLogic(Collection<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return SqlHelper.retBool(getBaseMapper().deleteBatchIdsIgnoreLogic(list));
    }

    /**
     * 批量删除 忽略逻辑删除标志
     *
     * @param list    主键ID或实体列表
     * @param useFill 是否填充(为true的情况,会将入参转换实体进行delete删除)
     * @return 删除结果
     * @since 3.5.0
     */
    @Transactional(rollbackFor = Exception.class)
    default boolean removeByIdsIgnoreLogic(Collection<?> list, boolean useFill) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        if (useFill) {
            return removeBatchByIdsIgnoreLogic(list, true);
        }
        return SqlHelper.retBool(getBaseMapper().deleteBatchIdsIgnoreLogic(list));
    }

    /**
     * 批量删除(jdbc批量提交) 忽略逻辑删除标志
     *
     * @param list 主键ID或实体列表(主键ID类型必须与实体类型字段保持一致)
     * @return 删除结果
     * @since 3.5.0
     */
    @Transactional(rollbackFor = Exception.class)
    default boolean removeBatchByIdsIgnoreLogic(Collection<?> list) {
        return removeBatchByIdsIgnoreLogic(list, DEFAULT_BATCH_SIZE);
    }

    /**
     * 批量删除(jdbc批量提交) 忽略逻辑删除标志
     *
     * @param list    主键ID或实体列表(主键ID类型必须与实体类型字段保持一致)
     * @param useFill 是否启用填充(为true的情况,会将入参转换实体进行delete删除)
     * @return 删除结果
     * @since 3.5.0
     */
    @Transactional(rollbackFor = Exception.class)
    default boolean removeBatchByIdsIgnoreLogic(Collection<?> list, boolean useFill) {
        return removeBatchByIdsIgnoreLogic(list, DEFAULT_BATCH_SIZE, useFill);
    }

    /**
     * 批量删除(jdbc批量提交) 忽略逻辑删除标志
     *
     * @param list      主键ID或实体列表
     * @param batchSize 批次大小
     * @return 删除结果
     * @since 3.5.0
     */
    default boolean removeBatchByIdsIgnoreLogic(Collection<?> list, int batchSize) {
        throw new UnsupportedOperationException("不支持的方法!");
    }

    /**
     * 批量删除(jdbc批量提交) 忽略逻辑删除标志
     *
     * @param list      主键ID或实体列表
     * @param batchSize 批次大小
     * @param useFill   是否启用填充(为true的情况,会将入参转换实体进行delete删除)
     * @return 删除结果
     * @since 3.5.0
     */
    default boolean removeBatchByIdsIgnoreLogic(Collection<?> list, int batchSize, boolean useFill) {
        throw new UnsupportedOperationException("不支持的方法!");
    }

    /**
     * 根据 ID 选择修改 忽略逻辑删除标志
     *
     * @param entity 实体对象
     * @return
     */
    default boolean updateByIdIgnoreLogic(T entity) {
        return SqlHelper.retBool(getBaseMapper().updateByIdIgnoreLogic(entity));
    }

    /**
     * 根据 UpdateWrapper 条件，更新记录 需要设置sqlset
     *
     * @param updateWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper}
     * @return
     */
    default boolean updateIgnoreLogic(Wrapper<T> updateWrapper) {
        return updateIgnoreLogic(null, updateWrapper);
    }

    /**
     * 根据 whereEntity 条件，更新记录
     *
     * @param entity        实体对象
     * @param updateWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper}
     * @return
     */
    default boolean updateIgnoreLogic(T entity, Wrapper<T> updateWrapper) {
        return SqlHelper.retBool(getBaseMapper().updateIgnoreLogic(entity, updateWrapper));
    }

    /**
     * 根据ID 批量更新 忽略逻辑删除标志
     *
     * @param entityList 实体对象集合
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    default boolean updateBatchByIdIgnoreLogic(Collection<T> entityList) {
        return updateBatchByIdIgnoreLogic(entityList, DEFAULT_BATCH_SIZE);
    }

    /**
     * 根据ID 批量更新
     *
     * @param entityList 实体对象集合
     * @param batchSize  更新批次数量
     * @return
     */
    boolean updateBatchByIdIgnoreLogic(Collection<T> entityList, int batchSize);

    /**
     * TableId 注解存在更新记录，否插入一条记录
     *
     * @param entity 实体对象
     * @return
     */
    boolean saveOrUpdateIgnoreLogic(T entity);

    /**
     * 根据 ID 查询
     *
     * @param id 主键ID
     * @return
     */
    default T getByIdIgnoreLogic(Serializable id) {
        return getBaseMapper().selectByIdIgnoreLogic(id);
    }

    /**
     * 查询（根据ID 批量查询）
     *
     * @param idList 主键ID列表
     * @return
     */
    default List<T> listByIdsIgnoreLogic(Collection<? extends Serializable> idList) {
        return getBaseMapper().selectBatchIdsIgnoreLogic(idList);
    }

    /**
     * 查询（根据 columnMap 条件）
     *
     * @param columnMap 表字段 map 对象
     * @return
     */
    default List<T> listByMapIgnoreLogic(Map<String, Object> columnMap) {
        return getBaseMapper().selectByMapIgnoreLogic(columnMap);
    }

    /**
     * 根据 Wrapper，查询一条记录 <br/>
     * <p>结果集，如果是多个会抛出异常，随机取一条加上限制条件 wrapper.last("LIMIT 1")</p>
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @return
     */
    default T getOneIgnoreLogic(Wrapper<T> queryWrapper) {
        return getOneIgnoreLogic(queryWrapper, true);
    }

    /**
     * 根据 Wrapper，查询一条记录
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @param throwEx      有多个 result 是否抛出异常
     * @return
     */
    T getOneIgnoreLogic(Wrapper<T> queryWrapper, boolean throwEx);

    /**
     * 根据 Wrapper，查询一条记录
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @return
     */
    Map<String, Object> getMapIgnoreLogic(Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper，查询一条记录
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @param mapper       转换函数
     * @return
     */
    <V> V getObjIgnoreLogic(Wrapper<T> queryWrapper, Function<? super Object, V> mapper);

    /**
     * 查询总记录数
     *
     * @see Wrappers#emptyWrapper()
     * @return
     */
    default long countIgnoreLogic() {
        return count(Wrappers.emptyWrapper());
    }

    /**
     * 根据 Wrapper 条件，查询总记录数
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @return
     */
    default long countIgnoreLogic(Wrapper<T> queryWrapper) {
        return SqlHelper.retCount(getBaseMapper().selectCountIgnoreLogic(queryWrapper));
    }

    /**
     * 查询列表
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @return
     */
    default List<T> listIgnoreLogic(Wrapper<T> queryWrapper) {
        return getBaseMapper().selectListIgnoreLogic(queryWrapper);
    }

    /**
     * 查询所有
     *
     * @see Wrappers#emptyWrapper()
     * @return
     */
    default List<T> listIgnoreLogic() {
        return listIgnoreLogic(Wrappers.emptyWrapper());
    }

    /**
     * 翻页查询
     *
     * @param page         翻页对象
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @return
     */
    default <E extends IPage<T>> E pageIgnoreLogic(E page, Wrapper<T> queryWrapper) {
        return getBaseMapper().selectPageIgnoreLogic(page, queryWrapper);
    }

    /**
     * 无条件翻页查询
     *
     * @param page 翻页对象
     * @see Wrappers#emptyWrapper()
     * @return
     */
    default <E extends IPage<T>> E pageIgnoreLogic(E page) {
        return pageIgnoreLogic(page, Wrappers.emptyWrapper());
    }

    /**
     * 查询列表
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @return
     */
    default List<Map<String, Object>> listMapsIgnoreLogic(Wrapper<T> queryWrapper) {
        return getBaseMapper().selectMapsIgnoreLogic(queryWrapper);
    }

    /**
     * 查询所有列表
     *
     * @see Wrappers#emptyWrapper()
     * @return
     */
    default List<Map<String, Object>> listIgnoreLogicMaps() {
        return listMapsIgnoreLogic(Wrappers.emptyWrapper());
    }

    /**
     * 查询全部记录
     * @return
     */
    default List<Object> listObjsIgnoreLogic() {
        return listObjsIgnoreLogic(Function.identity());
    }

    /**
     * 查询全部记录
     *
     * @param mapper 转换函数
     * @param <V>
     * @return
     */
    default <V> List<V> listObjsIgnoreLogic(Function<? super Object, V> mapper) {
        return listObjsIgnoreLogic(Wrappers.emptyWrapper(), mapper);
    }

    /**
     * 根据 Wrapper 条件，查询全部记录
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @return
     */
    default List<Object> listObjsIgnoreLogic(Wrapper<T> queryWrapper) {
        return listObjsIgnoreLogic(queryWrapper, Function.identity());
    }

    /**
     * 根据 Wrapper 条件，查询全部记录
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @param mapper       转换函数
     * @return
     */
    default <V> List<V> listObjsIgnoreLogic(Wrapper<T> queryWrapper, Function<? super Object, V> mapper) {
        return getBaseMapper().selectObjsIgnoreLogic(queryWrapper).stream().filter(Objects::nonNull).map(mapper).collect(Collectors.toList());
    }

    /**
     * 翻页查询
     *
     * @param page         翻页对象
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     * @return
     */
    default <E extends IPage<Map<String, Object>>> E pageMapsIgnoreLogic(E page, Wrapper<T> queryWrapper) {
        return getBaseMapper().selectMapsPageIgnoreLogic(page, queryWrapper);
    }

    /**
     * 无条件翻页查询
     *
     * @param page 翻页对象
     * @see Wrappers#emptyWrapper()
     * @return
     */
    default <E extends IPage<Map<String, Object>>> E pageMapsIgnoreLogic(E page) {
        return pageMapsIgnoreLogic(page, Wrappers.emptyWrapper());
    }


    /**
     * 根据updateWrapper尝试更新，否继续执行saveOrUpdate(T)方法
     * 此次修改主要是减少了此项业务代码的代码量（存在性验证之后的saveOrUpdate操作）
     *
     *
     * @param entity 实体对象
     * @param updateWrapper
     * @return
     */
    default boolean saveOrUpdateIgnoreLogic(T entity, Wrapper<T> updateWrapper) {
        return updateIgnoreLogic(entity, updateWrapper) || saveOrUpdateIgnoreLogic(entity);
    }

}
