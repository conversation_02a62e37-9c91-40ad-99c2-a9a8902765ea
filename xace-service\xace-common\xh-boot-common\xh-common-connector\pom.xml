<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xh-boot-common</artifactId>
        <groupId>com.xinghuo.xace</groupId>
        <version>2.1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xh-common-connector</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-common-core</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>
