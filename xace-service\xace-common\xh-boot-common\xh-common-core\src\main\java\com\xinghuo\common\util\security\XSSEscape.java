package com.xinghuo.common.util.security;

import com.xinghuo.common.util.json.JsonXhUtil;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.owasp.validator.html.AntiSamy;
import org.owasp.validator.html.CleanResults;
import org.owasp.validator.html.Policy;
import org.springframework.util.ObjectUtils;

import java.io.InputStream;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 防止XSS注入的工具类
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Slf4j
public class XSSEscape {

    // 定义非法路径符号的正则表达式
    private static final Pattern PATH_PATTERN = Pattern.compile("\\.\\.|~[/\\\\]|[<]|>|\"|[*]|[|]|[?]", Pattern.CASE_INSENSITIVE);

    private static InputStream inputStream;
    private static Policy policy;
    private static Policy emptyPolicy;
    private static Policy imgOnlyBase64Policy;

    static{
        try{
            // 初始化Antisamy的策略文件
            inputStream = XSSEscape.class.getClassLoader().getResourceAsStream("antisamy-ebay.xml");
            if (inputStream != null) {
                policy = Policy.getInstance(inputStream);
                inputStream.close();
            }
            inputStream = XSSEscape.class.getClassLoader().getResourceAsStream("antisamy-ebay-imgonlybase64.xml");
            if (inputStream != null) {
                imgOnlyBase64Policy = Policy.getInstance(inputStream);
                inputStream.close();
            }
            inputStream = XSSEscape.class.getClassLoader().getResourceAsStream("antisamy-empty.xml");
            if (inputStream != null) {
                emptyPolicy = Policy.getInstance(inputStream);
                inputStream.close();
            }

        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 对给定字符串进行XSS攻击防护处理
     *
     * @param character 需要进行防护处理的字符串
     * @return 经过处理的安全字符串
     */
    public static String escape(String character) {
        return getString(character, policy);
    }

    @Nullable
    private static String getString(String character, Policy policy) {
        try {
            AntiSamy antiSamy = new AntiSamy();
            String str = character.replaceAll("&quot;", "\"");
            str = str.replaceAll("&amp;", "&");
            str = str.replaceAll("&lt;", "<");
            str = str.replaceAll("&gt;", ">");
            CleanResults scan = antiSamy.scan(str, policy);
            str = scan.getCleanHTML();
            return str;
        } catch (Exception e) {
            log.error("转换错误：" + e.getMessage());
        }
        return null;
    }

    /**
     * 对给定字符串进行XSS攻击防护处理，仅允许图片标签且以base64编码方式存在
     *
     * @param character 需要进行防护处理的字符串
     * @return 经过处理的安全字符串
     */
    public static String escapeImgOnlyBase64(String character) {
        return getString(character, imgOnlyBase64Policy);
    }

    /**
     * 对对象中字符串属性进行伪过滤处理（目前方法未完成）
     *
     * @param character 需要进行处理的对象
     * @return 处理后的对象
     */
    public static  <T> T escapeObj(T character) {
        try {
            if(ObjectUtils.isEmpty(character)){
                return character;
            }
            String str = escapeEmpty(character.toString());
            if(ObjectUtils.isEmpty(str.trim())){
                return character;
            }
            // 将字符串转换回对象，目前使用的是JsonXhUtil工具类
            return (T) JsonXhUtil.toBeanEx(str, character.getClass());
        } catch (Exception e) {
        }
        return character;
    }

    /**
     * 对字符串进行伪过滤处理（目前方法未完成）
     *
     * @param character 需要进行处理的字符串
     * @return 处理后的字符串
     */
    public static String escapeEmpty(String character) {
        try {
            AntiSamy antiSamy = new AntiSamy();
            CleanResults scan = antiSamy.scan(character, emptyPolicy);
            return scan.getCleanHTML();
        } catch (Exception e) {
        }
        return character;
    }

    /**
     * 过滤非法路径符号
     *
     * @param path 需要进行过滤的路径字符串
     * @return 过滤后的路径字符串
     */
    public static String escapePath(String path){
        Matcher matcher;
        // 通过正则表达式移除非法路径符号
        while((matcher = PATH_PATTERN.matcher(path)).find()){
            path = matcher.replaceAll("");
        }
        return escapeEmpty(path);
    }
}
