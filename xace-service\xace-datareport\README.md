# xace-datareport

> 特别说明：源码、JDK、MySQL、Redis等存放路径禁止包含中文、空格、特殊字符等

## 环境要求

> 官方建议： JDK版本不低于 `1.8.0_281`版本，可使用`OpenJDK 8`、`Alibaba Dragonwell 8`、`BiShengJDK 8`

项目  | 推荐版本                              | 说明
-----|-----------------------------------| -------------
JDK  | 1.8.0_281            | JAVA环境依赖(需配置环境变量)
Maven  | 3.6.3                             | 项目构建(需配置环境变量)
Redis  | 3.2.100(Windows)/6.0.x(Linux,Mac) |
MySQL  | 5.7.x+                            | 数据库任选一(默认)
SQLServer  | 2012+                             | 数据库任选一
Oracle  | 11g+                              | 数据库任选一
PostgreSQL  | 12+                               | 数据库任选一
达梦数据库 | DM8                               | 数据库任选一
人大金库 | KingbaseES V8 R6                  | 数据库任选一

## 工具推荐
> 为防止无法正常下载Maven以来，请使用以下IDE版本

IDEA版本  | Maven版本
-----|-------- | 
IDEA2020及以上版本  | Maven 3.6.3及以上版本 |

## 环境配置

- 打开`xh-ureport2-console/src/main/resources`中的`application.yml`
- 修改配置
  - 端口配置
  - `数据库`配置和`Redis`配置
  - 是否开启多租户  
- 打开`xh-ureport2-console/src/main/java/com.bstek.ureport.console/DataReportApplication`运行

# 数据集条件用法

例子

```sql
${
"select * from base_user where 1=1" +(emptyparam("F_Gender")==true?"":" and F_Gender=:F_Gender") +(emptyparam("F_RealName")==true?"":" and F_RealName like :F_RealName") +(emptyparam("F_QuickQuery")==true?"":" and F_QuickQuery like :F_QuickQuery")
}
```
对应参数填写

参数名  | 数据类型  | 默认值
-----|-------- | -------------
F_RealName  | String |
F_QuickQuery  | String |
F_Gender  | Integer |
