package com.xinghuo.common.database.sql;

import com.xinghuo.common.database.model.dbfield.JdbcColumnModel;
import com.xinghuo.common.database.model.interfaces.DbSourceOrDbLink;
import com.xinghuo.common.database.source.AbstractDbBase;
import com.xinghuo.common.database.source.impl.DbMySQL;
import com.xinghuo.common.database.util.DbTypeUtil;
import com.xinghuo.common.exception.DataException;
import lombok.Data;

import java.util.List;

/**
 * SQL语句模板基类
 * 用以一些SQL语句不同库的特殊处理
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
public abstract class AbstractSqlBase {

    /**
     * 数据基类
     */
    protected String dbEncode;




    protected AbstractDbBase getDb(){
        try {
            return DbTypeUtil.getEncodeDb(this.dbEncode);
        } catch (DataException e) {
            e.printStackTrace();
        }
        return new DbMySQL();
    }

    /**
     * 初始结构参数
     */
    public abstract void initStructParams(String table, DbSourceOrDbLink dbSourceOrDbLink);


    /**
     * 批量添加数据
     */
    // TODO 其余几个数据还没有添加方法
    public String batchInsertSql(List<List<JdbcColumnModel>> dataList, String table) {
        return "";
    }




}
