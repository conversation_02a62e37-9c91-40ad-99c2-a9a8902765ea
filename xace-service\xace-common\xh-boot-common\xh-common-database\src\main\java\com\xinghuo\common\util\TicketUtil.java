package com.xinghuo.common.util;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.temp.SaTempUtil;
import com.xinghuo.common.redis.util.RedisUtil;
import org.springframework.stereotype.Component;

/**
 * 票据工具类，用于创建、解析和管理临时TOKEN
 * TicketUtil 类提供了创建、解析和管理临时TOKEN的方法，临时TOKEN是一种具有有效期的令牌。
 * 该类依赖 RedisUtil 类来操作 Redis 数据库存储临时TOKEN。
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Component
public class TicketUtil {

    private static RedisUtil redisUtil;

    /**
     * 构造方法，注入 RedisUtil 实例
     *
     * @param redisUtil RedisUtil 实例
     */
    public TicketUtil(RedisUtil redisUtil) {
        TicketUtil.redisUtil = redisUtil;
    }

    /**
     * 创建临时TOKEN
     *
     * @param value   值
     * @param timeout 有效时间，秒
     * @return 生成的临时TOKEN
     */
    public static String createTicket(Object value, long timeout) {
        return SaTempUtil.createToken(value, timeout);
    }

    /**
     * 获取临时TOKEN内的数据
     *
     * @param ticket 票据
     * @param <T>    数据类型
     * @return 解析后的数据对象
     * @see #createTicket(Object, long)
     */
    public static <T> T parseTicket(String ticket) {
        return (T) SaTempUtil.parseToken(ticket);
    }

    /**
     * 移除临时TOKEN
     *
     * @param ticket 票据
     */
    public static void deleteTicket(String ticket) {
        SaTempUtil.deleteToken(ticket);
    }

    /**
     * 更新TOKEN内的内容
     *
     * @param ticket  票据
     * @param value   新值
     * @param timeout 超时时间，秒，可为空表示不更新超时时间
     * @see #createTicket(Object, long)
     */
    public static void updateTicket(String ticket, Object value, Long timeout) {
        Object obj = parseTicket(ticket);
        if (obj == null) {
            return;
        }
        String key = getTicketKey(ticket);
        if (timeout != null) {
            SaManager.getSaTokenDao().setObject(key, value, timeout);
        } else {
            SaManager.getSaTokenDao().updateObject(key, value);
        }
    }

    /**
     * 获取票据的键名
     *
     * @param ticket 票据
     * @return 票据的键名
     */
    private static String getTicketKey(String ticket) {
        return SaManager.getSaTemp().splicingKeyTempToken("",ticket);
    }
}