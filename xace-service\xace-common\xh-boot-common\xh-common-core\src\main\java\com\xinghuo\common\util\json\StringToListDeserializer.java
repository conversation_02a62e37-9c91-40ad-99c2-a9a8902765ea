/**
 * 自定义反序列化器，将JSON字符串转换为字符串列表。
 *
 * <AUTHOR>
 */
package com.xinghuo.common.util.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.xinghuo.common.util.core.StrXhUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

// 继承StdDeserializer，用于反序列化为List<String>类型
public class StringToListDeserializer extends StdDeserializer<List<String>> {

    /**
     * 无参构造函数，调用带Class<?>参数的构造函数。
     */
    public StringToListDeserializer() {
        this(null);
    }

    /**
     * 带Class<?>参数的构造函数。
     *
     * @param vc 反序列化目标类的类型
     */
    public StringToListDeserializer(Class<?> vc) {
        super(vc);
    }

    /**
     * 反序列化方法，将JSON字符串解析为字符串列表。
     *
     * @param jp JSON解析器
     * @param ctxt 序列化上下文
     * @return 解析后的字符串列表
     * @throws IOException 输入输出异常
     * @throws JsonProcessingException JSON处理异常
     */
    @Override
    public List<String> deserialize(JsonParser jp, DeserializationContext ctxt)
            throws IOException, JsonProcessingException {
        String valueAsString = jp.getValueAsString(); // 获取JSON字符串值
        if(StrXhUtil.isNotBlank(valueAsString)) { // 判断字符串非空
            return Arrays.asList(valueAsString.split(",")); // 使用逗号分割并返回列表
        }
        return new ArrayList<String>(); // 如果为空，则返回空列表
    }
}
