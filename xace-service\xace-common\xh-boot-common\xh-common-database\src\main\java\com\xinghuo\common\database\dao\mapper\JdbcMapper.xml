<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.common.database.dao.JdbcMapper">

    <select id="getList" parameterType="String" resultType="map">
        ${sql}
    </select>

    <update id="update" parameterType="String">
        ${sql}
    </update>

    <delete id="delete" parameterType="String">
        ${sql}
    </delete>

    <insert id="insert" parameterType="String">
        ${sql}
    </insert>

    <update id="updates" parameterType="String" statementType="STATEMENT">
        ${sql}
    </update>

</mapper>
