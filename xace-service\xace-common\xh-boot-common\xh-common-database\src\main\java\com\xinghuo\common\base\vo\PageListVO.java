package com.xinghuo.common.base.vo;
import lombok.Data;

import java.util.List;

/**
 * 分页ListVO
 * 用于表示带有分页信息的列表数据的数据模型类
 * 包含一个泛型列表和一个分页信息对象
 * 通常用于与前端交互，传递包含列表和分页信息的数据
 *
 * @param <T> 列表中元素的类型
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
public class PageListVO<T> {
    /**
     * 列表数据
     */
    private List<T> list;
    /**
     * 分页信息
     */
    PaginationVO pagination;

    public PageListVO(){}

    public PageListVO(List<T> list, PaginationVO pagination) {
        this.list = list;
        this.pagination = pagination;
    }
}
