package com.xinghuo.common.util.extra;

import cn.hutool.core.util.StrUtil;
import com.xinghuo.common.constant.GlobalConstant;
import com.xinghuo.common.util.core.StrXhUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.util.*;

/**
 * 客户端工具类
 * <AUTHOR>
 * @date 2023-10-05
 */
public class ServletUtil {


    private static final AntPathMatcher MATCHER = new AntPathMatcher();

    /**
     * 获取当前请求的HttpServletRequest对象
     * @return HttpServletRequest对象，如果无法获取则返回null
     */
    public static HttpServletRequest getRequest() {
        try {
            return Objects.requireNonNull(getRequestAttributes()).getRequest();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据header名称获取请求头的值
     * @param name 请求头名称
     * @return 请求头的值，如果不存在则返回null
     */
    public static String getHeader(String name) {
        if (getRequest() != null) {
            return getRequest().getHeader(name);
        }
        return null;
    }

    /**
     * 判断当前请求是否来自移动设备
     * @return 如果是移动设备访问返回true，否则返回false
     */
    public static boolean getIsMobileDevice() {
        return isMobileDevice(ServletUtil.getUserAgent());
    }

    /**
     * 根据User-Agent字符串判断是否是移动设备
     * @param userAgent User-Agent字符串
     * @return 如果是移动设备访问返回true，否则返回false
     */
    public static boolean getIsMobileDevice(String userAgent) {
        return isMobileDevice(userAgent);
    }

    /**
     * 获取请求头中的User-Agent值
     * @return User-Agent字符串，如果不存在则返回空字符串
     */
    public static String getUserAgent() {
        return ServletUtil.getHeader("User-Agent");
    }

    /**
     * 判断是否是移动设备
     * @param requestHeader User-Agent请求头
     * @return 如果是移动设备返回true，否则返回false
     */
    public static boolean isMobileDevice(String requestHeader) {
        String[] deviceArray = new String[]{"android", "windows phone", "iphone", "ios", "ipad", "mqqbrowser"};
        if (requestHeader == null) {
            return false;
        }
        requestHeader = requestHeader.toLowerCase();
        for (String s : deviceArray) {
            if (requestHeader.indexOf(s) > 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取当前请求的Servlet路径
     * @return Servlet路径
     */
    public static String getServletPath() {
        return Objects.requireNonNull(ServletUtil.getRequest()).getServletPath();
    }

    /**
     * 获取当前请求的HttpServletResponse对象
     * @return HttpServletResponse对象，如果无法获取则返回null
     */
    public static HttpServletResponse getResponse() {
        try {
            return Objects.requireNonNull(getRequestAttributes()).getResponse();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前请求的HttpSession对象
     * @return HttpSession对象
     */
    public static HttpSession getSession() {
        return Objects.requireNonNull(getRequest()).getSession();
    }

    /**
     * 获取当前线程绑定的ServletRequestAttributes对象
     * @return ServletRequestAttributes对象，如果无法获取则返回null
     */
    public static ServletRequestAttributes getRequestAttributes() {
        try {
            RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
            return (ServletRequestAttributes) attributes;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前请求的所有请求头为Map形式
     * @param request HttpServletRequest对象
     * @return 包含所有请求头的Map，键为请求头名称，值为请求头值
     */
    public static Map<String, String> getHeaders(HttpServletRequest request) {
        Map<String, String> map = new LinkedHashMap<>();
        Enumeration<String> enumeration = request.getHeaderNames();
        if (enumeration != null) {
            while (enumeration.hasMoreElements()) {
                String key = enumeration.nextElement();
                String value = request.getHeader(key);
                map.put(key, value);
            }
        }
        return map;
    }

    /**
     * 将字符串渲染到客户端
     *
     * @param response 渲染对象
     * @param string   待渲染的字符串
     * @return null
     */
    public static String renderString(HttpServletResponse response, String string) {
        try {
            response.setStatus(200);
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().print(string);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 判断是否是Ajax异步请求
     */
    public static boolean isAjaxRequest(HttpServletRequest request) {
        String accept = request.getHeader("accept");
        if (accept != null && accept.contains("application/json")) {
            return true;
        }

        String xRequestedWith = request.getHeader("X-Requested-With");
        if (xRequestedWith != null && xRequestedWith.contains("XMLHttpRequest")) {
            return true;
        }

        String uri = request.getRequestURI();
        if (StrXhUtil.inStringIgnoreCase(uri, ".json", ".xml")) {
            return true;
        }

        String ajax = request.getParameter("__ajax");
        return StrXhUtil.inStringIgnoreCase(ajax, "json", "xml");
    }

    /**
     * 根据URL模式和当前URL路径匹配，获取URL路径变量
     * @param pattern URL模式
     * @param path    当前URL路径
     * @return 匹配得到的URL路径变量，以Map形式返回
     */
    public static Map<String, String> getPathVariables(String pattern, String path) {
        Map<String, String> vars = null;
        try {
            if(StrXhUtil.isNotEmpty(path)) {
                vars = MATCHER.extractUriTemplateVariables(pattern, path);
            }
        } catch (Exception e) { }
        if(vars == null){
            vars = Collections.EMPTY_MAP;
        }
        return vars;
    }

    /**
     * 获取当前访问地址中的path变量
     * @param pattern URL模式
     * @return 匹配得到的URL路径变量，以Map形式返回
     */
    public static Map<String, String> getPathVariables(String pattern) {
        return getPathVariables(pattern, getServletPath());
    }

    /**
     * 获取当前请求的主机名
     * @return 主机名，如果无法获取则返回空字符串
     */
    public static String getRequestHost(){
        HttpServletRequest request = getRequest();
        if(request != null){
            String host = request.getHeader(GlobalConstant.HEADER_HOST);
            if(StrXhUtil.isEmpty(host)){
                host = request.getHeader("X-Forwarded-Host");
                if(StrXhUtil.isNotEmpty(host)) {
                    int index = host.lastIndexOf(",");
                    if (index != -1) {
                        return host.substring(index);
                    } else {
                        return host;
                    }
                }else {
                    host = request.getHeader("Host");
                }
            }
            if(StrXhUtil.isNotEmpty(host)){
                return host;
            }
        }
        return StrUtil.EMPTY;
    }
}
