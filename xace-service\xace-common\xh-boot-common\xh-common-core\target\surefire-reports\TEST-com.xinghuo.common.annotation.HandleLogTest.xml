<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.xinghuo.common.annotation.HandleLogTest" time="0.813" tests="4" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="20"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="G:\v2\pd-xace-v2\xace-service\xace-common\xh-boot-common\xh-common-core\target\test-classes;G:\v2\pd-xace-v2\xace-service\xace-common\xh-boot-common\xh-common-core\target\classes;e:\.m2\repository\org\openjdk\nashorn\nashorn-core\15.4\nashorn-core-15.4.jar;e:\.m2\repository\org\ow2\asm\asm\7.3.1\asm-7.3.1.jar;e:\.m2\repository\org\ow2\asm\asm-commons\7.3.1\asm-commons-7.3.1.jar;e:\.m2\repository\org\ow2\asm\asm-analysis\7.3.1\asm-analysis-7.3.1.jar;e:\.m2\repository\org\ow2\asm\asm-tree\7.3.1\asm-tree-7.3.1.jar;e:\.m2\repository\org\ow2\asm\asm-util\7.3.1\asm-util-7.3.1.jar;e:\.m2\repository\com\alibaba\fastjson\1.2.83\fastjson-1.2.83.jar;e:\.m2\repository\com\google\zxing\core\3.5.0\core-3.5.0.jar;e:\.m2\repository\com\belerweb\pinyin4j\2.5.1\pinyin4j-2.5.1.jar;e:\.m2\repository\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.2.4\spring-boot-starter-web-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter\3.2.4\spring-boot-starter-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot\3.2.4\spring-boot-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.4\spring-boot-autoconfigure-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.2.4\spring-boot-starter-logging-3.2.4.jar;e:\.m2\repository\ch\qos\logback\logback-classic\1.4.14\logback-classic-1.4.14.jar;e:\.m2\repository\ch\qos\logback\logback-core\1.4.14\logback-core-1.4.14.jar;e:\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;e:\.m2\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;e:\.m2\repository\org\slf4j\jul-to-slf4j\2.0.12\jul-to-slf4j-2.0.12.jar;e:\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;e:\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.2.4\spring-boot-starter-json-3.2.4.jar;e:\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.4\jackson-databind-2.15.4.jar;e:\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.4\jackson-annotations-2.15.4.jar;e:\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.4\jackson-core-2.15.4.jar;e:\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar;e:\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar;e:\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.4\spring-boot-starter-tomcat-3.2.4.jar;e:\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.19\tomcat-embed-core-10.1.19.jar;e:\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.19\tomcat-embed-el-10.1.19.jar;e:\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.19\tomcat-embed-websocket-10.1.19.jar;e:\.m2\repository\org\springframework\spring-web\6.1.5\spring-web-6.1.5.jar;e:\.m2\repository\org\springframework\spring-beans\6.1.5\spring-beans-6.1.5.jar;e:\.m2\repository\io\micrometer\micrometer-observation\1.12.4\micrometer-observation-1.12.4.jar;e:\.m2\repository\io\micrometer\micrometer-commons\1.12.4\micrometer-commons-1.12.4.jar;e:\.m2\repository\org\springframework\spring-webmvc\6.1.5\spring-webmvc-6.1.5.jar;e:\.m2\repository\org\springframework\spring-aop\6.1.5\spring-aop-6.1.5.jar;e:\.m2\repository\org\springframework\spring-context\6.1.5\spring-context-6.1.5.jar;e:\.m2\repository\org\springframework\spring-expression\6.1.5\spring-expression-6.1.5.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.2.4\spring-boot-starter-test-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-test\3.2.4\spring-boot-test-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.4\spring-boot-test-autoconfigure-3.2.4.jar;e:\.m2\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;e:\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;e:\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;e:\.m2\repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;e:\.m2\repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;e:\.m2\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;e:\.m2\repository\net\bytebuddy\byte-buddy\1.14.12\byte-buddy-1.14.12.jar;e:\.m2\repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;e:\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;e:\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.2\junit-jupiter-5.10.2.jar;e:\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.2\junit-jupiter-api-5.10.2.jar;e:\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;e:\.m2\repository\org\junit\platform\junit-platform-commons\1.10.2\junit-platform-commons-1.10.2.jar;e:\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;e:\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.2\junit-jupiter-params-5.10.2.jar;e:\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.2\junit-jupiter-engine-5.10.2.jar;e:\.m2\repository\org\junit\platform\junit-platform-engine\1.10.2\junit-platform-engine-1.10.2.jar;e:\.m2\repository\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;e:\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.12\byte-buddy-agent-1.14.12.jar;e:\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;e:\.m2\repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;e:\.m2\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;e:\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;e:\.m2\repository\org\springframework\spring-core\6.1.5\spring-core-6.1.5.jar;e:\.m2\repository\org\springframework\spring-jcl\6.1.5\spring-jcl-6.1.5.jar;e:\.m2\repository\org\springframework\spring-test\6.1.5\spring-test-6.1.5.jar;e:\.m2\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;e:\.m2\repository\commons-io\commons-io\2.16.0\commons-io-2.16.0.jar;e:\.m2\repository\org\owasp\antisamy\antisamy\1.7.5\antisamy-1.7.5.jar;e:\.m2\repository\org\apache\httpcomponents\client5\httpclient5\5.2.3\httpclient5-5.2.3.jar;e:\.m2\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.2.4\httpcore5-h2-5.2.4.jar;e:\.m2\repository\org\apache\httpcomponents\core5\httpcore5\5.2.4\httpcore5-5.2.4.jar;e:\.m2\repository\org\apache\xmlgraphics\batik-css\1.17\batik-css-1.17.jar;e:\.m2\repository\org\apache\xmlgraphics\batik-shared-resources\1.17\batik-shared-resources-1.17.jar;e:\.m2\repository\org\apache\xmlgraphics\batik-util\1.17\batik-util-1.17.jar;e:\.m2\repository\org\apache\xmlgraphics\batik-constants\1.17\batik-constants-1.17.jar;e:\.m2\repository\org\apache\xmlgraphics\batik-i18n\1.17\batik-i18n-1.17.jar;e:\.m2\repository\org\apache\xmlgraphics\xmlgraphics-commons\2.9\xmlgraphics-commons-2.9.jar;e:\.m2\repository\commons-logging\commons-logging\1.0.4\commons-logging-1.0.4.jar;e:\.m2\repository\org\htmlunit\neko-htmlunit\3.11.1\neko-htmlunit-3.11.1.jar;e:\.m2\repository\org\slf4j\slf4j-api\2.0.12\slf4j-api-2.0.12.jar;e:\.m2\repository\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar;e:\.m2\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;e:\.m2\repository\xml-apis\xml-apis-ext\1.3.04\xml-apis-ext-1.3.04.jar;e:\.m2\repository\org\apache\httpcomponents\client5\httpclient5-fluent\5.3.1\httpclient5-fluent-5.3.1.jar;e:\.m2\repository\cn\hutool\hutool-all\5.8.26\hutool-all-5.8.26.jar;e:\.m2\repository\net\lingala\zip4j\zip4j\2.11.5\zip4j-2.11.5.jar;e:\.m2\repository\com\github\yitter\yitter-idgenerator\1.0.6\yitter-idgenerator-1.0.6.jar;e:\.m2\repository\cn\dev33\sa-token-core\1.37.0\sa-token-core-1.37.0.jar;e:\.m2\repository\org\projectlombok\lombok\1.18.32\lombok-1.18.32.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="20"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\Java\jdk-20.0.1\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire1421285468628772771\surefirebooter-20241230154402555_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire1421285468628772771 2024-12-30T15-44-00_669-jvmRun1 surefire-20241230154402555_1tmp surefire_0-20241230154402555_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="G:\v2\pd-xace-v2\xace-service\xace-common\xh-boot-common\xh-common-core\target\test-classes;G:\v2\pd-xace-v2\xace-service\xace-common\xh-boot-common\xh-common-core\target\classes;e:\.m2\repository\org\openjdk\nashorn\nashorn-core\15.4\nashorn-core-15.4.jar;e:\.m2\repository\org\ow2\asm\asm\7.3.1\asm-7.3.1.jar;e:\.m2\repository\org\ow2\asm\asm-commons\7.3.1\asm-commons-7.3.1.jar;e:\.m2\repository\org\ow2\asm\asm-analysis\7.3.1\asm-analysis-7.3.1.jar;e:\.m2\repository\org\ow2\asm\asm-tree\7.3.1\asm-tree-7.3.1.jar;e:\.m2\repository\org\ow2\asm\asm-util\7.3.1\asm-util-7.3.1.jar;e:\.m2\repository\com\alibaba\fastjson\1.2.83\fastjson-1.2.83.jar;e:\.m2\repository\com\google\zxing\core\3.5.0\core-3.5.0.jar;e:\.m2\repository\com\belerweb\pinyin4j\2.5.1\pinyin4j-2.5.1.jar;e:\.m2\repository\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.2.4\spring-boot-starter-web-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter\3.2.4\spring-boot-starter-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot\3.2.4\spring-boot-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.4\spring-boot-autoconfigure-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.2.4\spring-boot-starter-logging-3.2.4.jar;e:\.m2\repository\ch\qos\logback\logback-classic\1.4.14\logback-classic-1.4.14.jar;e:\.m2\repository\ch\qos\logback\logback-core\1.4.14\logback-core-1.4.14.jar;e:\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;e:\.m2\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;e:\.m2\repository\org\slf4j\jul-to-slf4j\2.0.12\jul-to-slf4j-2.0.12.jar;e:\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;e:\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.2.4\spring-boot-starter-json-3.2.4.jar;e:\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.4\jackson-databind-2.15.4.jar;e:\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.4\jackson-annotations-2.15.4.jar;e:\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.4\jackson-core-2.15.4.jar;e:\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar;e:\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar;e:\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.4\spring-boot-starter-tomcat-3.2.4.jar;e:\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.19\tomcat-embed-core-10.1.19.jar;e:\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.19\tomcat-embed-el-10.1.19.jar;e:\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.19\tomcat-embed-websocket-10.1.19.jar;e:\.m2\repository\org\springframework\spring-web\6.1.5\spring-web-6.1.5.jar;e:\.m2\repository\org\springframework\spring-beans\6.1.5\spring-beans-6.1.5.jar;e:\.m2\repository\io\micrometer\micrometer-observation\1.12.4\micrometer-observation-1.12.4.jar;e:\.m2\repository\io\micrometer\micrometer-commons\1.12.4\micrometer-commons-1.12.4.jar;e:\.m2\repository\org\springframework\spring-webmvc\6.1.5\spring-webmvc-6.1.5.jar;e:\.m2\repository\org\springframework\spring-aop\6.1.5\spring-aop-6.1.5.jar;e:\.m2\repository\org\springframework\spring-context\6.1.5\spring-context-6.1.5.jar;e:\.m2\repository\org\springframework\spring-expression\6.1.5\spring-expression-6.1.5.jar;e:\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.2.4\spring-boot-starter-test-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-test\3.2.4\spring-boot-test-3.2.4.jar;e:\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.4\spring-boot-test-autoconfigure-3.2.4.jar;e:\.m2\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;e:\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;e:\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;e:\.m2\repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;e:\.m2\repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;e:\.m2\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;e:\.m2\repository\net\bytebuddy\byte-buddy\1.14.12\byte-buddy-1.14.12.jar;e:\.m2\repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;e:\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;e:\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.2\junit-jupiter-5.10.2.jar;e:\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.2\junit-jupiter-api-5.10.2.jar;e:\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;e:\.m2\repository\org\junit\platform\junit-platform-commons\1.10.2\junit-platform-commons-1.10.2.jar;e:\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;e:\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.2\junit-jupiter-params-5.10.2.jar;e:\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.2\junit-jupiter-engine-5.10.2.jar;e:\.m2\repository\org\junit\platform\junit-platform-engine\1.10.2\junit-platform-engine-1.10.2.jar;e:\.m2\repository\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;e:\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.12\byte-buddy-agent-1.14.12.jar;e:\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;e:\.m2\repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;e:\.m2\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;e:\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;e:\.m2\repository\org\springframework\spring-core\6.1.5\spring-core-6.1.5.jar;e:\.m2\repository\org\springframework\spring-jcl\6.1.5\spring-jcl-6.1.5.jar;e:\.m2\repository\org\springframework\spring-test\6.1.5\spring-test-6.1.5.jar;e:\.m2\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;e:\.m2\repository\commons-io\commons-io\2.16.0\commons-io-2.16.0.jar;e:\.m2\repository\org\owasp\antisamy\antisamy\1.7.5\antisamy-1.7.5.jar;e:\.m2\repository\org\apache\httpcomponents\client5\httpclient5\5.2.3\httpclient5-5.2.3.jar;e:\.m2\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.2.4\httpcore5-h2-5.2.4.jar;e:\.m2\repository\org\apache\httpcomponents\core5\httpcore5\5.2.4\httpcore5-5.2.4.jar;e:\.m2\repository\org\apache\xmlgraphics\batik-css\1.17\batik-css-1.17.jar;e:\.m2\repository\org\apache\xmlgraphics\batik-shared-resources\1.17\batik-shared-resources-1.17.jar;e:\.m2\repository\org\apache\xmlgraphics\batik-util\1.17\batik-util-1.17.jar;e:\.m2\repository\org\apache\xmlgraphics\batik-constants\1.17\batik-constants-1.17.jar;e:\.m2\repository\org\apache\xmlgraphics\batik-i18n\1.17\batik-i18n-1.17.jar;e:\.m2\repository\org\apache\xmlgraphics\xmlgraphics-commons\2.9\xmlgraphics-commons-2.9.jar;e:\.m2\repository\commons-logging\commons-logging\1.0.4\commons-logging-1.0.4.jar;e:\.m2\repository\org\htmlunit\neko-htmlunit\3.11.1\neko-htmlunit-3.11.1.jar;e:\.m2\repository\org\slf4j\slf4j-api\2.0.12\slf4j-api-2.0.12.jar;e:\.m2\repository\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar;e:\.m2\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;e:\.m2\repository\xml-apis\xml-apis-ext\1.3.04\xml-apis-ext-1.3.04.jar;e:\.m2\repository\org\apache\httpcomponents\client5\httpclient5-fluent\5.3.1\httpclient5-fluent-5.3.1.jar;e:\.m2\repository\cn\hutool\hutool-all\5.8.26\hutool-all-5.8.26.jar;e:\.m2\repository\net\lingala\zip4j\zip4j\2.11.5\zip4j-2.11.5.jar;e:\.m2\repository\com\github\yitter\yitter-idgenerator\1.0.6\yitter-idgenerator-1.0.6.jar;e:\.m2\repository\cn\dev33\sa-token-core\1.37.0\sa-token-core-1.37.0.jar;e:\.m2\repository\org\projectlombok\lombok\1.18.32\lombok-1.18.32.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Java\jdk-20.0.1"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="G:\v2\pd-xace-v2\xace-service\xace-common\xh-boot-common\xh-common-core"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire1421285468628772771\surefirebooter-20241230154402555_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="20.0.1+9-29"/>
    <property name="user.name" value="Administrator"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="e:\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2023.3.3"/>
    <property name="java.version" value="20.0.1"/>
    <property name="user.dir" value="G:\v2\pd-xace-v2\xace-service\xace-common\xh-boot-common\xh-common-core"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\Java\jdk-20.0.1\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\java\jdk-20.0.1\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Python\Python311;D:\Python\Python311\Scripts;D:\java\apache-maven-3.6.3\bin;D:\tool\nvm;D:\Program Files\nodejs;;C:\Users\<USER>\AppData\Local\Programs\Fiddler;D:\IDE\Microsoft VS Code\bin;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="20.0.1+9-29"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="64.0"/>
  </properties>
  <testcase name="shouldHaveRequestMethod" classname="com.xinghuo.common.annotation.HandleLogTest" time="0.734"/>
  <testcase name="moduleNameShouldNotBeEmpty" classname="com.xinghuo.common.annotation.HandleLogTest" time="0.004"/>
  <testcase name="requestMethodShouldNotBeEmpty" classname="com.xinghuo.common.annotation.HandleLogTest" time="0.003"/>
  <testcase name="shouldHaveModuleName" classname="com.xinghuo.common.annotation.HandleLogTest" time="0.003"/>
</testsuite>