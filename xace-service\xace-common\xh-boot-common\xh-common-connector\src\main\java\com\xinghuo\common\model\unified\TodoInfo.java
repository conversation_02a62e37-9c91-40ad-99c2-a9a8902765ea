package com.xinghuo.common.model.unified;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 统一待办服务模块的配置类。
 *
 * <AUTHOR>
 * @date 2022_06_09
 */
@Data
public class TodoInfo {
    @NotBlank(message = "必填")
    @Schema(description = "待办服务url地址")
    private String url;
    @NotBlank(message = "必填")
    @Schema(description = "appid")
    private String appid;
    @NotBlank(message = "必填")
    @Schema(description = "模块名称")
    private String moduleName;
    @NotBlank(message = "必填")
    @Schema(description = "推送单用户单个待办事项")
    private String pushSingleUrl;
    @NotBlank(message = "必填")
    @Schema(description = "以用户ID、待办数向多用户推送相同待办事项")
    private String batchUseridCountUrl;
    @NotBlank(message = "必填")
    @Schema(description = "以用户code、待办数向多用户推送相同待办事项")
    private String batchUsercodeCountUrl;
    @NotBlank(message = "必填")
    @Schema(description = "以用户code、无待办数向多用户推送相同待办事项")
    private String pushBatchWithoutCountUrl;
    @NotBlank(message = "必填")
    @Schema(description = "以用户ID、无待办数批量推送待办数变量通知")
    private String useridsPendingUrl;
    @NotBlank(message = "必填")
    @Schema(description = "以用户code、无待办数批量推送待办数变量通知")
    private String usercodesPendingUrl;
    @NotBlank(message = "必填")
    @Schema(description = "推送代办详情消息到智能桌面代办事项")
    private String useridPendingDetailUrl;
}
