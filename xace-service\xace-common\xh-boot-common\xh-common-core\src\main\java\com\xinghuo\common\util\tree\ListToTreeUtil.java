package com.xinghuo.common.util.tree;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 提供将列表转换为树形结构的工具类。
 * <AUTHOR>
 * @date 2023-10-05
 */
public class ListToTreeUtil {

    /**
     * 将给定的数据列表转换为树形结构视图列表。
     *
     * @param data 待转换的数据列表
     * @return 转换后的树形结构视图列表
     */
    public static List<TreeViewModel> toTreeView(List<TreeViewModel> data) {
        return getChildNodeList(data, "0");
    }

    /**
     * 递归方法，根据父节点ID获取其所有子节点，并构建树形结构。
     *
     * @param data     待处理的数据列表
     * @param parentId 父节点的ID
     * @return 构建好的父节点及其子节点的树形结构列表
     */
    private static List<TreeViewModel> getChildNodeList(List<TreeViewModel> data, String parentId) {
        List<TreeViewModel> treeList = new ArrayList<>();
        // 筛选出父节点ID匹配的数据
        List<TreeViewModel> childNodeList = data.stream().filter(t -> String.valueOf(t.getParentId()).equals(parentId)).collect(Collectors.toList());
        for (TreeViewModel entity : childNodeList) {
            TreeViewModel model = new TreeViewModel();
            // 复制节点信息
            model.setId(entity.getId());
            model.setText(entity.getText());
            model.setParentId(entity.getParentId());
            model.setIsexpand(entity.getIsexpand());
            model.setComplete(entity.getComplete());
            // 判断节点是否有子节点
            model.setHasChildren(entity.getHasChildren() == null ? data.stream().noneMatch(t -> String.valueOf(t.getParentId()).equals(String.valueOf(entity.getId()))) ? false : true : false);
            // 如果节点显示复选框，则复制复选框状态
            if (entity.getShowcheck()) {
                model.setCheckstate(entity.getCheckstate());
                model.setShowcheck(true);
            }
            // 如果存在图片、CSS类、点击事件、代码、标题或高度，则复制这些属性
            if (entity.getImg() != null) {
                model.setImg(entity.getImg());
            }
            if (entity.getCssClass() != null) {
                model.setCssClass(entity.getCssClass());
            }
            if (entity.getClick() != null) {
                model.setClick(entity.getClick());
            }
            if (entity.getCode() != null) {
                model.setCode(entity.getCode());
            }
            if (entity.getTitle() != null) {
                model.setTitle(entity.getTitle());
            }
            if (entity.getHt() != null) {
                model.setHt(entity.getHt());
            }
            // 递归获取当前节点的子节点
            model.setChildNodes(getChildNodeList(data, entity.getId()));
            treeList.add(model);
        }
        return treeList;
    }

    /**
     * 递归查询父节点，生成树形结构。
     *
     * @param data     条件筛选后的数据
     * @param dataAll  所有数据
     * @param id       节点ID字段名
     * @param parentId 节点父ID字段名
     * @param <T>      数据类型
     * @return 构建好的树形结构列表
     */
    public static <T> JSONArray treeWhere(List<T> data, List<T> dataAll, String id, String parentId) {
        JSONArray resultData = new JSONArray();
        // 如果条件筛选后的数据与所有数据一致，直接返回条件筛选后的数据
        if (data.size() == dataAll.size()) {
            resultData.addAll(data);
            return resultData;
        }
        // 从所有数据中去除条件筛选后的数据
        List<T> dataListAll = new ArrayList<>();
        CollUtil.addAll(dataListAll,dataAll);
        dataListAll.removeAll(data);
        // 遍历条件筛选后的数据，构建树形结构
        for (T entity : data) {
            JSONObject json = JSONUtil.parseObj(JSONUtil.toJsonStr(entity));
            String firstParentId = json.getStr(parentId);
            // 如果结果数据中还未包含当前节点，则添加
            if (resultData.stream().noneMatch(t -> t.equals(json))) {
                resultData.add(entity);
            }
            // 如果当前节点不是根节点，则递归查询其父节点
            if (!"-1".equals(firstParentId)) {
                parentData(dataListAll, json, resultData, id, parentId);
            }
        }
        return resultData;
    }

    /**
     * 便捷方法，使用默认的节点ID和父ID字段名调用treeWhere方法。
     *
     * @param data     条件筛选后的数据
     * @param dataAll  所有数据
     * @param <T>      数据类型
     * @return 构建好的树形结构列表
     */
    public static <T> JSONArray treeWhere(List<T> data, List<T> dataAll) {
        String id = "id";
        String parentId = "parentId";
        return treeWhere(data, dataAll, id, parentId);
    }

    /**
     * 递归查询父节点，将其添加到结果数据中。
     *
     * @param dataAll    所有数据
     * @param json       当前节点
     * @param resultData 结果数据
     * @param id         节点ID字段名
     * @param parentId   节点父ID字段名
     * @param <T>        数据类型
     * @return 更新后的结果数据
     */
    private static <T> JSONArray parentData(List<T> dataAll, JSONObject json, JSONArray resultData, String id, String parentId) {
        // 筛选出当前节点的父节点
        List<T> data = dataAll.stream().filter(t ->  JSONUtil.parseObj(JSONUtil.toJsonStr(t)).getStr(id).equals(json.get(parentId))).toList();
        dataAll.removeAll(data);
        // 遍历父节点，构建树形结构
        for (T entity : data) {
            JSONObject object = JSONUtil.parseObj(JSONUtil.toJsonStr(entity));
            String parentIds = object.getStr(parentId);
            // 如果结果数据中还未包含当前父节点，则添加
            if (resultData.stream().noneMatch(t -> t.equals(object))) {
                resultData.add(entity);
            }
            // 如果当前父节点已经是根节点，则停止递归
            if ("-1".equals(parentIds)) {
                break;
            }
            // 递归查询当前父节点的父节点
            parentData(dataAll, object, resultData, id, parentId);
        }
        return resultData;
    }

    /**
     * 递归查询子节点，生成树形结构。
     *
     * @param dataAll  所有数据
     * @param id       节点ID字段名
     * @param parentId 节点父ID字段名
     * @param fid      查询的父节点ID
     * @param <T>      数据类型
     * @return 构建好的子节点列表
     */
    public static <T> JSONArray treeWhere(String fid, List<T> dataAll, String id, String parentId) {
        JSONArray resultData = new JSONArray();
        // 筛选出父节点ID匹配的数据
        List<T> data = dataAll.stream().filter(t ->JSONUtil.parseObj(JSONUtil.toJsonStr(t)).getStr(parentId).equals(fid)).toList();
        List<T> dataListAll = new ArrayList<>();
        CollUtil.addAll(dataListAll,dataAll);
        dataListAll.removeAll(data);
        // 遍历匹配到的父节点，构建子节点列表
        for (T entity : data) {
            JSONObject json = JSONUtil.parseObj(JSONUtil.toJsonStr(entity));
            String fId = json.getStr(id);
            String fParentId = json.getStr(parentId);
            // 如果查询的父节点ID与当前节点的父节点ID匹配，则将当前节点及其子节点添加到结果数据中
            if (fid.equals(fParentId)) {
                resultData.add(entity);
                childData(fId, dataListAll, resultData, id, parentId);
            }
        }
        return resultData;
    }

    /**
     * 便捷方法，使用默认的节点ID和父ID字段名调用treeWhere方法。
     *
     * @param fid      查询的父节点ID
     * @param data     所有数据
     * @param <T>      数据类型
     * @return 构建好的子节点列表
     */
    public static <T> JSONArray treeWhere(String fid, List<T> data) {
        String id = "id";
        String parentId = "parentId";
        return treeWhere(fid, data, id, parentId);
    }

    /**
     * 递归查询子节点，将其添加到结果数据中。
     *
     * @param dataAll  所有数据
     * @param id       节点ID字段名
     * @param parentId 节点父ID字段名
     * @param fid      查询的父节点ID
     * @param <T>      数据类型
     * @return 更新后的结果数据
     */
    public static <T> JSONArray childData(String fid, List<T> dataAll, JSONArray resultData, String id, String parentId) {
        // 筛选出父节点ID匹配的数据
        List<T> data = dataAll.stream().filter(t -> JSONUtil.parseObj(JSONUtil.toJsonStr(t)).getStr(parentId).equals(fid)).toList();
        dataAll.removeAll(data);
        // 遍历匹配到的父节点，构建子节点列表
        for (T entity : data) {
            JSONObject json = JSONUtil.parseObj(JSONUtil.toJsonStr(entity));
            String fId = json.getStr(id);
            String fParentId = json.getStr(parentId);
            // 如果查询的父节点ID与当前节点的父节点ID匹配，则将当前节点及其子节点添加到结果数据中
            if (fid.equals(fParentId)) {
                resultData.add(entity);
                childData(fId, dataAll, resultData, id, parentId);
            }
        }
        return resultData;
    }
}


