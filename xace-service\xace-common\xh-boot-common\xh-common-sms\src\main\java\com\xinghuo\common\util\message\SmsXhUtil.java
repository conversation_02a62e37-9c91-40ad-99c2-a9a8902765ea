package com.xinghuo.common.util.message;//package com.xinghuo.common.util.message;

//import com.aliyun.dysmsapi20170525.Client;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 阿里云发送短信
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Slf4j
public class SmsXhUtil {

    /**
     * 使用AK&SK初始化账号Client
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @param endpoint
     * @return Client
     */
    private static Object createClient(String accessKeyId, String accessKeySecret, String endpoint) {
        return null;
    }

    /**
     * 查询短信模板详情
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @param endpoint
     * @param templateId
     */
    public static List<String> querySmsTemplateRequest(String accessKeyId, String accessKeySecret, String endpoint, String templateId) {
        return null;
    }

    /**
     * 查询短信模板详情
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @param endpoint
     * @param templateId
     */
    public static String querySmsTemplateContent(String accessKeyId, String accessKeySecret, String endpoint, String templateId) {
        return null;
    }

    /**
     * 发送短信
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @param endpoint
     * @param phoneNumbers
     * @param signContent
     * @param templateId
     * @param map
     * @return
     */
    public static String sentSms(String accessKeyId, String accessKeySecret, String endpoint, String phoneNumbers, String signContent, String templateId, Map<String, Object> map) {
        return null;
    }

}
