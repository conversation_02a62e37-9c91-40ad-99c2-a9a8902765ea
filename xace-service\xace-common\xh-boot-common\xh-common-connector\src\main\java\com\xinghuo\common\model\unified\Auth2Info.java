package com.xinghuo.common.model.unified;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 统一认证配置Model
 * <AUTHOR>
 * @date 2022-07-11
 */
@Data
public class Auth2Info {

    @NotBlank(message = "必填")
    @Schema(description = "統一认证地址")
    private String url;
    @NotBlank(message = "必填")
    @Schema(description = "统一认证ClientId")
    private String clientId;
    @NotBlank(message = "必填")
    @Schema(description = "统一认证Secret")
    private String clientSecret;
    @NotBlank(message = "必填")
    @Schema(description = "获取token地址")
    private String gettokenUrl;
    @NotBlank(message = "必填")
    @Schema(description = "校验token地址")
    private String checkTokenUrl;
    @NotBlank(message = "必填")
    @Schema(description = "获取用户信息地址")
    private String getUserUrl;
    @NotBlank(message = "必填")
    @Schema(description = "登出地址")
    private String logoutUrl;
    @NotBlank(message = "必填")
    @Schema(description = "网页登录获取code地址")
    private String webLogin;
    @NotBlank(message = "必填")
    @Schema(description = "当前服务地址")
    private String serverUrl;
    @NotBlank(message = "必填")
    @Schema(description = "当前服务获取code回调地址")
    private String redirectUri;
    @NotBlank(message = "必填")
    @Schema(description = "获取全量用户地址")
    private String getAllUserUrl;
    @NotBlank(message = "必填")
    @Schema(description = "获取全量部门地址")
    private String getAllDeptUrl;
    @NotBlank(message = "必填")
    @Schema(description = "默认密码")
    private String defaultUserPassword;
    @NotBlank(message = "必填")
    @Schema(description = "加密key")
    private String secretKey;
    @NotBlank(message = "必填")
    @Schema(description = "是否配置角色")
    private String roleConfig;
    @NotBlank(message = "必填")
    @Schema(description = "默认角色（id集合，用逗号分隔）")
    private String defaultRoleId;
    @NotBlank(message = "必填")
    @Schema(description = "获取增量用户地址")
    private String getIncrementUserUrl;
    @NotBlank(message = "必填")
    @Schema(description = "获取增量部门地址")
    private String getIncrementDeptUrl;
}
