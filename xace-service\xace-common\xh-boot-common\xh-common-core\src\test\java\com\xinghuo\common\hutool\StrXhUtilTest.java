package com.xinghuo.common.hutool;

import com.xinghuo.common.util.core.StrXhUtil;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class StrXhUtilTest {

    @Test
    void isValidNotNullShouldReturnFalseWhenNull() {
        assertFalse(StrXhUtil.isValidNotNull(null));
    }

    @Test
    void isValidNotNullShouldReturnFalseWhenBlank() {
        assertFalse(StrXhUtil.isValidNotNull(""));
    }

    @Test
    void isValidNotNullShouldReturnFalseWhenNullString() {
        assertFalse(StrXhUtil.isValidNotNull("null"));
    }

    @Test
    void isValidNotNullShouldReturnTrueWhenValidString() {
        assertTrue(StrXhUtil.isValidNotNull("valid"));
    }

    @Test
    void inStringIgnoreCaseShouldReturnFalseWhenNull() {
        assertFalse(StrXhUtil.inStringIgnoreCase(null, "a", "b"));
    }

    @Test
    void inStringIgnoreCaseShouldReturnTrueWhenInArray() {
        assertTrue(StrXhUtil.inStringIgnoreCase("a", "a", "b"));
    }

    @Test
    void replaceMoreStrToOneStrShouldReturnOriginalWhenEmpty() {
        assertEquals("", StrXhUtil.replaceMoreStrToOneStr("", "a"));
    }

    @Test
    void replaceMoreStrToOneStrShouldReturnOriginalWhenNoDuplicates() {
        assertEquals("abc", StrXhUtil.replaceMoreStrToOneStr("abc", "a"));
    }

    @Test
    void replaceMoreStrToOneStrShouldReturnReplacedWhenDuplicates() {
        assertEquals("abc", StrXhUtil.replaceMoreStrToOneStr("aabc", "a"));
    }
}