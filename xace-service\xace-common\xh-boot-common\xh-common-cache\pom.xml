<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xh-boot-common</artifactId>
        <groupId>com.xinghuo.xace</groupId>
        <version>2.1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>xh-common-cache</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-common-core</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis-springdata</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <!-- 在极简系统中，如果不适用redis，可以不引入，需要在配置文件中，不引入remote    -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redisson.version}</version>
        </dependency>
    </dependencies>
</project>
