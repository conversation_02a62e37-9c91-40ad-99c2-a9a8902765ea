package com.xinghuo.common.exception;

import lombok.Getter;

/**
 * 租户数据库相关异常
 */
public class TenantDatabaseException extends RuntimeException {

    @Getter
    private String logMsg;
    public TenantDatabaseException() {
    }

    public TenantDatabaseException(String message) {
        super(message);
    }

    public TenantDatabaseException(String message, Throwable cause) {
        super(message, cause);
    }


    public TenantDatabaseException(String message, String logMsg) {
        super(message);
        this.logMsg = logMsg;
    }
}
