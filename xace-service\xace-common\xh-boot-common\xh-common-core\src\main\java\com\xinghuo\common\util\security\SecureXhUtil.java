package com.xinghuo.common.util.security;

import cn.hutool.crypto.SecureUtil;
import org.springframework.util.StopWatch;

/**
 * SecureXhUtil  加密解密工具类
 * <AUTHOR>
 * @date 2024-04-14
 *
 */
public class SecureXhUtil extends SecureUtil {

    /**外链秘钥*/
    private static String SHORT_LINK_KEY = "xhlink";
    private final static byte[] AESKEY_BYTES = "e1369d446dd9058f864dddd34913794c".getBytes();

    /**
     * 使用固定的种子进行AES解密
     */
    public static String aesDecode(String res) {
        return aes(AESKEY_BYTES).decryptStr(res);
    }

    /**
     * 使用固定的种子进行AES加密
     */
    public static String aesEncode(String res) {
        return aes(AESKEY_BYTES).encryptHex(res);
    }

    public static void main(String[] args)  throws Exception{
//        System.out.println("加密：" + DesUtil.aesEncode("123456"));
             String enCodeStr2 = aesEncode("123456");


             String decode2 = aesDecode(enCodeStr2);

        System.out.println("解密2：" + decode2);

        StopWatch stopWatch = new StopWatch();





        stopWatch.start("hutool encode");
        for (int i = 0; i < 10; i++) {
            aesEncode("123456");
//            System.out.println("加密：" + aesEncode("123456"));
        }

        stopWatch.stop();

        stopWatch.start("hutool decode");
        for (int i = 0; i < 10; i++) {
            aesDecode(enCodeStr2);
//            System.out.println("加密：" + aesEncode("123456"));
        }

        stopWatch.stop();

        System.out.println(stopWatch.prettyPrint());
        }
}
