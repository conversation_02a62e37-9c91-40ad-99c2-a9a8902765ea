package com.xinghuo.common.util;

import com.xinghuo.common.constant.MultiTenantType;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.security.XSSEscape;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
@ConfigurationProperties(prefix = "config")
public class ConfigValueUtil {


    /**
     * 前端附件文件夹
     */
    public final static String WEB_ANNEX_FOLDER = "WebAnnexFile";

    /**
     * 临时文件夹
     */
    public final static String TEMPORARY_FOLDER = "TemporaryFile";

    /**
     * 系统文件夹
     */
    public final static String SYSTEM_FOLDER = "SystemFile";

    /**
     * 文件模板文件夹
     */
    public final static String TEMPLATE_FOLDER = "TemplateFile";

    /**
     * 邮件文件夹
     */
    public final static String EMAIL_FOLDER = "EmailFile";

    /**
     * 文档管理文件夹
     */
    public final static String DOCUMENT_FOLDER = "DocumentFile";

    /**
     * 文档预览文件夹
     */
    public final static String DOCUMENT_PREVIEW_FOLDER = "DocumentPreview";

    /**
     * 用户头像文件夹
     */
    public final static String USER_AVATAR_FOLDER = "UserAvatar";

    /**
     * IM聊天图片+语音存储文件夹
     */
    public final static String IM_CONTENT_FOLDER = "IMContentFile";

    /**
     * 代码模板文件夹
     */
    public final static String TEMPLATE_CODE_FOLDER = "TemplateCode";
    /**
     * vue3代码模板文件夹
     */
    public final static String TEMPLATEVUE3_CODE_FOLDER = "TemplateCodeVue3";

    /**
     * 大屏图片文件夹
     */
    public final static String BI_VISUAL_FOLDER = "BiVisualPath";

    /**
     * 数据库备份文件夹
     */
    public final static String DATA_BACKUP_FOLDER = "DataBackupFile";

    /**
     * 前端模板文件夹
     */
    public final static String CODE_TEMP_FOLDER = "CodeTemp";

//    /**
//     * 环境路径
//     */
//    @Value("${config.Path:}")
//    private String path;
    /**
     * 数据库备份文件路径
     */
    private String dataBackupFilePath;
    /**
     * 临时文件存储路径
     */
    private String temporaryFilePath;
    /**
     * 系统文件存储路径
     */
    private String systemFilePath;
    /**
     * 文件模板存储路径
     */
    private String templateFilePath;
    /**
     * 代码模板存储路径
     */
    private String templateCodePath;
    /**
     * vue3代码模板存储路径
     */
    private String templateCodePathVue3;
    /**
     * 邮件文件存储路径
     */
    private String emailFilePath;
    /**
     * 大屏图片存储目录
     */
    private String biVisualPath;
    /**
     * 文档管理存储路径
     */
    private String documentFilePath;
    /**
     * 文件在线预览存储pdf
     */
    private String documentPreviewPath;
    /**
     * 用户头像存储路径
     */
    private String userAvatarFilePath;
    /**
     * IM聊天图片+语音存储路径
     */
    private String imContentFilePath;
    /**
     * 允许上传文件类型
     */
    @Value("${config.AllowUploadFileType:}")
    private String allowUploadFileType;
    /**
     * 允许图片类型
     */
    @Value("${config.AllowUploadImageType:}")
    private String allowUploadImageType;

    /**
     * 允许预览类型
     */
    @Value("${config.AllowPreviewFileType:}")
    private String allowPreviewFileType;

    /**
     * 预览方式
     */
    @Value("${config.PreviewType:}")
    private String previewType;

    /**
     * 预览方式
     */
    @Value("${config.kkFileUrl:}")
    private String kkFileUrl;

    /**
     * 前端文件目录
     */
    private String serviceDirectoryPath;
    /**
     * 代码生成器命名空间
     */
    @Value("${config.CodeAreasName:}")
    private String codeAreasName;

    /**
     * 前端附件文件目录
     */
    private String webAnnexFilePath;

    /**
     * 是否开启接口鉴权
     */
    private boolean enablePreAuth;

    /**
     * ApacheShardingSphere 配置开关
     */
    private boolean shardingSphereEnabled;

    /**
     * 是否开启magic接口编辑权限，默认为开启
     */
    @Value("${config.EnableMagicEditAuth:true}")
    private String enableMagicEditAuth;

    @Value("${config.ignoreTables:}")
    private List<String> ignoreTables;

    public String getServiceDirectoryPath(){
        String folder = StrXhUtil.isNotEmpty(serviceDirectoryPath) ? serviceDirectoryPath : CODE_TEMP_FOLDER;
        return getXssPath(folder +"/");
    }

    public String getDataBackupFilePath() {
        String folder = StrXhUtil.isNotEmpty(dataBackupFilePath) ? dataBackupFilePath : DATA_BACKUP_FOLDER;
        return getXssPath(folder +"/");
    }

    public String getTemporaryFilePath() {
        String folder = StrXhUtil.isNotEmpty(temporaryFilePath) ? temporaryFilePath : TEMPORARY_FOLDER;
        return getXssPath(folder +"/");
    }

    public String getSystemFilePath() {
        String folder = StrXhUtil.isNotEmpty(systemFilePath) ? systemFilePath : SYSTEM_FOLDER;
        return getXssPath(folder + "/");
    }

    public String getTemplateFilePath() {
        String folder = StrXhUtil.isNotEmpty(templateFilePath) ? templateFilePath : TEMPLATE_FOLDER;
        return getXssPath(folder + "/");
    }

    public String getTemplateCodePath() {
        String folder = StrXhUtil.isNotEmpty(templateCodePath) ? templateCodePath : TEMPLATE_CODE_FOLDER;
        return getXssPath(folder + "/");
    }

    public String getTemplateCodePathVue3() {
        String folder = StrXhUtil.isNotEmpty(templateCodePathVue3) ? templateCodePathVue3 : TEMPLATEVUE3_CODE_FOLDER;
        return getXssPath(folder + "/");
    }

    public String getEmailFilePath() {
        String folder = StrXhUtil.isNotEmpty(emailFilePath) ? emailFilePath : EMAIL_FOLDER;
        return getXssPath(folder + "/");
    }

    public String getDocumentPreviewPath() {
        String folder = StrXhUtil.isNotEmpty(documentPreviewPath) ? documentPreviewPath : DOCUMENT_PREVIEW_FOLDER;
        return getXssPath(folder + "/");
    }

    public String getUserAvatarFilePath() {
        String folder = StrXhUtil.isNotEmpty(userAvatarFilePath) ? userAvatarFilePath : USER_AVATAR_FOLDER;
        return getXssPath(folder + "/");
    }

    public String getImContentFilePath() {
        String folder = StrXhUtil.isNotEmpty(imContentFilePath) ? imContentFilePath : IM_CONTENT_FOLDER;
        return getXssPath(folder + "/");
    }

    public String getDocumentFilePath() {
        String folder = StrXhUtil.isNotEmpty(documentFilePath) ? documentFilePath : DOCUMENT_FOLDER;
        return getXssPath(folder + "/");
    }

    public String getWebAnnexFilePath() {
        String folder = StrXhUtil.isNotEmpty(webAnnexFilePath) ? webAnnexFilePath : WEB_ANNEX_FOLDER;
        return getXssPath(folder + "/");
    }

    public String getBiVisualPath() {
        String folder = StrXhUtil.isNotEmpty(biVisualPath) ? biVisualPath : BI_VISUAL_FOLDER;
        return getXssPath(folder + "/");
    }

    private String getXssPath(String path){
        String xssPath = XSSEscape.escapePath(path);
        return xssPath;
    }

    /**
     * 软件的错误报告
     */
    @Value("${config.ErrorReport:}")
    private String errorReport;
    /**
     * 软件的错误报告发给谁
     */
    @Value("${config.ErrorReportTo:}")
    private String errorReportTo;
    /**
     * 系统日志启用：true、false
     */
    @Value("${config.RecordLog:}")
    private String recordLog;
    /**
     * 多租户启用：true、false
     */
    private boolean multiTenancy;
    /**
     * 多租户接口
     */
    @Value("${config.MultiTenancyUrl:}")
    private String multiTenancyUrl;
    /**
     * 多租户模式
     */
    private MultiTenantType multiTenantType;
    /**
     * 多租户字段
     */
    private String multiTenantColumn = "F_TenantId";

    /**
     * 多租户登录短信验证接口
     */
    private String multiTenancyOfficialLoginCodeUrl = null;
    /**
     * 多租户重置密码短信验证接口
     */
    private String multiTenancyOfficialResetCodeUrl = null;

    /**
     * 开启逻辑删除功能  1 删除  0没删除
     */
    @Value("${config.EnableLogicDelete:false}")
    private boolean enableLogicDelete;
    /**
     * 逻辑删除字段
     */
    private String logicDeleteColumn = "F_DELETEMARK";
    /**
     * 版本
     */
    @Value("${config.SoftVersion:}")
    private String softVersion;
    /**
     * 推送是否启动：false、true
     */
    @Value("${config.IgexinEnabled:}")
    private String igexinEnabled;
    /**
     * APPID
     */
    @Value("${config.IgexinAppid:}")
    private String igexinAppid;
    /**
     * APPKEY
     */
    @Value("${config.IgexinAppkey:}")
    private String igexinAppkey;
    /**
     * MASTERSECRET
     */
    @Value("${config.IgexinMastersecret:}")
    private String igexinMastersecret;

    @Value("${config.AppUpdateContent:}")
    private String appUpdateContent;
    @Value("${config.AppVersion:}")
    private String appVersion;

    /**
     * -------------租户库配置-----------
     */



    /**
     * -------------跨域配置-----------
     */
//    @Value("${config.Origins}")
//    private String origins;
//    @Value("${config.Methods}")
//    private String methods;

    /**
     * -------------是否开启测试环境，admin账户可以无限登陆，并且无法修改密码-----------
     */
    @Value("${config.TestVersion:}")
    private String testVersion;

    /**
     * 是否验证请求是否来自内部
     */
    private boolean enableInnerAuth;

    /**
     * -------------uniPush在线-----------
     */
    private String appPushUrl;

}
