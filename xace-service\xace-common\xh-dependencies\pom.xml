<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xinghuo.xace</groupId>
    <artifactId>xh-dependencies</artifactId>
    <version>2.1.0</version>
    <packaging>pom</packaging>

    <properties>
        <!--这里修改JDK版本号, 1.8, 11, 17-->
        <xace.version>2.1.0</xace.version>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <!-- 插件版本 -->
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <maven-deploy-plugin.version>3.1.1</maven-deploy-plugin.version>
        <versions-maven-plugin.version>2.16.2</versions-maven-plugin.version>
        <maven-surefire-plugin.version>3.0.0-M7</maven-surefire-plugin.version>
        <!-- springboot依赖  magicAPI 不支持 3.2.4-->
        <spring-boot.version>3.2.4</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        <spring-cloud-alibaba.version>2022.0.0.0</spring-cloud-alibaba.version>
        <!-- swagger -->
        <knife4j.version>4.4.0</knife4j.version>
        <!-- magicapi  按需引入 -->
        <magicapi.version>2.1.1</magicapi.version>
        <!-- 数据库配置 -->
        <mybatis-plus.vesion>3.5.5</mybatis-plus.vesion>
        <mybatis-plus.generator.vesion>3.4.1</mybatis-plus.generator.vesion>
        <mybatis-plus.dynamic.vesion>4.3.0</mybatis-plus.dynamic.vesion>
        <mybatis-dynamic-sql.version>1.5.0</mybatis-dynamic-sql.version>
        <druid.version>1.2.23</druid.version>
        <pagehelper.version>6.0.0</pagehelper.version>
        <!-- 数据库驱动类，按需引入 -->
        <mysql.version>8.3.0</mysql.version>
        <oracle.version>21.9.0.0</oracle.version>
        <dm18.version>1.8.0</dm18.version>
        <kingbase.version>2.0</kingbase.version>
        <postgre.version>42.6.0</postgre.version>
        <sqlserver.version>11.2.1.jre8</sqlserver.version>
        <!-- 缓存 -->
        <jetcache.version>2.7.5.XH</jetcache.version>
        <caffeine.version>3.1.8</caffeine.version>
        <!-- 权限认证  -->
        <sa-token.version>1.37.0</sa-token.version>
        <!--文件存储 -->
        <x-file-storage.version>2.1.0</x-file-storage.version>
        <oss.minio.version>8.5.9</oss.minio.version>
        <!-- 工具 -->
        <lombok.version>1.18.32</lombok.version>
        <hutool.version>5.8.26</hutool.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <zxing.version>3.5.0</zxing.version>
        <httpclient.version>5.3.1</httpclient.version>
        <zip4j.version>2.11.5</zip4j.version>
        <yitter-idgenerator.version>1.0.6</yitter-idgenerator.version>

        <!-- 其他 -->
        <shardingsphere.version>5.4.1</shardingsphere.version>
        <xxl-job.version>1.2-RELEASE</xxl-job.version>
        <nashorn.version>15.4</nashorn.version>
        <oshi-core.version>6.5.0</oshi-core.version>
        <jna.version>5.14.0</jna.version>

        <weixin.version>3.3.0</weixin.version>
        <poi.version>4.1.2</poi.version>
        <easypoi-base.version>4.5.0-XH</easypoi-base.version>
        <fastjson.version>1.2.83</fastjson.version>
        <common-lang3.version>3.12.0</common-lang3.version>
        <common-lang.version>2.6</common-lang.version>
        <common-pool.version>2.11.1</common-pool.version>
        <jwt.version>3.4.0</jwt.version>
        <thumbnailator.version>0.4.17</thumbnailator.version>
        <itextpdf.version>********</itextpdf.version>
        <itext-asian.version>5.2.0</itext-asian.version>

        <guava.version>33.1.0-jre</guava.version>
        <javax-mail.version>1.6.2</javax-mail.version>
        <common-fileupload.version>1.5</common-fileupload.version>
        <commons-io.version>2.16.0</commons-io.version>
        <commons-compress.version>1.26.1</commons-compress.version>

        <!--        <dingtalk.verssion>1.0</dingtalk.verssion>-->
        <!--        <tencentcloud-sdk-java.version>3.1.278</tencentcloud-sdk-java.version>-->
        <!--        <aliyun-java-sdk-dysmsapi.version>2.1.0</aliyun-java-sdk-dysmsapi.version>-->
        <!--        <aliyun-java-sdk-core.version>4.5.20</aliyun-java-sdk-core.version>-->
        <!--        <alibaba-dingtalk-service-sdk.version>1.0.1</alibaba-dingtalk-service-sdk.version>-->
        <antisamy.version>1.7.5</antisamy.version>
        <dysmsapi.version>2.0.8</dysmsapi.version>
        <commons-text.version>1.10.0</commons-text.version>
        <spring-context-support.version>1.0.11</spring-context-support.version>
        <dom4j.version>2.1.3</dom4j.version>
        <okhttp3.version>4.12.0</okhttp3.version>

        <dubbo.verssion>2.7.22</dubbo.verssion>
        <sentinel.version>1.8.6</sentinel.version>
        <seata.version>1.4.2</seata.version>


        <!--漏洞版本升级-->
        <xercesImpl.version>2.12.2</xercesImpl.version>
        <calcite-core.version>1.32.0</calcite-core.version>
        <jackson-databind.version>2.15.4</jackson-databind.version>
        <jackson-annotations.version>2.15.4</jackson-annotations.version>
        <kryo-serializers.version>0.42</kryo-serializers.version>
        <lock4j-redisson-spring-boot-starter.version>2.2.2</lock4j-redisson-spring-boot-starter.version>
        <redisson.version>3.27.2</redisson.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <quartz.version>2.3.2</quartz.version>
        <velocity-engine-core.version>2.3</velocity-engine-core.version>

        <signclient.version>3.0.1</signclient.version>
        <justauth.version>1.16.4</justauth.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- SpringCloud 微服务 按需引入 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 按需引入 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- validation 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- TEST 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- Swagger 依赖配置 -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- druid -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- mybatisplus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.vesion}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.vesion}</version>
            </dependency>
            <!-- mybatisplus - dynamic -->
            <dependency>
                <groupId>org.mybatis.dynamic-sql</groupId>
                <artifactId>mybatis-dynamic-sql</artifactId>
                <version>${mybatis-dynamic-sql.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.dynamic.vesion}</version>
            </dependency>
            <!-- 代码生成 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.generator.vesion}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mybatis-plus-extension</artifactId>
                        <groupId>com.baomidou</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--pagehelper分页插件-->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <!-- mysql -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <!--sqlserver-->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${sqlserver.version}</version>
            </dependency>
            <!-- Oracle-->
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.nls</groupId>
                <artifactId>orai18n</artifactId>
                <version>${oracle.version}</version>
            </dependency>
            <!-- dm -->
            <dependency>
                <groupId>com.dm</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm18.version}</version>
            </dependency>
            <!--人大金仓-->
            <dependency>
                <groupId>com.kingbase8</groupId>
                <artifactId>kingbase8-jdbc</artifactId>
                <version>${kingbase.version}</version>
            </dependency>
            <!--PostGre-->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgre.version}</version>
            </dependency>
            <!--缓存 -->
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis-springdata</artifactId>
                <version>${jetcache.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.fastjson2</groupId>
                        <artifactId>fastjson2</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.lettuce</groupId>
                        <artifactId>lettuce-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <!-- 文件存储 -->
            <dependency>
                <groupId>org.dromara.x-file-storage</groupId>
                <artifactId>x-file-storage-spring</artifactId>
                <version>${x-file-storage.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${oss.minio.version}</version>
            </dependency>



            <!-- commons-lang3 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${common-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${common-lang.version}</version>
            </dependency>

            <!-- 公共资源池 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${common-pool.version}</version>
            </dependency>

            <!--工具 开始  -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!--二维码-->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <!-- 拼音 -->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>
            <!-- 雪花ID生成器 -->
            <dependency>
                <groupId>com.github.yitter</groupId>
                <artifactId>yitter-idgenerator</artifactId>
                <version>${yitter-idgenerator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5-fluent</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <!-- 基于 Spring Boot 的安全认证框架，代替Spring Security -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${sa-token.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-jwt</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${sa-token.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-boot-starter-data-redis</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                </exclusions>
            </dependency>


            <!-- 微信 -->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${weixin.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 TODO 待去除 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>${itextpdf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext-asian</artifactId>
                <version>${itext-asian.version}</version>
            </dependency>

            <!--File转MultipartFile-->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${common-fileupload.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>


            <!--dubbo-->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.verssion}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring-context-support.version}</version>
            </dependency>
            <!--sentinel整合dubbo-->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-apache-dubbo-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <!--短信钉钉及企业微信-->
            <!-- 钉钉API的Jar -->
            <!--            <dependency>-->
            <!--                <groupId>dingtalk-sdk-java</groupId>-->
            <!--                <artifactId>taobao-sdk-java</artifactId>-->
            <!--                <version>${dingtalk.verssion}</version>-->
            <!--            </dependency>-->
            <!--            <dependency>-->
            <!--                <groupId>dingtalk-sdk-java</groupId>-->
            <!--                <artifactId>taobao-sdk-java-source</artifactId>-->
            <!--                <version>${dingtalk.verssion}</version>-->
            <!--            </dependency>-->

            <!--            <dependency>-->
            <!--                <groupId>com.tencentcloudapi</groupId>-->
            <!--                <artifactId>tencentcloud-sdk-java</artifactId>-->
            <!--                <version>${tencentcloud-sdk-java.version}</version>-->
            <!--            </dependency>-->
            <!--阿里云短信-->
            <!--            <dependency>-->
            <!--                <groupId>com.aliyun</groupId>-->
            <!--                <artifactId>dysmsapi20170525</artifactId>-->
            <!--                <version>${dysmsapi.version}</version>-->
            <!--            </dependency>-->
            <!--            <dependency>-->
            <!--                <groupId>com.aliyun</groupId>-->
            <!--                <artifactId>aliyun-java-sdk-core</artifactId>-->
            <!--                <version>${aliyun-java-sdk-core.version}</version>-->
            <!--            </dependency>-->
            <!--            <dependency>-->
            <!--                <groupId>com.aliyun</groupId>-->
            <!--                <artifactId>alibaba-dingtalk-service-sdk</artifactId>-->
            <!--                <version>${alibaba-dingtalk-service-sdk.version}</version>-->
            <!--            </dependency>-->
            <!--End短信钉钉及企业微信-->
            <!--防止XSS注入-->
            <dependency>
                <groupId>org.owasp.antisamy</groupId>
                <artifactId>antisamy</artifactId>
                <version>${antisamy.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- commons-text -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>

            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>${xercesImpl.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>

<!--            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>-->
            <!--Seata-->
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata.version}</version>
            </dependency>
            <!--Apache ShardingSphere-JDBC-->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core</artifactId>
                <version>${shardingsphere.version}</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>


            <!--漏洞版本升级-->

            <dependency>
                <groupId>org.apache.calcite</groupId>
                <artifactId>calcite-core</artifactId>
                <version>${calcite-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.calcite</groupId>
                <artifactId>calcite-linq4j</artifactId>
                <version>${calcite-core.version}</version>
            </dependency>


            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson-databind.version}</version>
            </dependency>


            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-base</artifactId>
                <version>${easypoi-base.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-compress</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                            <groupId>com.google.guava</groupId>
                            <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j-redisson-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>net.lingala.zip4j</groupId>
                <artifactId>zip4j</artifactId>
                <version>${zip4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <!-- websocket -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <!-- Quartz -->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>

            <!-- -->
            <!-- 缩略图 -->
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>${thumbnailator.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-annotation</artifactId>
                <version>${easypoi-base.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity-engine-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi-core.version}</version>
            </dependency>
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna-platform</artifactId>
                <version>${jna.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>me.zhyd.oauth</groupId>
                <artifactId>JustAuth</artifactId>
                <version>${justauth.version}</version>
            </dependency>
            <!--调度-->
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-scheduletask-client</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xh-scheduletask-model</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinghuo.xace</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <!-- JDK 17 需要独立引入 js引擎 -->
            <dependency>
                <groupId>org.openjdk.nashorn</groupId>
                <artifactId>nashorn-core</artifactId>
                <version>${nashorn.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>${versions-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
