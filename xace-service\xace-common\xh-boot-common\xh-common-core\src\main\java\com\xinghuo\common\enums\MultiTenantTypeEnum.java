package com.xinghuo.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023-10-05
 */
@Getter
public enum MultiTenantTypeEnum {

    /**
     * 表中字段过滤租户数据
     */
    COLUMN("字段模式"),

    /**
     * 动态替换SQL
     * {租户ID}.表名
     */
    SCHEMA("SCHEMA模式");


    private String directions;

    MultiTenantTypeEnum(String directions) {
        this.directions = directions;
    }


    public boolean eq(String val) {
        return this.name().equalsIgnoreCase(val);
    }

    public boolean eq(MultiTenantTypeEnum val) {
        if (val == null) {
            return false;
        }
        return eq(val.name());
    }
}
