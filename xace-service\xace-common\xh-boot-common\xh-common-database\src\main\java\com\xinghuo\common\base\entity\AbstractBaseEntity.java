package com.xinghuo.common.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xinghuo.common.constant.DbColumnConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 定义AbstractBaseEntity
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode
@SuppressWarnings("ALL")
public abstract class AbstractBaseEntity implements Serializable {


    /**
     * 添加ID字段，默认是F_ID
     * @param <T>
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode
    public static abstract class AbstractIBaseEntity<T> extends AbstractBaseEntity {
        /**
         * 主键
         */
//        @TableId(DbColumnConstant.F_ID)
//        public T id;
        //为了cglib 不报错，修改为String类型
        @TableId(DbColumnConstant.F_ID)
        public String id;

    }

    /**
     * 扩展ID、租户ID字段（F_TENANTID）
     * @param <T>
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode
    public static abstract class AbstractTBaseEntity<T> extends AbstractIBaseEntity<T> {
        /**
         * 租户id
         */
        @TableField(DbColumnConstant.F_TENANT_ID)
        private String tenantId;

    }

    /**
     * 扩展id，租户id，创建者，创建时间
     * @param <T>
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode
    public static abstract class AbstractCBaseEntity<T> extends AbstractTBaseEntity<T> {

        /**
         * 创建时间
         */
        @TableField(value = DbColumnConstant.F_CREATOR_TIME, fill = FieldFill.INSERT)
        private Date creatorTime;

        /**
         * 创建用户
         */
        @TableField(value = DbColumnConstant.F_CREATOR_USER_ID, fill = FieldFill.INSERT)
        private String creatorUserId;

    }

    /**
     * 扩展id，租户id，创建者，创建时间,最后更新时间，最后更改人
     * @param <T>
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode
    public static abstract class AbstractCUBaseEntity<T> extends AbstractCBaseEntity<T> {

        /**
         * 修改时间
         */
        @TableField(value = DbColumnConstant.F_LAST_MODIFY_TIME, fill = FieldFill.INSERT_UPDATE)
        private Date lastModifyTime;

        /**
         * 修改用户
         */
        @TableField(value = DbColumnConstant.F_LAST_MODIFY_USER_ID, fill = FieldFill.INSERT_UPDATE)
        private String lastModifyUserId;

    }

    /**
     * 扩展id，租户id，创建者，创建时间,最后更新时间，最后更改人,删除标志，删除时间，删除操作用户
     * @param <T>
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode
    public static abstract class AbstractCUDBaseEntity<T> extends AbstractCUBaseEntity<T> {

        /**
         * 删除标志
         */
        @TableField(value = DbColumnConstant.F_DELETE_MARK , updateStrategy = FieldStrategy.IGNORED)
        private Integer deleteMark;

        /**
         * 删除时间
         */
        @TableField(value = DbColumnConstant.F_DELETE_TIME , fill = FieldFill.UPDATE)
        private Date deleteTime;

        /**
         * 删除用户
         */
        @TableField(value = DbColumnConstant.F_DELETE_USER_ID , fill = FieldFill.UPDATE)
        private String deleteUserId;
    }

    /**
     * 扩展id，租户id，创建者，创建时间,删除标志，删除时间，删除操作用户
     * @param <T>
     */
    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode
    public static abstract class AbstractCDBaseEntity<T> extends AbstractCBaseEntity<T> {

        /**
         * 删除标志
         */
        @TableField(value = DbColumnConstant.F_DELETE_MARK , updateStrategy = FieldStrategy.IGNORED)
        private Integer deleteMark;

        /**
         * 删除时间
         */
        @TableField(value = DbColumnConstant.F_DELETE_TIME , fill = FieldFill.UPDATE)
        private Date deleteTime;
        /**
         * 删除用户
         */
        @TableField(value = DbColumnConstant.F_DELETE_USER_ID, fill = FieldFill.UPDATE)
        private String deleteUserId;
    }

}

