package com.xinghuo.common.hutool;

import cn.hutool.core.date.DateTime;
import com.xinghuo.common.util.core.DateXhUtil;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertNotNull;

class DateXhUtilTest {

    @Test
    void formatDateShouldReturnNonNullValue() {
        String result = DateXhUtil.formatDate(System.currentTimeMillis());
        assertNotNull(result);
    }

    @Test
    void formatDateTimeShouldReturnNonNullValue() {
        String result = DateXhUtil.formatDateTime(System.currentTimeMillis());
        assertNotNull(result);
    }

    @Test
    void beginOfDayShouldReturnNonNullValue() {
        DateTime result = DateXhUtil.beginOfDay(System.currentTimeMillis());
        assertNotNull(result);
    }

    @Test
    void endOfDayShouldReturnNonNullValue() {
        DateTime result = DateXhUtil.endOfDay(System.currentTimeMillis());
        assertNotNull(result);
    }

    @Test
    void getZonedDateTimeToStringShouldReturnNonNullValue() {
        String result = DateXhUtil.getZonedDateTimeToString(ZonedDateTime.now());
        assertNotNull(result);
    }

    @Test
    void getmmNowShouldReturnNonNullValue() {
        String result = DateXhUtil.getmmNow();
        assertNotNull(result);
    }

    @Test
    void nowShouldReturnNonNullValue() {
        String result = DateXhUtil.now("yyyy-MM-dd HH:mm:ss");
        assertNotNull(result);
    }

    @Test
    void cstFormatDateTimeShouldReturnNonNullValue() {
        String result = DateXhUtil.cstFormatDateTime("Wed Dec 15 00:00:00 CST 2021");
        assertNotNull(result);
    }

    @Test
    void getNextDateShouldReturnNonNullValue() {
//        List<Date> result = new ArrayList<>();
//        DateXhUtil.getNextDate(1, "2", new Date(), new Date(), result);
//        assertFalse(result.isEmpty());
    }

    @Test
    void formatSysDateTimeShouldReturnNonNullValue() {
        String result = DateXhUtil.formatSysDateTime(new Date());
        assertNotNull(result);
    }

    @Test
    void formatSysDateShouldReturnNonNullValue() {
        String result = DateXhUtil.formatSysDate(new Date());
        assertNotNull(result);
    }

    @Test
    void getDatesByMonthShouldReturnNonNullValue() {
        String[] result = DateXhUtil.getDatesByMonth("202112");
        assertNotNull(result);
    }

    @Test
    void lengthOfMonthShouldReturnNonNullValue() {
        Integer result = DateXhUtil.lengthOfMonth("202112");
        assertNotNull(result);
    }
}