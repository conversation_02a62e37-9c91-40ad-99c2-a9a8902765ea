package com.xinghuo.common.database.source.impl;

import com.baomidou.mybatisplus.annotation.DbType;
import com.xinghuo.common.database.constant.DbConst;
import com.xinghuo.common.database.source.AbstractDbBase;
import com.xinghuo.common.database.sql.model.DbStruct;

/**
 * MySQL模型
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@SuppressWarnings("AlibabaClassNamingShouldBeCamel")
public class DbMySQL extends AbstractDbBase {

    @Override
    protected void init(){
        setInstance(
                MYSQL,
                DbType.MYSQL,
                com.alibaba.druid.DbType.mysql,
                "3306",
                "root",
                "mysql",
                "com.mysql.cj.jdbc.Driver",
                "jdbc:mysql://{host}:{port}/{dbname}?useUnicode=true&characterEncoding=utf-8&allowMultiQueries=true&serverTimezone=GMT%2B8&useSSL=false"
                //connUrl = "jdbc:mysql://{host}:{port}/{dbname}?useUnicode=true&autoReconnect=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8";
                );
    }

    @Override
    public String getConnUrl(String prepareUrl, String host, Integer port, DbStruct struct) {
        prepareUrl = super.getConnUrl(prepareUrl, host, port, null);
        return prepareUrl.replace(DbConst.DB_NAME, struct.getMysqlDbName());
    }

}
