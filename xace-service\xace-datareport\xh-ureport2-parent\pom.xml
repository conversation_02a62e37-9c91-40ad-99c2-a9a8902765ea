<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.bstek.ureport</groupId>
	<artifactId>xh-ureport2-parent</artifactId>
	<version>2.0.0</version>
	<packaging>pom</packaging>
	<parent>
		<artifactId>xh-ureport2-datareport</artifactId>
		<groupId>com.xinghuo</groupId>
		<version>2.0.0</version>
	</parent>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.jar</include>
					<include>**/*.xml</include>
					<include>**/*.xsd</include>
					<include>**/*.schemas</include>
					<include>**/*.handlers</include>
					<include>**/*.properties</include>
					<include>**/*.png</include>
					<include>**/*.jpg</include>
					<include>**/*.gif</include>
					<include>**/*.css</include>
					<include>**/*.js</include>
					<include>**/*.map</include>
					<include>**/*.swf</include>
					<include>**/*.swz</include>
					<include>**/*.html</include>
					<include>**/*.jsp</include>
					<include>**/*.txt</include>
					<include>**/*.eot</include>
					<include>**/*.svg</include>
					<include>**/*.ttf</include>
					<include>**/*.ttc</include>
					<include>**/*.TTF</include>
					<include>**/*.TTC</include>
					<include>**/*.woff</include>
					<include>**/*.woff2</include>
					<include>**/*.md</include>
					<include>**/*.template</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
					<include>**/*.xsd</include>
					<include>**/*.schemas</include>
					<include>**/*.handlers</include>
					<include>**/*.properties</include>
					<include>**/*.png</include>
					<include>**/*.jpg</include>
					<include>**/*.gif</include>
					<include>**/*.css</include>
					<include>**/*.js</include>
					<include>**/*.map</include>
					<include>**/*.html</include>
					<include>**/*.jsp</include>
					<include>**/*.txt</include>
					<include>**/*.eot</include>
					<include>**/*.svg</include>
					<include>**/*.ttf</include>
					<include>**/*.ttc</include>
					<include>**/*.TTF</include>
					<include>**/*.TTC</include>
					<include>**/*.woff</include>
					<include>**/*.woff2</include>
					<include>**/*.md</include>
					<include>**/*.template</include>
				</includes>
			</resource>
		</resources>
		<!--<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>utf-8</encoding>
				</configuration>
			</plugin>
			<plugin>
			  <groupId>org.apache.maven.plugins</groupId>
			  <artifactId>maven-release-plugin</artifactId>
			  <version>2.5.3</version>
			</plugin>
			<plugin>
				<groupId>org.sonatype.plugins</groupId>
				<artifactId>nexus-staging-maven-plugin</artifactId>
				<version>1.6.7</version>
				<extensions>true</extensions>
				<configuration>
					<serverId>ossrh</serverId>
					<nexusUrl>https://oss.sonatype.org/</nexusUrl>
					<autoReleaseAfterClose>true</autoReleaseAfterClose>
				</configuration>
			</plugin>
			<plugin>
		      <groupId>org.apache.maven.plugins</groupId>
		      <artifactId>maven-source-plugin</artifactId>
		      <version>2.2.1</version>
		      <executions>
		        <execution>
		          <id>attach-sources</id>
		          <goals>
		            <goal>jar-no-fork</goal>
		          </goals>
		        </execution>
		      </executions>
		    </plugin>
		    <plugin>
		      <groupId>org.apache.maven.plugins</groupId>
		      <artifactId>maven-javadoc-plugin</artifactId>
		      <version>2.9.1</version>
		      <executions>
		        <execution>
		          <id>attach-javadocs</id>
		          <goals>
		            <goal>jar</goal>
		          </goals>
		        </execution>
		      </executions>
		    </plugin>
		</plugins>-->
	</build>
</project>
