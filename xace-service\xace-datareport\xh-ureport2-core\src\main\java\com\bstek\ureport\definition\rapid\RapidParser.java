package com.bstek.ureport.definition.rapid;

import com.bstek.ureport.parser.Parser;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Element;

public class RapidParser implements Parser<RapidDefinition> {
    @Override
    public RapidDefinition parse(Element element) {
        RapidDefinition hf = new RapidDefinition();
        String windows = element.attributeValue("windows");
        if(StringUtils.isNotEmpty(windows)){
            hf.setWindows(windows);
        }
        String mac = element.attributeValue("mac");
        if(StringUtils.isNotEmpty(mac)){
            hf.setMac(mac);
        }
        String fullName = element.attributeValue("fullName");
        if(StringUtils.isNotEmpty(fullName)){
            hf.setFullName(fullName);
        }
        return hf;
    }
}
