# Model 层规范

## 基本结构

Model层用于数据传输，通常分为DTO(数据传输对象)、VO(视图对象)和Query(查询条件对象)三类。

### 位置与命名

* **包路径:**
  * `com.xinghuo.[模块名].model.[业务功能]`

  例如:
  * `com.xinghuo.allegro.manage.model.store` - 店铺相关模型
  * `com.xinghuo.allegro.manage.model.currency` - 货币相关模型
  * `com.xinghuo.allegro.manage.model.storeConfig` - 店铺配置相关模型
  * `com.xinghuo.allegro.manage.model.translate` - 翻译相关模型

  其中，[业务功能]通常对应各个Controller的业务领域。

* **命名规范:**
  * DTO: 以操作+`DTO`结尾，如 `UserCreateDTO`，`ProductCategoryUpdateDTO`
  * VO: 以 `VO` 结尾，如 `UserVO`，`ProductCategoryDetailVO`
  * Pagination: 以 `Pagination` 结尾，如 `ProductPagination`，`OrderPagination`，包含查询条件
  * Model: 业务模型，通常以 `Model` 结尾，如 `RateModel`，`AllegroStoreBaseModel`
  * Form: 表单提交模型，通常以 `Form` 结尾，如 `BuckFeeForm`，`StoreShelfForm`

### 基础注解

* `@Data` - Lombok提供，生成getter/setter等方法
* `@Schema` - Swagger文档模型及属性注解
* 参数校验注解，如 `@NotNull`, `@Size` 等

## 分页查询基类

项目中使用 `Pagination` 作为分页查询的基类，所有需要分页的查询对象应继承此类。

```java
/**
 * 分页查询基类
 */
@Data
public class Pagination extends Page {
    /**
     * 每页记录数，默认20
     */
    private Integer pageSize = 20;

    /**
     * 排序方式，默认DESC
     */
    private String sort = "DESC";

    /**
     * 排序字段
     */
    private String sidx = "";

    /**
     * 当前页码，默认1
     */
    private Integer currentPage = 1;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 记录数
     */
    private Long records;

    /**
     * 数据类型 0-当前页，1-全部数据
     */
    private String dataType;

    /**
     * 高级查询条件JSON
     */
    private String superQueryJson;

    /**
     * 功能ID
     */
    private String moduleId;

    /**
     * 菜单ID
     */
    private String menuId;

    /**
     * 设置数据列表并返回，同时设置总记录数
     *
     * @param data 数据列表
     * @param records 总记录数
     * @return 原始数据列表
     */
    public <T> List<T> setDataList(List<T> data, Long records) {
        this.total = records;
        return data;
    }

    /**
     * 设置数据列表并返回，总记录数为列表大小
     *
     * @param data 数据列表
     * @return 原始数据列表
     */
    @JsonIgnore
    public <T> List<T> setDataList(List<T> data) {
        this.total = (long) data.size();
        return data;
    }

    /**
     * 设置总记录数（Integer转Long）
     *
     * @param total 总记录数
     */
    @JsonIgnore
    public void setTotalFromInt(Integer total) {
        this.total = total.longValue();
    }
}
```

### 分页查询对象示例

**⚠️ 重要：分页基类导入路径**

**✅ 正确的导入：**
```java
import com.xinghuo.common.base.model.Pagination;
```

**❌ 错误的导入：**
```java
import com.xinghuo.common.base.Pagination;  // 错误！缺少 model 包
```

```java
package com.xinghuo.project.model.product;

import com.xinghuo.common.base.model.Pagination;
/**
 * 产品分页查询对象
 *
 * <AUTHOR>
 * date 2023-10-15
 */
@Data
@Schema(description = "产品分页查询参数")
public class ProductPagination extends Pagination {

    /**
     * 产品名称(模糊查询)
     */
    @Schema(description = "产品名称")
    private String productName;

    /**
     * 产品分类ID
     */
    @Schema(description = "产品分类ID")
    private Long categoryId;

    /**
     * 产品状态 (1-上架，0-下架)
     */
    @Schema(description = "产品状态")
    private Integer status;

    /**
     * 搜索类型，格式：字段名|搜索方式，如：product_name|LIKE
     */
    @Schema(description = "搜索类型(字段名|搜索方式)")
    private String searchType;

    /**
     * 搜索关键字
     */
    @Schema(description = "搜索关键字")
    private String searchKey;

    /**
     * 时间类型，用于指定按哪个时间字段筛选
     */
    @Schema(description = "时间类型")
    private String dateType;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
```

## DTO 示例

```java
/**
 * 产品分类创建DTO
 *
 * @作者： 开发人员
 * @日期： 2023-05-15
 */
@Data
@ApiModel("产品分类创建请求")
public class ProductCategoryCreateDTO {

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 50, message = "分类名称长度不能超过50")
    @ApiModelProperty("分类名称")
    private String name;

    /**
     * 父分类ID (顶级分类为0)
     */
    @ApiModelProperty("父分类ID")
    private Long parentId;

    /**
     * 排序号
     */
    @Min(value = 0, message = "排序号不能小于0")
    @ApiModelProperty("排序号")
    private Integer sort;

    /**
     * 状态 (0-禁用, 1-启用)
     */
    @ApiModelProperty("状态")
    private Integer status;
}
```

## VO 示例

```java
/**
 * 产品分类视图对象
 *
 * @作者： 开发人员
 * @日期： 2023-05-15
 */
@Data
@ApiModel("产品分类视图")
public class ProductCategoryVO {

    /**
     * 分类ID
     */
    @ApiModelProperty("分类ID")
    private Long id;

    /**
     * 分类名称
     */
    @ApiModelProperty("分类名称")
    private String name;

    /**
     * 父分类ID
     */
    @ApiModelProperty("父分类ID")
    private Long parentId;

    /**
     * 父分类名称
     */
    @ApiModelProperty("父分类名称")
    private String parentName;

    /**
     * 排序号
     */
    @ApiModelProperty("排序号")
    private Integer sort;

    /**
     * 状态 (0-禁用, 1-启用)
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 状态名称
     */
    @ApiModelProperty("状态名称")
    private String statusName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
```

## Query 示例

```java
/**
 * 产品分类查询参数
 *
 * @作者： 开发人员
 * @日期： 2023-05-15
 */
@Data
@ApiModel("产品分类查询参数")
public class ProductCategoryQuery extends PageQuery {

    /**
     * 分类名称(模糊查询)
     */
    @ApiModelProperty("分类名称")
    private String name;

    /**
     * 父分类ID
     */
    @ApiModelProperty("父分类ID")
    private Long parentId;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 创建时间起始
     */
    @ApiModelProperty("创建时间起始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    /**
     * 创建时间截止
     */
    @ApiModelProperty("创建时间截止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;
}
```

## 参数校验注解

### ⚠️ 重要：使用 Jakarta EE 规范

由于项目使用 **JDK 17+ 和 Spring Boot 3.x**，必须使用 **Jakarta EE** 的校验注解：

**✅ 正确的导入：**
```java
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Email;
```

**❌ 错误的导入（不要使用）：**
```java
import javax.validation.constraints.NotNull;  // 错误！
import javax.validation.constraints.NotBlank; // 错误！
```

### 常用校验注解

| 注解 | 说明 | 示例 |
|------|------|------|
| `@NotNull` | 不能为null | `@NotNull(message = "ID不能为空")` |
| `@NotEmpty` | 不能为空集合/数组/字符串 | `@NotEmpty(message = "列表不能为空")` |
| `@NotBlank` | 不能为空白字符串 | `@NotBlank(message = "名称不能为空")` |
| `@Size` | 长度范围 | `@Size(min = 6, max = 20, message = "长度在6-20之间")` |
| `@Min` | 最小值 | `@Min(value = 0, message = "不能小于0")` |
| `@Max` | 最大值 | `@Max(value = 100, message = "不能大于100")` |
| `@Pattern` | 正则表达式 | `@Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")` |
| `@Email` | 邮箱格式 | `@Email(message = "邮箱格式不正确")` |

## 设计原则

1. **DTO 设计原则**
   * 只包含客户端传入的数据
   * 应包含严格的参数校验
   * 不应包含敏感数据
   * 可以有多个专用DTO，如CreateDTO, UpdateDTO

2. **VO 设计原则**
   * 只包含需要返回给客户端的数据
   * 可以聚合多个实体信息
   * 通常不包含校验规则
   * 可以对敏感数据进行脱敏处理
   * 可以额外包含计算/转换字段

3. **Query 设计原则**
   * 继承分页基类如 `PageQuery`
   * 包含所有可能的查询参数
   * 参数通常不做严格校验，允许为空
   * 对日期等特殊类型字段注意格式化

## 转换工具

推荐使用 `BeanUtils.copyProperties` 或 MapStruct 库进行对象转换：

```java
// 使用Spring BeanUtils
ProductCategoryVO vo = new ProductCategoryVO();
BeanUtils.copyProperties(entity, vo);

// 处理特殊字段
vo.setStatusName(entity.getStatus() == 1 ? "启用" : "禁用");
```

使用 MapStruct 定义映射接口：

```java
@Mapper(componentModel = "spring")
public interface ProductCategoryConverter {

    ProductCategoryVO toVO(ProductCategoryEntity entity);

    ProductCategoryEntity toEntity(ProductCategoryCreateDTO dto);

    void updateEntity(@MappingTarget ProductCategoryEntity entity, ProductCategoryUpdateDTO dto);
}
```

## 最佳实践

1. 严格区分DTO、VO和Entity，不要混用
2. DTO类应包含完整的参数校验
3. VO类可以包含额外的显示字段，如状态名称、格式化时间等
4. 避免在模型类中包含业务逻辑
5. 所有字段都应有明确的注释和Swagger文档注解
6. 对于复杂的模型转换，考虑使用MapStruct等工具
7. 列表查询通常返回分页对象，如 `Page<ProductCategoryVO>`
8. 日期字段使用 `@JsonFormat` 统一格式化
9. 敏感字段使用 `@JsonIgnore` 或自定义序列化器处理

## 分页查询最佳实践

1. **查询对象命名规范**
   * 使用 `实体名称+Pagination` 作为类名，如 `ProductPagination`
   * 明确表示该对象用于分页查询

2. **必要字段规范**
   * 继承 `Pagination` 类获取基础分页功能
   * 添加业务相关的查询字段
   * 对日期范围查询添加 `startTime` 和 `endTime` 字段
   * 对于通用搜索，添加 `searchType` 和 `searchKey` 字段

3. **使用方式**
   * 在 Controller 方法参数中直接使用 Pagination 对象
   * 在 Service 层通过 Pagination 构建 QueryWrapper
   * 使用 `setDataList()` 方法设置返回结果和总记录数

4. **高级查询**
   * 利用 `superQueryJson` 字段实现复杂条件查询
   * 系统支持通过 `moduleId` 和 `menuId` 进行权限过滤
