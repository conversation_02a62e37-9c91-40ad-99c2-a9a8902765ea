package com.xinghuo.common.database.model.dto;

import com.xinghuo.common.database.source.AbstractDbBase;
import lombok.Data;

import java.sql.ResultSet;

/**
 * 自定义模板参数对象
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Data
public class ModelDTO {

    public ModelDTO(ResultSet resultSet, String dbEncode){
        this.resultSet = resultSet;
        this.dbEncode = dbEncode;
    }

    public ModelDTO(ResultSet resultSet, AbstractDbBase dbBase){
        this.resultSet = resultSet;
    }

    /**
     * 结果集
     */
    private ResultSet resultSet;

    /**
     * 数据基类
     */
    private String dbEncode;

}
