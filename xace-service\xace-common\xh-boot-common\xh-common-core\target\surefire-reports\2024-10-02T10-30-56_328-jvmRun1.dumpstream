# Created at 2024-10-02T10:30:58.181
Corrupted channel by directly writing to native stream in forked JVM 1. Stream '[0.045s][warning][gc] Failed to reserve memory for new overflow mark stack with 4096 chunks and size 33554432B.'.

# Created at 2024-10-02T10:30:58.184
Corrupted channel by directly writing to native stream in forked JVM 1. Stream 'Error occurred during initialization of VM'.

# Created at 2024-10-02T10:30:58.209
Corrupted channel by directly writing to native stream in forked JVM 1. Stream 'Failed to allocate initial concurrent mark overflow mark stack.'.

