# xace-service 项目结构与包命名

## 基础包名

`com.xinghuo` 是所有代码的基础包名，所有代码都应该组织在这个包名下。

## 模块化原则

代码应根据功能划分到相应的子模块中。主要模块包括：

* `xh-admin` - 系统管理与业务主模块
* `xh-system` - 系统功能支持模块
* `xh-oauth` - 身份验证与授权模块
* `xh-file` - 文件管理模块
* `xh-workflow-engine` - 工作流引擎模块
* `xh-visualdev` - 可视化开发模块
* `xh-ext-*` - 扩展功能模块

每个模块应相对独立，通过接口进行交互，避免紧耦合依赖。

## 子模块包结构

在基础包名下，应按功能领域和层级组织代码，遵循以下结构：

* `com.xinghuo.[模块名].controller` - 控制器层，处理HTTP请求
* `com.xinghuo.[模块名].service` - 服务接口定义
* `com.xinghuo.[模块名].service.impl` - 服务接口实现
* `com.xinghuo.[模块名].mapper` - MyBatis映射接口
* `com.xinghuo.[模块名].entity` - 数据库实体
* `com.xinghuo.[模块名].model.dto` - 数据传输对象 (DTO)
* `com.xinghuo.[模块名].model.vo` - 视图对象 (VO)
* `com.xinghuo.[模块名].model.query` - 查询参数对象
* `com.xinghuo.[模块名].config` - 模块配置类
* `com.xinghuo.[模块名].util` - 工具类
* `com.xinghuo.[模块名].enums` - 枚举类
* `com.xinghuo.[模块名].constant` - 常量类
* `com.xinghuo.[模块名].exception` - 异常类

## 命名规则示例

| 类型 | 命名规则 | 示例 |
|------|----------|------|
| 控制器 | `[业务名]Controller` | `UserController`, `ProjectController` |
| 服务接口 | `[业务名]Service` | `UserService`, `ProjectService` |
| 服务实现 | `[业务名]ServiceImpl` | `UserServiceImpl`, `ProjectServiceImpl` |
| 数据访问 | `[业务名]Mapper` | `UserMapper`, `ProjectMapper` |
| 实体 | `[业务名]Entity` | `UserEntity`, `ProjectEntity` |
| DTO | `[业务名][操作]DTO` | `UserCreateDTO`, `ProjectUpdateDTO` |
| VO | `[业务名]VO` | `UserVO`, `ProjectVO` |
| 查询参数 | `[业务名]Query` | `UserQuery`, `ProjectQuery` |
| 异常类 | `[业务名]Exception` | `UserNotFoundException`, `ProjectOperationException` |

## 资源文件结构

* `src/main/resources/application.yml` - 应用主配置文件
* `src/main/resources/application-{profile}.yml` - 环境特定配置文件
* `src/main/resources/mapper/[模块名]` - MyBatis XML映射文件
* `src/main/resources/static` - 静态资源文件
* `src/main/resources/templates` - 模板文件（如果使用模板引擎）

## 多环境配置

* `application-dev.yml` - 开发环境配置
* `application-test.yml` - 测试环境配置
* `application-prod.yml` - 生产环境配置

## 代码分层原则

* **控制器层（Controller）**：只负责接收请求、参数校验和返回结果，不包含业务逻辑
* **服务层（Service）**：包含所有业务逻辑，事务控制在此层进行
* **数据访问层（Mapper）**：只负责数据库操作，不包含业务逻辑
* **实体层（Entity）**：与数据库表结构一一对应
* **模型层（Model）**：包含DTO、VO等数据传输对象
